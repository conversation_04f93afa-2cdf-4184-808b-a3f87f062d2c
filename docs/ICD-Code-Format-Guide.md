# ICD Code Format Guide

## Auto-Correction Feature

The `icd-both-proxy` endpoint automatically corrects common ICD code formatting mistakes.

## Common Format Issues & Solutions

### ✅ Auto-Corrected Formats

| User Input | Auto-Corrected To | Result | Description |
|------------|-------------------|---------|-------------|
| `A001` | `A00.1` | Cholera due to Vibrio cholerae 01, biovar eltor | Cholera infection |
| `A009` | `A00.9` | Cholera, unspecified | Unspecified cholera |
| `E111` | `E11.1` | Type 2 diabetes mellitus with ketoacidosis with coma | Diabetes with ketoacidosis |
| `E119` | `E11.9` | Type 2 diabetes mellitus without complications | Type 2 diabetes |
| `R509` | `R50.9` | Fever, unspecified | General fever |
| `Z511` | `Z51.1` | Encounter for antineoplastic chemotherapy | Cancer treatment |

### ✅ Already Correct Formats (No Correction Needed)

| Input | Version | Result |
|-------|---------|---------|
| `A00.1` | ICD-10 | Cholera due to Vibrio cholerae 01, biovar eltor |
| `E11.9` | ICD-10 | Type 2 diabetes mellitus without complications |
| `5A11` | ICD-11 | Type 2 diabetes mellitus |
| `MG26` | ICD-11 | Fever of other or unknown origin |
| `1A07` | ICD-11 | Typhoid fever |

## ICD Code Patterns

### ICD-10 Code Structure
- **Pattern**: `[Letter][2-3 digits].[1-2 digits/letters]`
- **Examples**: 
  - `A00.1` - Infectious diseases
  - `E11.9` - Endocrine disorders  
  - `R50.9` - Symptoms and signs
  - `Z51.1` - Factors influencing health status

### ICD-11 Code Structure
- **Pattern**: `[1-2 alphanumeric][1-3 alphanumeric].[optional extension]`
- **Examples**:
  - `5A11` - Type 2 diabetes mellitus
  - `MG26` - Fever of other or unknown origin
  - `1A07` - Typhoid fever
  - `KB60.2` - Neonatal diabetes mellitus

## API Usage Examples

### Test Auto-Correction

```bash
# These inputs will be auto-corrected:
curl "http://localhost:7071/api/icd-both-proxy?q=A001"  # → A00.1
curl "http://localhost:7071/api/icd-both-proxy?q=E111"  # → E11.1
curl "http://localhost:7071/api/icd-both-proxy?q=R509"  # → R50.9
```

### Version-Specific Searches

```bash
# ICD-10 only
curl "http://localhost:7071/api/icd-both-proxy?q=A001&version=icd10"

# ICD-11 only
curl "http://localhost:7071/api/icd-both-proxy?q=5A11&version=icd11"

# Both versions (default)
curl "http://localhost:7071/api/icd-both-proxy?q=diabetes&version=both"
```

### Disease Name Searches

```bash
# Search for all diabetes codes in both versions
curl "http://localhost:7071/api/icd-both-proxy?q=diabetes"

# Search for fever codes in ICD-10 only
curl "http://localhost:7071/api/icd-both-proxy?q=fever&version=icd10"

# Search for cancer codes in ICD-11 only
curl "http://localhost:7071/api/icd-both-proxy?q=cancer&version=icd11"
```

## Response Examples

### Successful Code Lookup (A001 → A00.1)

```json
{
  "error": false,
  "errorMessage": null,
  "resultChopped": false,
  "wordSuggestionsChopped": false,
  "guessType": 1,
  "uniqueSearchId": "",
  "words": null,
  "query": "A001",
  "version": "icd10",
  "destinationEntities": [
    {
      "id": "icd10-0",
      "title": "Cholera due to Vibrio cholerae 01, biovar eltor",
      "theCode": "A00.1",
      "definition": "",
      "type": "ICD-10-CM",
      "chapter": "",
      "score": 1.0,
      "isLeaf": true,
      "source": "ICD-10 (NLM)",
      "version": "ICD-10",
      "matchType": "exact_code"
    }
  ]
}
```

### Disease Search Results (Both Versions)

```json
{
  "error": false,
  "errorMessage": null,
  "resultChopped": false,
  "wordSuggestionsChopped": false,
  "guessType": 2,
  "uniqueSearchId": "",
  "words": null,
  "query": "diabetes",
  "version": "both",
  "destinationEntities": [
    {
      "id": "icd10-0",
      "title": "Diabetes insipidus",
      "theCode": "E23.2",
      "type": "ICD-10-CM",
      "version": "ICD-10",
      "source": "ICD-10 (NLM)"
    },
    {
      "id": "icd11-0", 
      "title": "Type 1 diabetes mellitus",
      "theCode": "5A10",
      "type": "ICD-11",
      "version": "ICD-11",
      "source": "ICD-11 (NLM)",
      "definition": "Diabetes mellitus type 1..."
    }
  ]
}
```

### No Results Found

```json
{
  "error": false,
  "errorMessage": "ICD code \"XYZ123\" not found in ICD-10 or ICD-11 database.",
  "resultChopped": false,
  "wordSuggestionsChopped": false,
  "guessType": 0,
  "uniqueSearchId": "",
  "words": null,
  "destinationEntities": []
}
```

## Integration Guide

### Frontend Integration

```javascript
// Search for ICD codes with auto-correction
async function searchIcdCode(code, version = 'both') {
  try {
    const response = await fetch(`/api/icd-both-proxy?q=${encodeURIComponent(code)}&version=${version}`)
    const data = await response.json()
    
    if (data.destinationEntities && data.destinationEntities.length > 0) {
      return data.destinationEntities.map(entity => ({
        code: entity.theCode,
        title: entity.title,
        version: entity.version,
        definition: entity.definition
      }))
    }
    
    return []
  } catch (error) {
    console.error('ICD search error:', error)
    return []
  }
}

// Usage examples
const cholera = await searchIcdCode('A001')  // Auto-corrects to A00.1
const diabetes = await searchIcdCode('diabetes', 'both')
const fever = await searchIcdCode('MG26', 'icd11')
```

### Backend Integration

```javascript
// Use in other Azure Functions
const { searchIcdCode } = require('./icd-both-proxy')

// In your medical record processing
async function validateDiagnosisCode(code) {
  const results = await searchIcdCode(code)
  return results.length > 0 ? results[0] : null
}
```

## Performance Considerations

- **Response Time**: ~1-3 seconds per search
- **Rate Limiting**: Built-in delays between API calls
- **Concurrent Requests**: Supports multiple simultaneous searches
- **Timeout**: 10 seconds per external API call

## Troubleshooting

### Common Issues

1. **"A001 returns no results"**
   - ✅ **Fixed**: Auto-correction now handles this
   - Check logs for "Auto-correcting A001 to A00.1" message

2. **Getting wrong ICD version**
   - Use `version=icd10` or `version=icd11` parameter
   - Check the `version` field in the response

3. **Too many results**
   - Use more specific search terms
   - Limit to one ICD version using `version` parameter

### Debug Logs

The API provides detailed logging:
```
ICD Search - Query: "A001", Version: both, Type: auto
Auto-correcting "A001" to "A00.1"
Original: "A001", Corrected: "A00.1"
Detected as ICD code: true, ICD-10 pattern: true, ICD-11 pattern: false
Trying ICD-10 API...
ICD-10 API: Found 1 total results, returning 1
Found exact match in ICD-10, returning immediately
```

## Data Sources

- **ICD-10-CM**: U.S. Clinical Modification of ICD-10 from NLM
- **ICD-11**: WHO International Classification of Diseases 11th Revision from NLM
- **Provider**: National Library of Medicine (NLM) Clinical Tables Service
- **Update Frequency**: Regularly updated by NLM
- **Coverage**: Complete ICD-10-CM and ICD-11 code sets
