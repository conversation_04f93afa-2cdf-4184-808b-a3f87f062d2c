# ICD Both Proxy API Documentation

## Overview

The `icd-both-proxy` endpoint provides comprehensive search functionality for both ICD-10 and ICD-11 medical codes. It supports direct code lookups, disease name searches, and automatic format correction for common ICD code formatting issues.

## Endpoint

```
GET /api/icd-both-proxy
```

## Features

- ✅ **Dual Version Support**: Searches both ICD-10 and ICD-11 databases
- ✅ **Auto-Format Correction**: Automatically fixes common formatting issues (e.g., A001 → A00.1)
- ✅ **Smart Detection**: Automatically detects ICD code version based on pattern
- ✅ **Exact Code Lookups**: Direct code-to-disease mapping
- ✅ **Disease Name Searches**: Disease-to-codes mapping across both versions
- ✅ **Version Control**: Manual version selection when needed
- ✅ **Deduplication**: Removes duplicate results across versions

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `q` | string | *required* | Search query (ICD code or disease name) |
| `version` | string | `both` | ICD version to search: `icd10`, `icd11`, or `both` |
| `type` | string | `auto` | Search type: `auto`, `code`, or `disease` |

## Auto-Format Correction

The API automatically corrects common ICD code formatting issues:

| Input Format | Auto-Corrected To | Example |
|--------------|-------------------|---------|
| `A001` | `A00.1` | A001 → A00.1 (Cholera) |
| `E111` | `E11.1` | E111 → E11.1 (Type 2 diabetes with ketoacidosis) |
| `R509` | `R50.9` | R509 → R50.9 (Fever, unspecified) |

## Code Pattern Detection

### ICD-10 Patterns
- **Format**: Letter + 2-3 digits + optional decimal + digits
- **Examples**: `A15.0`, `E11.1`, `R50.9`, `Z51.11`

### ICD-11 Patterns  
- **Format**: Alphanumeric 2-4 characters + optional decimal + alphanumeric
- **Examples**: `5A11`, `MG26`, `1A07`, `KB60.2`

## Usage Examples

### 1. ICD Code Lookups

#### ICD-10 Code Search
```bash
# Auto-corrected format
curl "http://localhost:7071/api/icd-both-proxy?q=A001"

# Correct format
curl "http://localhost:7071/api/icd-both-proxy?q=A00.1"

# Force ICD-10 version
curl "http://localhost:7071/api/icd-both-proxy?q=E111&version=icd10"
```

#### ICD-11 Code Search
```bash
# ICD-11 codes
curl "http://localhost:7071/api/icd-both-proxy?q=5A11"
curl "http://localhost:7071/api/icd-both-proxy?q=MG26"

# Force ICD-11 version
curl "http://localhost:7071/api/icd-both-proxy?q=5A11&version=icd11"
```

### 2. Disease Name Searches

#### Search Both Versions
```bash
curl "http://localhost:7071/api/icd-both-proxy?q=diabetes&version=both"
curl "http://localhost:7071/api/icd-both-proxy?q=fever&version=both"
curl "http://localhost:7071/api/icd-both-proxy?q=hypertension"
```

#### Version-Specific Disease Search
```bash
# ICD-10 only
curl "http://localhost:7071/api/icd-both-proxy?q=diabetes&version=icd10"

# ICD-11 only  
curl "http://localhost:7071/api/icd-both-proxy?q=diabetes&version=icd11"
```

## Response Format

### Successful Response

```json
{
  "error": false,
  "errorMessage": null,
  "resultChopped": false,
  "wordSuggestionsChopped": false,
  "guessType": 1,
  "uniqueSearchId": "",
  "words": null,
  "query": "A001",
  "version": "icd10",
  "destinationEntities": [
    {
      "id": "icd10-0",
      "title": "Cholera due to Vibrio cholerae 01, biovar eltor",
      "theCode": "A00.1",
      "definition": "",
      "type": "ICD-10-CM",
      "chapter": "",
      "score": 1.0,
      "isLeaf": true,
      "source": "ICD-10 (NLM)",
      "version": "ICD-10",
      "matchType": "exact_code"
    }
  ]
}
```

### Entity Object Fields

| Field | Description |
|-------|-------------|
| `id` | Unique identifier for the result |
| `title` | Disease/condition name |
| `theCode` | ICD code (corrected format) |
| `definition` | Detailed description (ICD-11 only) |
| `type` | Code type (ICD-10-CM or ICD-11) |
| `chapter` | ICD chapter number |
| `score` | Relevance score (1.0 for exact matches) |
| `isLeaf` | Whether this is a leaf node |
| `source` | Data source (ICD-10 (NLM) or ICD-11 (NLM)) |
| `version` | ICD version (ICD-10 or ICD-11) |
| `matchType` | Match type (exact_code or search_result) |

### Error Response

```json
{
  "error": false,
  "errorMessage": "ICD code \"XYZ123\" not found in ICD-10 or ICD-11 database.",
  "resultChopped": false,
  "wordSuggestionsChopped": false,
  "guessType": 0,
  "uniqueSearchId": "",
  "words": null,
  "destinationEntities": []
}
```

## Common Use Cases

### 1. Medical Coding Applications
```javascript
// Look up specific ICD codes
const response = await fetch('/api/icd-both-proxy?q=A001')
const result = await response.json()
console.log(result.destinationEntities[0].title) // "Cholera due to Vibrio cholerae 01, biovar eltor"
```

### 2. EHR Integration
```javascript
// Search for all diabetes codes across both versions
const response = await fetch('/api/icd-both-proxy?q=diabetes&version=both')
const codes = await response.json()
codes.destinationEntities.forEach(entity => {
  console.log(`${entity.version}: ${entity.theCode} - ${entity.title}`)
})
```

### 3. Code Validation
```javascript
// Validate and get details for a specific code
const response = await fetch('/api/icd-both-proxy?q=E111&version=icd10')
const result = await response.json()
if (result.destinationEntities.length > 0) {
  console.log('Valid ICD-10 code:', result.destinationEntities[0].theCode)
}
```

## API Data Sources

- **ICD-10**: NLM Clinical Tables API (`clinicaltables.nlm.nih.gov/api/icd10cm/v3/search`)
- **ICD-11**: NLM Clinical Tables API (`clinicaltables.nlm.nih.gov/api/icd11_codes/v3/search`)

## Performance Notes

- **Timeout**: 10 seconds per API call
- **Rate Limiting**: Built-in delays between requests
- **Caching**: No caching implemented (real-time searches)
- **Max Results**: 25 results per version (50 total for both versions)

## Error Handling

The API handles various error scenarios:

1. **Missing Query**: Returns 400 error for missing `q` parameter
2. **API Failures**: Graceful fallback and error reporting
3. **Invalid Codes**: Returns empty results with helpful error messages
4. **Network Issues**: Timeout handling and error details

## Implementation Details

### Auto-Correction Logic
```javascript
// Detects pattern like A001, E111, R509
if (/^[A-Z]\d{3}$/i.test(correctedQuery)) {
  const corrected = correctedQuery.substring(0, 3) + '.' + correctedQuery.substring(3)
  correctedQuery = corrected
}
```

### Version Detection
```javascript
const looksLikeIcd10 = /^[A-Z]\d{2}(\.\d+)?$/i.test(correctedQuery) // A15.0, E11.1
const looksLikeIcd11 = /^[A-Z0-9]{2,4}(\.[A-Z0-9]+)?$/i.test(correctedQuery) && !looksLikeIcd10 // 5A11, MG26
```

### Search Strategy
1. **Smart Version Detection**: Tries the most likely version first
2. **Exact Match Priority**: Returns exact code matches immediately
3. **Fallback Search**: Searches both versions if no exact match
4. **Result Deduplication**: Removes duplicate codes across versions

## Testing

To test the endpoint functionality:

1. **Start Azure Functions**: `func start`
2. **Test A001 Issue**: `curl "http://localhost:7071/api/icd-both-proxy?q=A001"`
3. **Expected Result**: Cholera diagnosis with code A00.1

## Troubleshooting

### Common Issues

1. **"No results for A001"**: 
   - ✅ **Fixed**: Auto-correction now handles this
   - The code gets corrected to A00.1 automatically

2. **Wrong ICD version results**:
   - Use the `version` parameter to force specific version
   - Check the `version` field in response to see which version was used

3. **Too many results**:
   - Use more specific search terms
   - Use the `version` parameter to limit to one ICD version

### Debug Information

The API logs detailed information:
- Original vs corrected query
- Code pattern detection results  
- API response counts
- Exact match detection

Check the Azure Functions logs for debugging information when troubleshooting issues.
