# Payment Tokenization Implementation

## Overview

This document describes the payment tokenization system implemented to secure sensitive payment data in the EMR system. The tokenization ensures that sensitive payment details are never stored in plain text and provides secure token-based payment processing.

## Architecture

### Components

1. **PaymentTokenizationService** - Core tokenization service
2. **PaymentTokenModel** - Model for payment tokens
3. **PaymentSessionTokenModel** - Model for session tokens
4. **PaymentTokenRepository** - Repository for token management
5. **Enhanced PaymentService** - Updated with tokenization support

### Security Features

- **Token Generation**: Secure HMAC-based token generation with timestamps
- **Token Validation**: Time-based token expiration and signature validation
- **Sensitive Data Protection**: Automatic tokenization of sensitive payment fields
- **Session Management**: Temporary session tokens for payment flows
- **Audit Trail**: Complete logging and tracking of token usage

## Implementation Details

### Sensitive Fields Tokenized

The following payment fields are automatically tokenized:
- `razorpayPaymentId`
- `razorpaySignature`
- `cardNumber`
- `cardHolderName`
- `cvv`
- `bankAccount`
- `ifscCode`
- `upiId`

### Token Types

#### 1. Payment Reference Token
- **Purpose**: Secure reference to payment data
- **Lifetime**: 24 hours
- **Usage**: Multiple uses allowed (up to 5 attempts)
- **Format**: Base64 encoded `tokenId:signature:timestamp`

#### 2. Session Token
- **Purpose**: Temporary payment session management
- **Lifetime**: 30 minutes
- **Usage**: Single session
- **Format**: Base64 encoded session data with HMAC

### API Changes

#### Create Payment Order
**Endpoint**: `POST /payments/create-order`

**Enhanced Response**:
```json
{
  "success": true,
  "data": {
    "orderId": "order_xyz123",
    "paymentId": "uuid-payment-id",
    "paymentReferenceToken": "base64-encoded-token",
    "sessionToken": "base64-encoded-session-token",
    "keyId": "rzp_test_key",
    "amount": 50000,
    "currency": "INR",
    "status": "created",
    "description": "Payment description"
  }
}
```

#### Verify Payment
**Endpoint**: `POST /payments/verify`

**Enhanced Request**:
```json
{
  "razorpay_order_id": "order_xyz123",
  "razorpay_payment_id": "pay_abc456",
  "razorpay_signature": "signature_hash",
  "paymentReferenceToken": "base64-encoded-token",
  "sessionToken": "base64-encoded-session-token",
  "userId": "user-id-optional"
}
```

#### Get Payment Details
**Endpoint**: `GET /payments/details?id=payment-id&paymentReferenceToken=token`

**Enhanced Parameters**:
- `id`: Payment ID (required)
- `paymentReferenceToken`: Token for secure access (optional)

### New Endpoints

#### 1. Validate Tokens
**Endpoint**: `POST /payments/tokens/validate`

**Request**:
```json
{
  "paymentReferenceToken": "base64-encoded-token",
  "sessionToken": "base64-encoded-session-token",
  "razorpayOrderId": "order_xyz123"
}
```

**Response**:
```json
{
  "valid": true,
  "message": "Tokens are valid"
}
```

#### 2. Cleanup Expired Tokens
**Endpoint**: `POST /payments/tokens/cleanup`

**Response**:
```json
{
  "success": true,
  "message": "Cleaned up 15 expired tokens",
  "cleanedCount": 15
}
```

## Configuration

### Environment Variables

Add the following to your Key Vault or environment variables:

```bash
PAYMENT_TOKENIZATION_KEY=your-32-byte-hex-key
```

If not provided, a key will be generated automatically (not recommended for production).

### Cosmos DB Containers

The implementation requires two new Cosmos DB containers:

1. **payment-tokens**
   - Partition Key: `/id`
   - Purpose: Store payment token metadata

2. **payment-sessions**
   - Partition Key: `/id`
   - Purpose: Store payment session tokens

## Security Considerations

### Token Security
- Tokens use HMAC-SHA256 for integrity
- Timestamps prevent replay attacks
- Automatic expiration prevents long-term exposure
- Usage limits prevent abuse

### Data Protection
- Sensitive payment data is tokenized before storage
- Original sensitive data is never persisted
- Token validation ensures authorized access
- Audit trails track all token operations

### Best Practices
- Always validate tokens before processing payments
- Implement proper error handling for token failures
- Regular cleanup of expired tokens
- Monitor token usage patterns for anomalies

## Usage Examples

### Frontend Integration

```javascript
// Create payment order
const orderResponse = await fetch('/payments/create-order', {
  method: 'POST',
  body: JSON.stringify(paymentData)
});

const { paymentReferenceToken, sessionToken, orderId } = orderResponse.data;

// Verify payment with tokens
const verifyResponse = await fetch('/payments/verify', {
  method: 'POST',
  body: JSON.stringify({
    razorpay_order_id: orderId,
    razorpay_payment_id: paymentId,
    razorpay_signature: signature,
    paymentReferenceToken,
    sessionToken
  })
});
```

### Backend Token Validation

```javascript
// Validate tokens before processing
const isValid = await paymentService.validatePaymentTokens(
  paymentReferenceToken,
  sessionToken,
  razorpayOrderId
);

if (!isValid) {
  throw new Error('Invalid payment tokens');
}
```

## Monitoring and Maintenance

### Regular Tasks
1. **Token Cleanup**: Run cleanup endpoint regularly to remove expired tokens
2. **Usage Monitoring**: Monitor token usage patterns and failures
3. **Security Audits**: Regular review of token generation and validation logs

### Alerts
- High token validation failure rates
- Unusual token usage patterns
- Token cleanup failures
- Tokenization service errors

## Migration Guide

For existing payments, the system maintains backward compatibility:
- Existing payments without tokens continue to work
- New payments automatically use tokenization
- Gradual migration of sensitive data can be implemented
