# ===========================================
# EMR-MS Azure Pipeline
# Builds and pushes Docker image to different registries based on branch
# main branch    → ermdevcontainer (DEV)
# staging branch → emrstagingcontainer (STAGING)
# ===========================================

trigger:
  - main
  - staging
  - develop
  - test

pool:
  vmImage: 'ubuntu-latest'

steps:
  - script: echo "Building EMR-MS for branch $(Build.SourceBranchName)"
    displayName: 'Initialize build'

  # Build and Push to DEV registry (main, develop, test branches)
  - task: Docker@2
    displayName: 'Build and Push to DEV Registry'
    condition: ne(variables['Build.SourceBranchName'], 'staging')
    inputs:
      containerRegistry: 'ermdevcontainer'
      repository: 'emr-v01/emr-ms'
      command: 'buildAndPush'
      Dockerfile: 'Dockerfile'
      tags: |
        $(Build.SourceBranchName).$(Build.BuildId)
        latest

  # Build and Push to STAGING registry (staging branch only)
  - task: Docker@2
    displayName: 'Build and Push to STAGING Registry'
    condition: eq(variables['Build.SourceBranchName'], 'staging')
    inputs:
      containerRegistry: 'emrstagingcontainer'
      repository: 'emr-v01/emr-ms'
      command: 'buildAndPush'
      Dockerfile: 'Dockerfile'
      tags: |
        $(Build.SourceBranchName).$(Build.BuildId)
        latest

  # Publish build artifacts
  - task: PublishBuildArtifacts@1
    displayName: 'Publish build artifacts'
    inputs:
      ArtifactName: 'emr-ms'
      publishLocation: 'Container'
      PathtoPublish: '.'