#!/bin/bash
set -e

# =============================================================================
# EMR-TEST – SAFE & CORRECT NETWORK SECURITY SETUP
# =============================================================================

RESOURCE_GROUP="EMR-TEST"
LOCATION="centralindia"

VNET_NAME="EMR-TEST-VNET"
VNET_ADDRESS_SPACE="*********/16"

SUBNET_FUNCTION="snet-functionapp"
SUBNET_FUNCTION_PREFIX="*********/24"

SUBNET_APIM="snet-apim"
SUBNET_APIM_PREFIX="*********/24"

SUBNET_PE="snet-privateendpoints"
SUBNET_PE_PREFIX="**********/24"

NSG_APP="nsg-emr-test-app"

FUNCTION_APP="EMR-MS-TEST"
APIM_NAME="emr-ms-test-apim"
COSMOSDB="emrdbtest"
STORAGE="emrtest8ffe"
KEYVAULT="vault-emrstaging"

echo "🚀 Starting EMR-TEST SAFE network deployment..."

# =============================================================================
# 1. VNET
# =============================================================================
az network vnet create \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --name $VNET_NAME \
  --address-prefix $VNET_ADDRESS_SPACE

# =============================================================================
# 2. NSG (Function + APIM)
# =============================================================================
az network nsg create \
  --resource-group $RESOURCE_GROUP \
  --location $LOCATION \
  --name $NSG_APP

# Allow VNet traffic
az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-VNet \
  --priority 100 \
  --direction Outbound \
  --access Allow \
  --protocol "*" \
  --source-address-prefixes VirtualNetwork \
  --destination-address-prefixes VirtualNetwork \
  --destination-port-ranges "*"

# Azure platform traffic
az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-AzureCloud \
  --priority 110 \
  --direction Outbound \
  --access Allow \
  --protocol Tcp \
  --destination-address-prefixes AzureCloud \
  --destination-port-ranges 443

az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-AzureMonitor \
  --priority 120 \
  --direction Outbound \
  --access Allow \
  --protocol Tcp \
  --destination-address-prefixes AzureMonitor \
  --destination-port-ranges 443

az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-AzureAD \
  --priority 130 \
  --direction Outbound \
  --access Allow \
  --protocol Tcp \
  --destination-address-prefixes AzureActiveDirectory \
  --destination-port-ranges 443

az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-Storage \
  --priority 140 \
  --direction Outbound \
  --access Allow \
  --protocol Tcp \
  --destination-address-prefixes Storage \
  --destination-port-ranges 443

az network nsg rule create \
  --resource-group $RESOURCE_GROUP \
  --nsg-name $NSG_APP \
  --name Allow-DNS \
  --priority 150 \
  --direction Outbound \
  --access Allow \
  --protocol Udp \
  --destination-address-prefixes ************* \
  --destination-port-ranges 53

# =============================================================================
# 3. SUBNETS
# =============================================================================
az network vnet subnet create \
  --resource-group $RESOURCE_GROUP \
  --vnet-name $VNET_NAME \
  --name $SUBNET_FUNCTION \
  --address-prefix $SUBNET_FUNCTION_PREFIX \
  --network-security-group $NSG_APP \
  --delegations Microsoft.Web/serverFarms

az network vnet subnet create \
  --resource-group $RESOURCE_GROUP \
  --vnet-name $VNET_NAME \
  --name $SUBNET_APIM \
  --address-prefix $SUBNET_APIM_PREFIX \
  --network-security-group $NSG_APP

az network vnet subnet create \
  --resource-group $RESOURCE_GROUP \
  --vnet-name $VNET_NAME \
  --name $SUBNET_PE \
  --address-prefix $SUBNET_PE_PREFIX \
  --disable-private-endpoint-network-policies true

# =============================================================================
# 4. FUNCTION APP VNET INTEGRATION
# =============================================================================
az functionapp vnet-integration remove \
  --name $FUNCTION_APP \
  --resource-group $RESOURCE_GROUP || true

az functionapp vnet-integration add \
  --name $FUNCTION_APP \
  --resource-group $RESOURCE_GROUP \
  --vnet $VNET_NAME \
  --subnet $SUBNET_FUNCTION

az functionapp config appsettings set \
  --name $FUNCTION_APP \
  --resource-group $RESOURCE_GROUP \
  --settings WEBSITE_VNET_ROUTE_ALL=1

# =============================================================================
# 5. PRIVATE DNS ZONES
# =============================================================================
zones=(
  privatelink.documents.azure.com
  privatelink.blob.core.windows.net
  privatelink.vaultcore.azure.net
)

for z in "${zones[@]}"; do
  az network private-dns zone create \
    --resource-group $RESOURCE_GROUP \
    --name $z || true

  az network private-dns link vnet create \
    --resource-group $RESOURCE_GROUP \
    --zone-name $z \
    --name link-$z \
    --virtual-network $VNET_NAME \
    --registration-enabled false || true
done

SUBNET_ID=$(az network vnet subnet show \
  --resource-group $RESOURCE_GROUP \
  --vnet-name $VNET_NAME \
  --name $SUBNET_PE \
  --query id -o tsv)

# =============================================================================
# 6. COSMOS DB PRIVATE ENDPOINT + DNS (CRITICAL FIX)
# =============================================================================
COSMOS_ID=$(az cosmosdb show -g $RESOURCE_GROUP -n $COSMOSDB --query id -o tsv)

az network private-endpoint create \
  --resource-group $RESOURCE_GROUP \
  --name pe-cosmos-test \
  --location $LOCATION \
  --subnet $SUBNET_ID \
  --private-connection-resource-id $COSMOS_ID \
  --group-id Sql \
  --connection-name cosmos-conn

az network private-endpoint dns-zone-group create \
  --resource-group $RESOURCE_GROUP \
  --endpoint-name pe-cosmos-test \
  --name zg-cosmos \
  --private-dns-zone privatelink.documents.azure.com \
  --zone-name cosmos

az cosmosdb update \
  --name $COSMOSDB \
  --resource-group $RESOURCE_GROUP \
  --public-network-access Disabled

# =============================================================================
# 7. STORAGE PRIVATE ENDPOINT + DNS
# =============================================================================
STORAGE_ID=$(az storage account show -g $RESOURCE_GROUP -n $STORAGE --query id -o tsv)

az network private-endpoint create \
  --resource-group $RESOURCE_GROUP \
  --name pe-storage-test \
  --location $LOCATION \
  --subnet $SUBNET_ID \
  --private-connection-resource-id $STORAGE_ID \
  --group-id blob \
  --connection-name storage-conn

az network private-endpoint dns-zone-group create \
  --resource-group $RESOURCE_GROUP \
  --endpoint-name pe-storage-test \
  --name zg-storage \
  --private-dns-zone privatelink.blob.core.windows.net \
  --zone-name storage

# =============================================================================
# 8. KEY VAULT PRIVATE ENDPOINT + DNS
# =============================================================================
KV_ID=$(az keyvault show -g $RESOURCE_GROUP -n $KEYVAULT --query id -o tsv)

az network private-endpoint create \
  --resource-group $RESOURCE_GROUP \
  --name pe-kv-test \
  --location $LOCATION \
  --subnet $SUBNET_ID \
  --private-connection-resource-id $KV_ID \
  --group-id vault \
  --connection-name kv-conn

az network private-endpoint dns-zone-group create \
  --resource-group $RESOURCE_GROUP \
  --endpoint-name pe-kv-test \
  --name zg-kv \
  --private-dns-zone privatelink.vaultcore.azure.net \
  --zone-name keyvault

echo "✅ EMR-TEST network security setup COMPLETE"
echo "⏳ Wait 5–10 minutes, then test from Kudu:"
echo "nslookup ${COSMOSDB}.documents.azure.com"
echo "nslookup ${STORAGE}.blob.core.windows.net"
echo "nslookup ${KEYVAULT}.vault.azure.net"
