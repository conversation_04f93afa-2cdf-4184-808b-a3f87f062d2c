# Trivy Security Scanning Operations Guide

## 1. Scan Inventory & Governance

### Vulnerability Database
- **Source**: <PERSON>vy automatically downloads the latest vulnerability database from Aqua Security's GitHub releases.
- **Update Frequency**: The database is updated at the start of every scan execution in the CI/CD pipeline.
- **Versioning**: The database version used for each scan is logged in the GitHub Actions execution logs.

### Report Retention Policy
- **Retention Period**: Scan reports and SBOMs are retained as GitHub Actions Artifacts for **90 days**.
- **Storage**:
    - **SARIF Reports**: Uploaded to GitHub Security tab (Code Scanning Alerts) for indefinite retention until resolved.
    - **Raw Artifacts**: Stored in GitHub Actions run artifacts.

### Accessing Stored Artifacts
To download these 90-day retained files:
1.  Go to the **Actions** tab in your GitHub repository.
2.  Click on the specific workflow run (e.g., "Trivy Security Scan").
3.  Scroll down to the **Artifacts** section at the bottom of the page.
4.  You will see files named with the unique Commit SHA (e.g., `sbom-a1b2c3d`), ensuring every report is traceable to a specific code version.

### Naming Conventions
To ensure traceability and easy retrieval, the following naming conventions are enforced:

| Artifact Type | Naming Pattern | Example |
| :--- | :--- | :--- |
| **SBOM** | `sbom-${COMMIT_SHA}.json` | `sbom-a1b2c3d.json` |
| **FS Scan Report** | `trivy-fs-results.sarif` | `trivy-fs-results.sarif` |
| **Image Scan Report** | `trivy-image-results.sarif` | `trivy-image-results.sarif` |
| **Docker Image Tag** | `${BRANCH_NAME}-${COMMIT_SHA}` | `main-a1b2c3d` |

## 2. Troubleshooting Guide

### Common Failures

#### Database Update Errors
- **Symptom**: `Fatal error: failed to download vulnerability DB`
- **Cause**: Network connectivity issues or GitHub API rate limiting.
- **Resolution**:
    1. Retry the GitHub Actions job.
    2. If persistent, check [Aqua Security Status](https://status.aquasec.com/).

#### Registry Access Issues
- **Symptom**: `401 Unauthorized` or `denied: requested access to the resource is denied` during Docker login or pull.
- **Cause**: Expired Service Principal credentials or incorrect secrets.
- **Resolution**:
    1. Verify the correct secrets are set in GitHub Settings based on the environment:
        - **Dev**: `ACR_USERNAME_DEV`, `ACR_PASSWORD_DEV`
        - **Staging**: `ACR_USERNAME_STAGING`, `ACR_PASSWORD_STAGING`
    2. Check if the Azure Service Principal has expired and regenerate if necessary.
    3. Ensure the Service Principal has `AcrPull` (and `AcrPush` if building) permissions.

#### Workflow Failures
- **Symptom**: Job fails at "Build Docker Image" step.
- **Cause**: Dockerfile errors or missing build context files.
- **Resolution**:
    1. Check the "Build Docker Image" step logs.
    2. Verify the `Dockerfile` builds successfully locally: `docker build .`

#### False Positives
- **Symptom**: Trivy reports a vulnerability that is not exploitable or is a known acceptable risk.
- **Resolution**:
    1. Create or edit the `.trivyignore` file in the repository root.
    2. Add the CVE ID and a comment explaining the reason.
    ```text
    # .trivyignore
    # False positive: dependency is only used in dev and not present in production image
    CVE-2023-12345
    ```

## 3. Traceability
Every scan is linked to a specific Git Commit SHA.
- **From Artifact to Code**: The artifact name contains the SHA.
- **From Image to Code**: The image tag contains the SHA.
- **From Alert to Code**: GitHub Security alerts link directly to the vulnerable line of code (for FS scans) or the image layer.

## 4. Workflow Execution & Effects

### Triggers
The security scan runs automatically in two scenarios:
1.  **Pull Request**: When you open or update a PR targeting `main`, `staging`, or `develop`.
    - *Goal*: Catch vulnerabilities *before* they merge.
2.  **Merge (Push)**: When code is merged (pushed) into these branches.
    - *Goal*: Ensure the deployed artifact (Docker image) is secure and generate the final SBOM.

### What Happens During a Scan?
1.  **Filesystem Scan**: Trivy scans your source code and `package.json`/`package-lock.json` for vulnerabilities and configuration issues (IaC).
2.  **SBOM Generation**: A Software Bill of Materials (`sbom-*.json`) is created, listing all dependencies.
3.  **Docker Build**: The application is built into a Docker container.
4.  **Image Scan**: Trivy scans the final Docker image for OS-level vulnerabilities (e.g., in Alpine/Debian base images).

### The "Effects" (Outputs)
- **GitHub Security Tab**: Vulnerabilities will appear under "Security" > "Code scanning alerts". You can manage/dismiss them there.
- **Build Artifacts**: You can download the `sbom-*.json` and `.sarif` reports from the "Summary" page of the GitHub Action run.
- **Build Status**:
    - Currently, the workflow is in **Audit Mode**. It will **NOT fail the build** even if critical vulnerabilities are found. This allows you to baseline your security posture without blocking development.
    - *To enforce gating (fail build on error)*: Add `exit-code: '1'` to the Trivy steps in the YAML file.
