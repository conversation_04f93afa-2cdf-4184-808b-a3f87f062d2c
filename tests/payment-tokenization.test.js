const paymentTokenizationService = require('../src/services/payment-tokenization-service')
const { PaymentTokenModel, PaymentSessionTokenModel } = require('../src/models/payment-token-model')

describe('Payment Tokenization', () => {
  beforeAll(async () => {
    // Initialize the tokenization service
    await paymentTokenizationService.initialize()
  })

  describe('Token Generation and Validation', () => {
    test('should generate and validate payment token', async () => {
      const sensitiveData = 'pay_test123456789'
      
      // Generate token
      const token = await paymentTokenizationService.generateToken(sensitiveData)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      
      // Validate token
      const isValid = await paymentTokenizationService.validateToken(token, sensitiveData)
      expect(isValid).toBe(true)
    })

    test('should reject invalid token', async () => {
      const sensitiveData = 'pay_test123456789'
      const invalidToken = 'invalid_token_data'
      
      const isValid = await paymentTokenizationService.validateToken(invalidToken, sensitiveData)
      expect(isValid).toBe(false)
    })

    test('should reject token with wrong data', async () => {
      const sensitiveData = 'pay_test123456789'
      const wrongData = 'pay_different_data'
      
      const token = await paymentTokenizationService.generateToken(sensitiveData)
      const isValid = await paymentTokenizationService.validateToken(token, wrongData)
      expect(isValid).toBe(false)
    })
  })

  describe('Payment Data Tokenization', () => {
    test('should tokenize sensitive payment fields', async () => {
      const paymentData = {
        id: 'payment_123',
        amount: 50000,
        razorpayPaymentId: 'pay_sensitive123',
        razorpaySignature: 'signature_sensitive456',
        cardNumber: '****************',
        organizationId: 'org_123'
      }

      const tokenizedData = await paymentTokenizationService.tokenizePaymentData(paymentData)
      
      // Check that sensitive fields are tokenized
      expect(tokenizedData.razorpayPaymentId).not.toBe(paymentData.razorpayPaymentId)
      expect(tokenizedData.razorpaySignature).not.toBe(paymentData.razorpaySignature)
      expect(tokenizedData.cardNumber).not.toBe(paymentData.cardNumber)
      
      // Check that non-sensitive fields remain unchanged
      expect(tokenizedData.id).toBe(paymentData.id)
      expect(tokenizedData.amount).toBe(paymentData.amount)
      expect(tokenizedData.organizationId).toBe(paymentData.organizationId)
      
      // Check tokenization flags
      expect(tokenizedData.razorpayPaymentId_tokenized).toBe(true)
      expect(tokenizedData.razorpaySignature_tokenized).toBe(true)
      expect(tokenizedData.cardNumber_tokenized).toBe(true)
    })
  })

  describe('Payment Reference Token', () => {
    test('should create and validate payment reference token', async () => {
      const paymentReference = {
        paymentId: 'payment_123',
        organizationId: 'org_456',
        amount: 50000,
        timestamp: Date.now()
      }

      const token = await paymentTokenizationService.createPaymentReferenceToken(paymentReference)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')

      const isValid = await paymentTokenizationService.validatePaymentReferenceToken(token, paymentReference)
      expect(isValid).toBe(true)
    })
  })

  describe('Session Token', () => {
    test('should generate payment session token', async () => {
      const sessionData = {
        paymentId: 'payment_123',
        userId: 'user_456',
        organizationId: 'org_789'
      }

      const sessionToken = await paymentTokenizationService.generatePaymentSessionToken(sessionData)
      expect(sessionToken).toBeDefined()
      expect(typeof sessionToken).toBe('string')
    })
  })
})

describe('Payment Token Model', () => {
  test('should create valid payment token model', () => {
    const tokenData = {
      paymentId: 'payment_123',
      organizationId: 'org_456',
      paymentReferenceToken: 'token_abc123',
      amount: 50000,
      currency: 'INR',
      paymentType: 'consultation',
      status: 'created'
    }

    const paymentToken = new PaymentTokenModel(tokenData)
    expect(paymentToken.id).toBeDefined()
    expect(paymentToken.paymentId).toBe(tokenData.paymentId)
    expect(paymentToken.organizationId).toBe(tokenData.organizationId)
    expect(paymentToken.isValidForUse()).toBe(true)
  })

  test('should validate required fields', () => {
    const invalidTokenData = {
      // Missing required fields
      amount: 50000
    }

    expect(() => {
      new PaymentTokenModel(invalidTokenData)
    }).toThrow('Payment token validation failed')
  })

  test('should handle token expiration', () => {
    const tokenData = {
      paymentId: 'payment_123',
      organizationId: 'org_456',
      paymentReferenceToken: 'token_abc123',
      amount: 50000,
      tokenExpiresAt: new Date(Date.now() - 1000).toISOString() // Expired 1 second ago
    }

    const paymentToken = new PaymentTokenModel(tokenData)
    expect(paymentToken.isExpired()).toBe(true)
    expect(paymentToken.isValidForUse()).toBe(false)
  })

  test('should handle token usage limits', () => {
    const tokenData = {
      paymentId: 'payment_123',
      organizationId: 'org_456',
      paymentReferenceToken: 'token_abc123',
      amount: 50000,
      maxTokenUsage: 2,
      tokenUsageCount: 1
    }

    const paymentToken = new PaymentTokenModel(tokenData)
    expect(paymentToken.isValidForUse()).toBe(true)
    
    // Mark as used
    paymentToken.markAsUsed('user_123')
    expect(paymentToken.tokenUsageCount).toBe(2)
    expect(paymentToken.isValidForUse()).toBe(false) // Should be revoked after max usage
    expect(paymentToken.tokenRevoked).toBe(true)
  })
})

describe('Payment Session Token Model', () => {
  test('should create valid session token model', () => {
    const sessionData = {
      paymentId: 'payment_123',
      userId: 'user_456',
      organizationId: 'org_789',
      sessionToken: 'session_token_abc'
    }

    const sessionToken = new PaymentSessionTokenModel(sessionData)
    expect(sessionToken.id).toBeDefined()
    expect(sessionToken.sessionId).toBeDefined()
    expect(sessionToken.paymentId).toBe(sessionData.paymentId)
    expect(sessionToken.isActive).toBe(true)
    expect(sessionToken.isExpired()).toBe(false)
  })

  test('should handle session completion', () => {
    const sessionData = {
      paymentId: 'payment_123',
      userId: 'user_456',
      organizationId: 'org_789',
      sessionToken: 'session_token_abc'
    }

    const sessionToken = new PaymentSessionTokenModel(sessionData)
    sessionToken.markCompleted()
    
    expect(sessionToken.isActive).toBe(false)
    expect(sessionToken.completedAt).toBeDefined()
  })
})
