{"annotations": {"list": []}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "sum(rate(traces_service_graph_request_total{client=\"$service\"}[$__rate_interval])) by (server)", "legendFormat": "{{server}}", "range": true, "refId": "A"}], "title": "Request Rate by Downstream Service", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(traces_service_graph_request_server_seconds_bucket{client=\"$service\"}[$__rate_interval])) by (le, server)) * 1000", "legendFormat": "p95 - {{server}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(traces_service_graph_request_server_seconds_bucket{client=\"$service\"}[$__rate_interval])) by (le, server)) * 1000", "legendFormat": "p50 - {{server}}", "range": true, "refId": "B"}], "title": "Response Time (p50, p95)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.01}, {"color": "red", "value": 0.05}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 8}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "sum(rate(traces_service_graph_request_failed_total{client=\"$service\"}[$__rate_interval])) / sum(rate(traces_service_graph_request_total{client=\"$service\"}[$__rate_interval]))", "legendFormat": "Error Rate", "range": true, "refId": "A"}], "title": "Error Rate", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 8}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "sum(rate(traces_service_graph_request_total{client=\"$service\"}[$__rate_interval]))", "legendFormat": "Request Rate", "range": true, "refId": "A"}], "title": "Total Request Rate", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 1000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 8}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(traces_service_graph_request_server_seconds_bucket{client=\"$service\"}[$__rate_interval])) by (le)) * 1000", "legendFormat": "p95 Latency", "range": true, "refId": "A"}], "title": "P95 Latency", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 8}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus_datasource}"}, "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(traces_service_graph_request_server_seconds_bucket{client=\"$service\"}[$__rate_interval])) by (le)) * 1000", "legendFormat": "p50 Latency", "range": true, "refId": "A"}], "title": "P50 Latency", "type": "stat"}, {"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 12}, "id": 7, "options": {"nodes": {}}, "targets": [{"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "queryType": "serviceMap", "refId": "A"}], "title": "Service Graph", "type": "nodeGraph"}, {"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "unit", "value": "ns"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 22}, "id": 8, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Start time"}]}, "targets": [{"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "limit": 20, "queryType": "traceqlSearch", "refId": "A", "tableType": "traces", "filters": [{"id": "service-name", "operator": "=", "scope": "resource", "tag": "service.name", "value": ["${service}"], "valueType": "string"}]}], "title": "Recent Traces", "type": "table"}, {"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "unit", "value": "ns"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 30}, "id": 9, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Start time"}]}, "targets": [{"datasource": {"type": "tempo", "uid": "${tempo_datasource}"}, "limit": 20, "queryType": "traceqlSearch", "refId": "A", "tableType": "traces", "filters": [{"id": "service-name", "operator": "=", "scope": "resource", "tag": "service.name", "value": ["${service}"], "valueType": "string"}, {"id": "http-status", "operator": ">=", "scope": "span", "tag": "http.status_code", "value": ["400"], "valueType": "int"}]}], "title": "Error Traces (HTTP 4xx/5xx)", "type": "table"}], "refresh": "30s", "schemaVersion": 38, "tags": ["emr", "microservice", "traces"], "templating": {"list": [{"current": {"selected": true, "text": "grafanacloud-suhaila1-prom", "value": "grafanacloud-suhaila1-prom"}, "hide": 0, "includeAll": false, "label": "Prometheus", "multi": false, "name": "prometheus_datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "grafanacloud-suhaila1-traces", "value": "grafanacloud-suhaila1-traces"}, "hide": 0, "includeAll": false, "label": "Tempo", "multi": false, "name": "tempo_datasource", "options": [], "query": "tempo", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "emr-microservice-dev", "value": "emr-microservice-dev"}, "hide": 0, "includeAll": false, "multi": false, "name": "service", "options": [{"selected": true, "text": "emr-microservice-dev", "value": "emr-microservice-dev"}, {"selected": false, "text": "emr-microservice-qa", "value": "emr-microservice-qa"}, {"selected": false, "text": "emr-microservice-prod", "value": "emr-microservice-prod"}], "query": "emr-microservice-dev,emr-microservice-qa,emr-microservice-prod", "queryValue": "", "skipUrlSync": false, "type": "custom", "label": "Service"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "EMR Microservice Observability", "uid": "emr-microservice-obs", "version": 1, "weekStart": ""}