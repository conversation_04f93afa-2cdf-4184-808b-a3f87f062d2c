# Arcaai EHR Application - Complete Architecture Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Technology Stack](#technology-stack)
3. [Architecture Patterns](#architecture-patterns)
4. [Application Structure](#application-structure)
5. [Data Layer](#data-layer)
6. [Business Logic Layer](#business-logic-layer)
7. [API Layer](#api-layer)
8. [Security & Authentication](#security--authentication)
9. [External Integrations](#external-integrations)
10. [Development Environment](#development-environment)
11. [CI/CD Pipeline](#cicd-pipeline)
12. [Deployment Architecture](#deployment-architecture)
13. [Backup Strategy](#backup-strategy)
14. [Conclusion](#conclusion)

---

## System Overview

The Arcaai EHR (Electronic Health Records) application is a comprehensive healthcare management system built as a serverless microservice architecture on Microsoft Azure. The system manages patient records, doctor profiles, appointments, lab tests, prescriptions, and lifestyle data with integrated payment processing and ABDM (Ayushman Bharat Digital Mission) compliance.

### Key Features
- **Patient Management**: Complete patient lifecycle management with medical history
- **Doctor Portal**: Doctor profiles, EMR customization, and consultation management
- **Appointment System**: Scheduling and queue management
- **Lab Management**: Test ordering, report processing with OCR capabilities
- **Prescription System**: Medicine management and prescription packages
- **Payment Integration**: Razorpay payment gateway integration
- **ABDM Integration**: ABHA number generation and verification
- **Lifestyle Tracking**: Patient lifestyle and nutrition monitoring
- **AI-Powered**: OpenAI integration for medical summaries and ambient listening

---

## Technology Stack

### Backend Framework
- **Runtime**: Node.js 20
- **Framework**: Azure Functions v4 (Serverless)
- **Language**: JavaScript (ES6+)

### Database & Storage
- **Primary Database**: Azure Cosmos DB (NoSQL)
- **Caching**: Azure Redis Cache
- **File Storage**: Azure Blob Storage
- **Search**: Cosmos DB SQL API with custom indexing

### Cloud Platform
- **Platform**: Microsoft Azure
- **Compute**: Azure Functions (Consumption Plan)
- **API Management**: Azure API Management (APIM)
- **Container Registry**: Azure Container Registry
- **Identity**: Azure Active Directory B2C

### External Services
- **AI/ML**: Azure OpenAI Service (GPT-4)
- **Payment**: Razorpay Payment Gateway
- **Email**: Custom SMTP service
- **OCR**: Custom OCR service for lab reports
- **Healthcare Standards**: ICD-11, SNOMED CT, LOINC

### Development Tools
- **IDE**: Visual Studio Code
- **Version Control**: Git
- **Package Manager**: npm
- **Containerization**: Docker
- **CI/CD**: Azure DevOps Pipelines

---

## Architecture Patterns

### 1. Clean Architecture
The application follows clean architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Azure Functions │  │   API Gateway   │  │  Middleware  │ │
│  │   (Controllers)  │  │     (APIM)      │  │ (Auth/CORS)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │    Handlers     │  │    Services     │  │   Utilities  │ │
│  │  (Use Cases)    │  │ (Domain Logic)  │  │   (Helpers)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      Data Access Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Repositories   │  │     Queries     │  │    Models    │ │
│  │ (CRUD Operations)│  │ (Read Operations)│  │ (Data Schema)│ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Cosmos DB     │  │   Redis Cache   │  │ Blob Storage │ │
│  │   (Database)    │  │   (Caching)     │  │ (Files/Docs) │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. Repository Pattern
- **Repositories**: Handle all database operations and data persistence
- **Queries**: Contains SQL queries that are used by repositories 
- **Models**: Data schema definitions and validation

### 3. Service Layer Pattern
- **Services**: Business logic and domain operations
- **Handlers**: Request/response processing and validation
- **Utilities**: Shared helper functions and utilities


---

## Application Structure

```
src/
├── auth/                    # Authentication utilities
├── common/                  # Shared utilities and constants
│   ├── auth-message.js     # Authentication messages
│   ├── constant.js         # Application constants
│   ├── helper.js           # Common helper functions
│   ├── permissions.js      # Permission definitions
│   ├── roles.js           # Role definitions
│   └── user-validation.js  # User validation logic
├── cosmosDbContext/        # Database context and configuration
│   └── comosdb-context.js  # Cosmos DB client and utilities
├── functions/              # Azure Functions (API endpoints)
│   ├── index.js           # Main function app configuration
│   ├── auth.js            # Authentication endpoints
│   ├── patient.js         # Patient management endpoints
│   ├── doctor.js          # Doctor management endpoints
│   ├── appointment.js     # Appointment endpoints
│   ├── payment.js         # Payment endpoints
│   ├── abdm.js            # ABDM integration endpoints
│   └── [other-functions]  # Additional domain-specific endpoints
├── handlers/               # Business logic handlers
│   ├── patient-handler.js  # Patient business logic
│   ├── doctor-handler.js   # Doctor business logic
│   ├── payment-handler.js  # Payment processing logic
│   └── [other-handlers]    # Additional handlers
├── services/               # Domain services
│   ├── patient-service.js  # Patient domain service
│   ├── doctor-service.js   # Doctor domain service
│   ├── payment-service.js  # Payment service
│   ├── openai-service.js   # AI integration service
│   ├── b2c-service.js      # Azure B2C integration
│   └── [other-services]    # Additional services
├── repositories/           # Data access layer
│   ├── patient-repository.js    # Patient data operations
│   ├── doctor-repository.js     # Doctor data operations
│   ├── payment-repository.js    # Payment data operations
│   └── [other-repositories]     # Additional repositories
├── queries/                # Optimized read operations
│   ├── patient-query.js    # Patient query operations
│   ├── medicine-query.js   # Medicine query operations
│   └── [other-queries]     # Additional queries
├── models/                 # Data models and schemas
│   ├── patient-model.js    # Patient data model
│   ├── doctor-model.js     # Doctor data model
│   └── [other-models]      # Additional models
├── utils/                  # Utility functions
│   ├── pagination.js       # Pagination utilities
│   ├── sanitization.js     # Data sanitization
│   └── [other-utils]       # Additional utilities
└── tasks/                  # Background tasks and cron jobs
    ├── finalize-patient-history-cron.js
    └── finalize-records-cron.js
```

---

## Data Layer

### Cosmos DB Architecture

#### Database Configuration
- **Database Name**:
  - Development: ArcaAudioLayer
  - Production: emr
- **API**: NoSQL API (Document-based)
- **Consistency Level**: Session (default)
- **Throughput**:
  - Development: 100-1000 RU/s
  - Production: 400-4000 RU/s

#### Container Strategy
Each domain has dedicated containers with optimized partition keys:

```javascript
// Container Examples
{
  "patients": { partitionKey: "/id", throughput: 400 },
  "doctors": { partitionKey: "/id", throughput: 400 },
  "appointments": { partitionKey: "/doctorId", throughput: 400 },
  "medicines": { partitionKey: "/id", throughput: 400 },
  "lab_tests": { partitionKey: "/patientId", throughput: 400 },
  "payments": { partitionKey: "/organizationId", throughput: 400 },
  "organizations": { partitionKey: "/id", throughput: 400 },
  "users": { partitionKey: "/organizationId", throughput: 400 }
}
```

#### Performance Optimization
- **Batch Operations**: Partition-aware batching (max 100 operations per partition)
- **Connection Pooling**: Shared Cosmos DB client with connection reuse
- **Caching**: Redis cache for frequently accessed data

### Redis Cache Strategy
- **Session Storage**: User sessions and JWT tokens
- **Frequently Accessed Data**: Medicine lists, lab test catalogs
- **Temporary Data**: OTP codes

---

## Business Logic Layer

### Handler Pattern
Each domain has handlers that process business logic:
- **Patient Handler**: Patient lifecycle management
- **Doctor Handler**: Doctor management and EMR customization
- **Appointment Handler**: Appointment scheduling and management
- **Payment Handler**: Razorpay payment processing
- **ABDM Handler**: ABHA number management

### Service Pattern
Services contain domain-specific business logic:
- **Patient Service**: Core patient operations
- **Doctor Service**: Doctor profile management
- **Payment Service**: Payment processing logic
- **B2C Service**: Azure B2C user management
- **Email Service**: Email notifications
- **OpenAI Service**: AI-powered medical summaries

### External Service Integrations

#### OpenAI Service
- **Medical Summaries**: Conversation analysis and summary generation
- **Ambient Listening**: Real-time conversation processing
- **Lifestyle Analysis**: Patient lifestyle data interpretation

#### B2C Service
- **User Management**: Azure AD B2C integration
- **Authentication**: JWT token validation
- **User Provisioning**: Automated user creation and management

---

## API Layer

### Azure Functions Configuration
```javascript
// Function App Settings
{
  "version": "2.0",
  "extensionBundle": {
    "id": "Microsoft.Azure.Functions.ExtensionBundle",
    "version": "[4.*, 5.0.0)"
  },
  "logging": {
    "applicationInsights": {
      "samplingSettings": {
        "isEnabled": true,
        "excludedTypes": "Request"
      }
    }
  }
}
```

### API Gateway (APIM) Configuration
- **Base URLs**:
  - Main API: `https://emr-ms-dev-apim.azure-api.net/EMR-MS/api/v0.1`
  - ABDM: `https://emr-ms-dev-apim.azure-api.net/abdm/v0.1`
  - Appointments: `https://emr-ms-dev-apim.azure-api.net/appointment/v0.1`
- **CORS**: Configured for cross-origin requests
- **Authentication**: JWT Bearer token validation

### Function Routing Strategy
```javascript
// Example function definition
app.http('patient-profile', {
  methods: ['GET', 'POST', 'PATCH'],
  route: 'patient',
  authLevel: 'function',
  handler: async (req, context) => {
    return await patientHandler.handleRequest(req)
  }
})
```

---

## Security & Authentication

### Authentication Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   Azure APIM    │───▶│ Azure Functions │
│                 │    │  (API Gateway)  │    │   (Backend)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Azure AD B2C   │    │  JWT Validation │    │ Role-Based Auth │
│ (Identity Mgmt) │    │   & Rate Limit  │    │  & Permissions  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### JWT Token Structure
```javascript
// Actual Azure B2C JWT Token Structure
{
  "exp": 1755690404,
  "nbf": 1755689804,
  "ver": "1.0",
  "iss": "https://erm20240520.b2clogin.com/cecfdadd-c501-4007-8619-84df7c41930b/v2.0/",
  "sub": "80fa2c02-96ae-4524-ab82-1f3832936928",
  "aud": "f22ad9c9-3fe2-4921-b825-ed8b887f3ab7",
  "nonce": "************",
  "iat": 1755689804,
  "auth_time": 1755689799,
  "oid": "80fa************",
  "name": "James",
  "emails": ["<EMAIL>"],
  "tfp": "B2C_1_emrapp"
}
```

### Role-Based Access Control (RBAC)
```javascript
// Default Roles
const DefaultRoles = {
  SUPER_ADMIN: 'super admin',
  ORGANIZATION_SUPER_ADMIN: 'organization super admin',
  ORGANIZATION_ADMIN: 'organization admin',
  DOCTOR: 'doctor',
  NURSE: 'nurse',
  RECEPTIONIST: 'receptionist'
}

// Permission System (Sample - 50+ permissions available)
const Permissions = {
  'emr.access': 'Access to EMR system',
  'mrd.access': 'Access to MRD system',
  'patient.view': 'View patient information',
  'patient.manage': 'Manage patient records',
  'doctor.view': 'View doctor profiles',
  'appointment.manage': 'Manage appointments',
  'lab.manage': 'Manage lab tests',
  'prescription.create': 'Create prescriptions',
  'payment.view': 'View payments',
  'organization.manage': 'Manage organization'
  // ... 40+ more permissions
  'prescription.create': 'Create prescriptions',
  'payment.view': 'View payments',
  'organization.manage': 'Manage organization'
  .................
}
```

### Data Security
- **Encryption at Rest**: Cosmos DB automatic encryption (built-in)
- **Encryption in Transit**: HTTPS/TLS 1.2+ for all communications (Azure Functions default)
- **Data Sanitization**: Input validation and sanitization
- **PII Protection**: Patient data encryption and access logging
- **Audit Logging**: Comprehensive audit trail for most operations

---

## External Integrations

### 1. ABDM (Ayushman Bharat Digital Mission)
```javascript
// ABDM Service Configuration
{
  baseUrl: 'https://abhasbx.abdm.gov.in/abha/api/v3',
  clientId: '********',
  clientSecret: '**********',
  operations: [
    'initiate-aadhaar',
    'initiate-mobile',
    'verify-otp',
    'details-by-number'
  ]
}
```

### 2. Razorpay Payment Gateway
```javascript
// Payment Configuration
{
  keyId: 'r**********************',
  keySecret: 'rzp_test_**********************',
  webhookSecret: 'webhook_secret',
  paymentTypes: [
    'patient_registration',
    'consultation',
    'prescription',
    'lab_tests'
  ]
}
```

### 3. Azure OpenAI Service
```javascript
// OpenAI Configuration
{
  endpoint: 'https://erm-dev-openai.openai.azure.com',
  apiKey: '******************',
  model: 'emrsummary4o',
  features: [
    'medical_summaries',
    'ambient_listening',
    'lifestyle_analysis'
  ]
}
```

### 4. Healthcare Standards Integration
- **ICD-11**: International Classification of Diseases
- **SNOMED CT**: Systematized Nomenclature of Medicine Clinical Terms
- **LOINC**: Logical Observation Identifiers Names and Codes
- **HL7 FHIR**: Healthcare data exchange standards (planned for future implementation)

### 5. OCR Service Integration
```javascript
// OCR Service for Lab Reports
{
  endpoint: 'http://ocrcontainergroup-v1.eastus.azurecontainer.io:8000/ocr/',
  supportedFormats: ['PDF'], // Only PDF supported currently
  features: [
    'text_extraction',
    'structured_data_parsing',
    'medical_terminology_recognition'
  ]
}
```

---

## Development Environment

### Local Development Setup
```bash
# Prerequisites
- Node.js 20+
- Azure Functions Core Tools v4
- Azure CLI
- Visual Studio Code

# Environment Configuration
{
  "FUNCTIONS_WORKER_RUNTIME": "node",
  "COSMOS_DB_CONNECTIONSTRING": "local_cosmos_connection",
  "COSMOS_DB_DATABASE": "ArcaAudioLayer",
  "OPENAI_ENDPOINT": "azure_openai_endpoint",
  "CLIENT_ID": "azure_b2c_client_id",
  "TENANT_ID": "azure_tenant_id",
  "cosmos_running_mode": "emulator"
}
```



#### Package Dependencies
```json
{
  "dependencies": {
    "@azure/cosmos": "^4.0.0",
    "@azure/functions": "^4.5.0",
    "@azure/identity": "^4.3.0",
    "@azure/storage-blob": "^12.26.0",
    "@microsoft/microsoft-graph-client": "^3.0.7",
    "axios": "^1.7.2",
    "jsonwebtoken": "^9.0.2",
    "razorpay": "^2.9.6",
    "redis": "^4.7.0",
    "openai": "^4.38.5"
  }
}
```

---

## CI/CD Pipeline

### Azure DevOps Pipeline Configuration

#### Build Pipeline (azure-pipelines.yml)
```yaml
trigger:
  - main
  - develop
  - test
  pool:
    vmImage: 'ubuntu-latest'
  
  steps:
  - script: echo Hello, world!
    displayName: 'Run a one-line script'
  
  - script: |
      echo Add other tasks to build, test, and deploy your project.
      echo See https://aka.ms/yaml
    displayName: 'Run a multi-line script'
  
  - task: Docker@2
    inputs:
      containerRegistry: 'ermdevcontainer'
      repository: 'emr-v01/emr-ms'
      command: 'buildAndPush'
      Dockerfile: 'Dockerfile'
      tags: '$(Build.SourceBranchName).$(Build.BuildId)'
  
  - task: PublishBuildArtifacts@1
    inputs:
      ArtifactName: 'emr-ms'
      publishLocation: 'Container'
      PathtoPublish: '.'
```

#### Dockerfile Configuration
```dockerfile
FROM mcr.microsoft.com/azure-functions/node:4-nightly-node20

ENV AzureWebJobsScriptRoot=/home/<USER>/wwwroot \
    AzureFunctionsJobHost__Logging__Console__IsEnabled=true

COPY . /home/<USER>/wwwroot

RUN cd /home/<USER>/wwwroot && \
    npm install
```

### Release Pipeline Strategy

#### Environment Configuration
```yaml
# Development Environment
- Environment: dev
  ResourceGroup: EMR-DEV
  FunctionApp: EMR-MS
  CosmosDB: emr-dev-cosmosdb
  StorageAccount: ermdevstoragedata

# Production Environment
- Environment: production
  ResourceGroup: EMR-TEST
  FunctionApp: EMR-MS-TEST
  CosmosDB: emrdbtest
  StorageAccount: ermdevstoragedata
```

#### Deployment Process
1. **Build Trigger**: Code commit to main/develop/test branches
2. **Docker Build**: Create containerized application image
3. **Image Push**: Push to Azure Container Registry
4. **Release Trigger**: Manual deployment trigger
5. **Environment Selection**: Choose target environment (Dev/Production)
6. **Configuration Update**: Environment-specific settings
7. **Health Check**: Verify deployment success

---

## Deployment Architecture

### Azure Infrastructure

#### Resource Groups
```
EMR-DEV/
├── EMR-MS (Function App)
├── emr-dev-cosmosdb (Cosmos DB Account)
├── EMR-MS-DEV-apim (API Management)
├── ermdevstoragedata (Storage Account)
├── emrdevcache (Redis Cache)
├── ermdevcontainer (Container Registry)
```

#### Network Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    Internet Gateway                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Azure API Management                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Rate Limiting │  │  Authentication │  │     CORS     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Azure Functions                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Patient APIs   │  │  Doctor APIs    │  │ Payment APIs │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  Data Layer                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Cosmos DB     │  │   Redis Cache   │  │ Blob Storage │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Scaling Configuration
```javascript
// Cosmos DB Scaling
{
  "throughput": {
    "development": "100-1000 RU/s",
    "production": "400-4000 RU/s"
  },
  "autoscale": true,
  "maxThroughput": 4000
}
```

---

## Backup Strategy

### Cosmos DB Backup
- **Frequency**: Every 20-24 hours
- **Restore**: Point-in-time restore capability
- **Storage**: Geo-redundant backup storage
- **Retention**: 30-day retention period

---

## Conclusion

This EMR application is built as a **modular monolith** using Azure Functions, providing a scalable and maintainable healthcare management system. The architecture emphasizes clean separation of concerns, robust security, and efficient data management while maintaining the flexibility to evolve into microservices as needed.

### Key Architectural Strengths:
- **Modular Monolith**: Single deployment with clear domain separation
- **Serverless Architecture**: Cost-effective and automatically scalable Azure Functions
- **Clean Architecture**: Maintainable codebase with separation of concerns
- **Security-First**: Comprehensive security with Azure B2C and role-based permissions
- **Healthcare Standards**: Integration with ICD-11, SNOMED CT, LOINC, and ABDM
- **AI-Powered**: Advanced AI capabilities for medical insights and ambient listening
- **DevOps Ready**: Automated CI/CD pipeline with Docker containerization

This architecture provides a solid foundation for a modern, scalable, and secure healthcare management system that can adapt to future requirements and technological advances.
