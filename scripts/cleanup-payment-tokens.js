#!/usr/bin/env node

/**
 * Payment Token Cleanup Script
 * 
 * This script cleans up expired payment tokens and can be run as a scheduled job.
 * It should be run periodically (e.g., daily) to maintain database hygiene.
 * 
 * Usage:
 *   node scripts/cleanup-payment-tokens.js
 *   
 * Environment Variables:
 *   - CLEANUP_DRY_RUN: Set to 'true' to run in dry-run mode (default: false)
 *   - CLEANUP_BATCH_SIZE: Number of tokens to process in each batch (default: 100)
 */

const paymentTokenRepository = require('../src/repositories/payment-token-repository')
const logging = require('../src/common/logging')

class PaymentTokenCleanup {
  constructor() {
    this.dryRun = process.env.CLEANUP_DRY_RUN === 'true'
    this.batchSize = parseInt(process.env.CLEANUP_BATCH_SIZE) || 100
  }

  async run() {
    try {
      logging.logInfo('Starting payment token cleanup...', {
        dryRun: this.dryRun,
        batchSize: this.batchSize
      })

      const startTime = Date.now()
      let totalCleaned = 0

      if (this.dryRun) {
        logging.logInfo('Running in DRY-RUN mode - no tokens will be actually cleaned')
        totalCleaned = await this.dryRunCleanup()
      } else {
        totalCleaned = await this.performCleanup()
      }

      const duration = Date.now() - startTime
      
      logging.logInfo('Payment token cleanup completed', {
        totalCleaned,
        durationMs: duration,
        dryRun: this.dryRun
      })

      // Exit with success
      process.exit(0)
    } catch (error) {
      logging.logError('Payment token cleanup failed:', error)
      process.exit(1)
    }
  }

  async performCleanup() {
    try {
      const cleanedCount = await paymentTokenRepository.cleanupExpiredTokens()
      logging.logInfo(`Successfully cleaned up ${cleanedCount} expired tokens`)
      return cleanedCount
    } catch (error) {
      logging.logError('Error during token cleanup:', error)
      throw error
    }
  }

  async dryRunCleanup() {
    try {
      // Initialize repository
      await paymentTokenRepository.initialize()
      
      const now = new Date().toISOString()
      
      // Query expired payment tokens
      const expiredTokensQuery = {
        query: 'SELECT * FROM c WHERE c.tokenExpiresAt < @now OR c.tokenRevoked = true',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: expiredTokens } = await paymentTokenRepository.paymentTokenContainer
        .items.query(expiredTokensQuery).fetchAll()
      
      // Query expired session tokens
      const expiredSessionsQuery = {
        query: 'SELECT * FROM c WHERE c.sessionExpiresAt < @now',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: expiredSessions } = await paymentTokenRepository.sessionTokenContainer
        .items.query(expiredSessionsQuery).fetchAll()
      
      const totalExpired = expiredTokens.length + expiredSessions.length
      
      logging.logInfo('Dry run results:', {
        expiredPaymentTokens: expiredTokens.length,
        expiredSessionTokens: expiredSessions.length,
        totalExpired
      })

      // Log sample expired tokens (without sensitive data)
      if (expiredTokens.length > 0) {
        logging.logInfo('Sample expired payment tokens:', {
          samples: expiredTokens.slice(0, 5).map(token => ({
            id: token.id,
            paymentId: token.paymentId,
            organizationId: token.organizationId,
            tokenCreatedAt: token.tokenCreatedAt,
            tokenExpiresAt: token.tokenExpiresAt,
            tokenRevoked: token.tokenRevoked,
            tokenUsageCount: token.tokenUsageCount
          }))
        })
      }

      return totalExpired
    } catch (error) {
      logging.logError('Error during dry run cleanup:', error)
      throw error
    }
  }

  async getCleanupStatistics() {
    try {
      await paymentTokenRepository.initialize()
      
      const now = new Date().toISOString()
      
      // Get total token counts
      const totalTokensQuery = 'SELECT VALUE COUNT(1) FROM c'
      const { resources: [totalPaymentTokens] } = await paymentTokenRepository.paymentTokenContainer
        .items.query(totalTokensQuery).fetchAll()
      
      const { resources: [totalSessionTokens] } = await paymentTokenRepository.sessionTokenContainer
        .items.query(totalTokensQuery).fetchAll()
      
      // Get expired token counts
      const expiredTokensQuery = {
        query: 'SELECT VALUE COUNT(1) FROM c WHERE c.tokenExpiresAt < @now OR c.tokenRevoked = true',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: [expiredPaymentTokens] } = await paymentTokenRepository.paymentTokenContainer
        .items.query(expiredTokensQuery).fetchAll()
      
      const expiredSessionsQuery = {
        query: 'SELECT VALUE COUNT(1) FROM c WHERE c.sessionExpiresAt < @now',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: [expiredSessionTokens] } = await paymentTokenRepository.sessionTokenContainer
        .items.query(expiredSessionsQuery).fetchAll()
      
      return {
        totalPaymentTokens,
        totalSessionTokens,
        expiredPaymentTokens,
        expiredSessionTokens,
        totalTokens: totalPaymentTokens + totalSessionTokens,
        totalExpired: expiredPaymentTokens + expiredSessionTokens
      }
    } catch (error) {
      logging.logError('Error getting cleanup statistics:', error)
      throw error
    }
  }
}

// Handle command line arguments
const args = process.argv.slice(2)
const command = args[0] || 'cleanup'

async function main() {
  const cleanup = new PaymentTokenCleanup()
  
  switch (command) {
    case 'cleanup':
      await cleanup.run()
      break
      
    case 'stats':
      const stats = await cleanup.getCleanupStatistics()
      console.log('Payment Token Statistics:')
      console.log(JSON.stringify(stats, null, 2))
      break
      
    case 'dry-run':
      process.env.CLEANUP_DRY_RUN = 'true'
      await cleanup.run()
      break
      
    default:
      console.log('Usage: node cleanup-payment-tokens.js [cleanup|stats|dry-run]')
      console.log('  cleanup  - Clean up expired tokens (default)')
      console.log('  stats    - Show token statistics')
      console.log('  dry-run  - Show what would be cleaned without actually cleaning')
      process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Script failed:', error)
    process.exit(1)
  })
}

module.exports = PaymentTokenCleanup
