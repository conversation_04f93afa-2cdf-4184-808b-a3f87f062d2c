const getLabTestsByPatientIdQuery = (patientId) =>
  `SELECT * FROM c WHERE c.patientId = "${patientId}"`

const getLabTestById = (labTestId) =>
  `SELECT * FROM c WHERE c.id = "${labTestId}"`

const searchTestQuery = (queryString, patientId) => `
  SELECT c.id, c.patientId, test 
  FROM c 
  JOIN test IN c.labTests 
  WHERE c.patientId = "${patientId}" AND (
    CONTAINS(LOWER(test.testName), LOWER("${queryString}"))
  )
`
const getLabTestsWithSortingAndFilteringQuery = (
  patientId,
  dateClause,
  sortClause,
  searchText,
  department,
) => {
  const searchClause = searchText
    ? `AND EXISTS(SELECT VALUE t FROM t IN c.labTests WHERE CONTAINS(LOWER(t.testName), LOWER("${searchText}")))`
    : ''


  return `
    SELECT * FROM c
    WHERE c.patientId = "${patientId}"
    ${dateClause}
    ${searchClause}
    ${sortClause}
  `
}

const getPatientLatestLabTestStatusQuery = (patientId) => {
  return `SELECT TOP 1 c.labTests 
        FROM c 
        WHERE c.patientId = "${patientId}"
        ORDER BY c.updated_on DESC`
}

module.exports = {
  getLabTestsByPatientIdQuery,
  getLabTestById,
  searchTestQuery,
  getLabTestsWithSortingAndFilteringQuery,
  getPatientLatestLabTestStatusQuery
}
