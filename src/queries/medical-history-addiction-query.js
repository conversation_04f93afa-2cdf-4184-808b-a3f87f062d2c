const getMedicalHistoryAddictionByPatientIdQuery = (patientId) => {
  return `SELECT * FROM c WHERE c.patientId = '${patientId}'`
}

const getMedicalHistoryAddictionByIdQuery = (id) => {
  return `SELECT * FROM c WHERE c.id = '${id}'`
}

const getMedicalHistoryAddictionByStatusQuery = (status) => {
  return `SELECT * FROM c WHERE c.status = '${status}'`
}

const getMedicalHistoryAddictionByPatientAndDateRangeQuery = (patientId, startDate, endDate) => {
  const startTimestamp = new Date(startDate).getTime() / 1000
  const endTimestamp = new Date(endDate).getTime() / 1000
  return `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c._ts >= ${startTimestamp} AND c._ts <= ${endTimestamp} ORDER BY c._ts DESC`
}

const getMedicalHistoryAddictionByDiagnosisQuery = (diseaseName) => {
  return `SELECT * FROM c WHERE ARRAY_CONTAINS(c.diagnosis, {"diseaseName": "${diseaseName}"}, true)`
}

const getMedicalHistoryAddictionBySubstanceHistoryQuery = (substanceType, history) => {
  return `SELECT * FROM c WHERE c.${substanceType}.history = '${history}'`
}

const getMedicalHistoryAddictionWithNicotineTestQuery = () => {
  return `SELECT * FROM c WHERE IS_DEFINED(c.nicotineDependenceTest)`
}

const getMedicalHistoryAddictionByCreatedByQuery = (createdBy) => {
  return `SELECT * FROM c WHERE c.createdBy = '${createdBy}' ORDER BY c.createdAt DESC`
}

const getMedicalHistoryAddictionByDateRangeQuery = (startDate, endDate) => {
  return `SELECT * FROM c WHERE c.createdAt >= '${startDate}' AND c.createdAt <= '${endDate}' ORDER BY c.createdAt DESC`
}

module.exports = {
  getMedicalHistoryAddictionByPatientIdQuery,
  getMedicalHistoryAddictionByIdQuery,
  getMedicalHistoryAddictionByStatusQuery,
  getMedicalHistoryAddictionByPatientAndDateRangeQuery,
  getMedicalHistoryAddictionByDiagnosisQuery,
  getMedicalHistoryAddictionBySubstanceHistoryQuery,
  getMedicalHistoryAddictionWithNicotineTestQuery,
  getMedicalHistoryAddictionByCreatedByQuery,
  getMedicalHistoryAddictionByDateRangeQuery
}