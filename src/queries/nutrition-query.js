module.exports = {
  getAllFoodNamesQuery: () => {
    return "SELECT c.food_name FROM c ORDER BY c.food_name ASC"
  },

  getFoodNamesBySearchQuery: (searchTerm) => {
    return `SELECT c.food_name FROM c WHERE STARTSWITH(UPPER(c.food_name), UPPER('${searchTerm}')) ORDER BY c.food_name ASC`
  },

  getServingUnitByFoodNameQuery: (foodName) => {
    return `SELECT c.servings_unit FROM c WHERE UPPER(c.food_name) = UPPER('${foodName}')`
  },

  getNutritionDataByFoodNamesQuery: (foodNames) => {
    const foodNamesList = foodNames.map(name => `'${name.replace(/'/g, "''")}'`).join(', ')
    return `SELECT c.food_name, 
            c.unit_serving_energy_kcal, 
            c.unit_serving_carb_g, 
            c.unit_serving_protein_g, 
            c.unit_serving_fat_g, 
            c.unit_serving_freesugar_g, 
            c.unit_serving_fibre_g, 
            c.unit_serving_calcium_mg, 
            c.unit_serving_phosphorus_mg, 
            c.unit_serving_magnesium_mg, 
            c.unit_serving_sodium_mg, 
            c.unit_serving_potassium_mg, 
            c.unit_serving_iron_mg,
            c.unit_serving_sfa_mg,
            c.unit_serving_mufa_mg,
            c.unit_serving_pufa_mg,
            c.unit_serving_cholesterol_mg
            FROM c WHERE UPPER(c.food_name) IN (${foodNamesList.split(', ').map(name => `UPPER(${name})`).join(', ')})`
  }
}
