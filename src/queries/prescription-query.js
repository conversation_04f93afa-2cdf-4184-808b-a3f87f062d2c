const { buildDateFilterClause } = require('../utils/query-utils')

const getPrescriptionsByPatient = (
  patientId,
  dateFilter = null,
  customDateRange = null,
  searchText = null,
) => {
  let dateClause = buildDateFilterClause(dateFilter)

  if (dateFilter === 'custom' && customDateRange) {
    dateClause = `AND c.updated_on >= "${customDateRange.start}" AND c.updated_on <= "${customDateRange.end}"`
  }

  const searchClause = searchText
    ? `AND (
        CONTAINS(LOWER(c.doctor), LOWER("${searchText}")) 
        OR ARRAY_LENGTH(
          ARRAY(
            SELECT VALUE m 
            FROM m IN c.medicines 
            WHERE CONTAINS(LOWER(m.genericName), LOWER("${searchText}"))
          )
        ) > 0
      )`
    : ''

  return `
    SELECT * FROM c 
    WHERE c.patientId = "${patientId}" 
    ${dateClause}
    ${searchClause}
  `
}

const getPrescriptionById = (prescriptionId) => {
  return `SELECT * FROM c WHERE  c.id = "${prescriptionId}"`
}
const searchPrescriptionQuery = (queryString, patientId) => `
  SELECT * FROM c 
  WHERE c.patientId = "${patientId}" AND (
    CONTAINS(LOWER(c.doctor), LOWER("${queryString}")) 
    OR ARRAY_LENGTH(
      ARRAY(
        SELECT VALUE m 
        FROM m IN c.medicines 
        WHERE CONTAINS(LOWER(m.genericName), LOWER("${queryString}"))
      )
    ) > 0
  )
  ORDER BY c.updated_on DESC
`
module.exports = {
  getPrescriptionsByPatient,
  getPrescriptionById,
  searchPrescriptionQuery,
}
