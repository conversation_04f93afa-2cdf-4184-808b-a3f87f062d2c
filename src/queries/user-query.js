const buildUserFilterConditions = (organizationId, search, role, isActive) => {
  const conditions = []

  if (organizationId) {
    conditions.push(`c.organizationId = '${organizationId}'`)
  }

  if (search) {
    conditions.push(
      `(CONTAINS(c.name, '${search}') OR CONTAINS(c.email, '${search}') OR CONTAINS(c.phone, '${search}'))`,
    )
  }

  if (role) {
    conditions.push(`c.userRole = '${role}'`)
  }

  if (isActive !== null) {
    conditions.push(`c.isActive = ${isActive}`)
  }

  return conditions
}

const getUsersByOrganizationQuery = (
  organizationId,
  search,
  role,
  isActive,
  sortBy,
  sortOrder,
  pageSize = null,
  page = null,
) => {
  const conditions = buildUserFilterConditions(organizationId, search, role, isActive)

  let query = 'SELECT * FROM c'
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`
  }
  query += ` ORDER BY c.${sortBy} ${sortOrder.toUpperCase()}`

  if (pageSize && page) {
    const offset = (page - 1) * pageSize
    query += ` OFFSET ${offset} LIMIT ${pageSize}`
  }

  return query
}

const getUsersCountQuery = (organizationId, search, role, isActive) => {
  const conditions = buildUserFilterConditions(organizationId, search, role, isActive)

  let query = 'SELECT VALUE COUNT(1) FROM c'
  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(' AND ')}`
  }

  return query
}

const getUserByEmailIncludingInactiveQuery = (email) => {
  return `SELECT * FROM c WHERE c.email = '${email}'`
}

module.exports = {
  getUsersByOrganizationQuery,
  getUsersCountQuery,
  getUserByEmailIncludingInactiveQuery,
}
