module.exports = {
  getAllFeaturesQuery: (includeInactive = false) => {
    let query = 'SELECT * FROM c'
    if (!includeInactive) {
      query += ' WHERE c.isActive = true'
    }
    query += ' ORDER BY c.featureName ASC'
    return query
  },

  searchFeaturesQuery: (searchParams) => {
    const conditions = ['c.isActive = true']
    const parameters = []

    if (searchParams.featureName) {
      conditions.push('CONTAINS(LOWER(c.featureName), LOWER(@featureName))')
      parameters.push({ name: '@featureName', value: searchParams.featureName })
    }

    if (searchParams.permissionKey) {
      conditions.push('ARRAY_CONTAINS(c.permissionKeys, @permissionKey)')
      parameters.push({ name: '@permissionKey', value: searchParams.permissionKey })
    }

    return {
      query: `SELECT * FROM c WHERE ${conditions.join(' AND ')} ORDER BY c.created_on DESC`,
      parameters: parameters,
    }
  },

  getFeaturesByPermissionKeyQuery: (permissionKey) => {
    return `SELECT * FROM c WHERE c.isActive = true AND ARRAY_CONTAINS(c.permissionKeys, '${permissionKey}') ORDER BY c.featureName ASC`
  },

  getAllPermissionKeysQuery: () => {
    return `SELECT DISTINCT VALUE pk FROM c JOIN pk IN c.permissionKeys WHERE c.isActive = true`
  },

  // Batch query to get features by multiple IDs
  getFeaturesByIdsQuery: (featureIds) => {
    if (!featureIds || featureIds.length === 0) return null
    const idList = featureIds.map(id => `'${id}'`).join(',')
    return `SELECT * FROM c WHERE c.id IN (${idList})`
  },
}
