module.exports = {
  getTotalPatientsQuery: (organizationId) => {
    return {
      query: 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId',
      parameters: [{ name: '@organizationId', value: organizationId }],
    }
  },

  getTodaysAppointmentsQuery: (organizationId, todayString) => {
    return {
      query:
        'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId AND STARTSWITH(c.date, @todayString)',
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@todayString', value: todayString },
      ],
    }
  },

  getPatientQueueQuery: (organizationId, todayString) => {
    return {
      query:
        'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @organizationId AND (STARTSWITH(c.status, "Booked") OR c.status = "Booked-Arrived" OR c.status = "Booked-Booked") AND STARTSWITH(c.date, @todayString)',
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@todayString', value: todayString },
      ],
    }
  },

  getAverageWaitingTimeQuery: (organizationId, todayString) => {
    return {
      query:
        'SELECT c.patientArrivalTime, c.consultationStartTime, c.consultationEndTime FROM c WHERE c.organizationId = @organizationId AND STARTSWITH(c.date, @todayString) AND IS_DEFINED(c.consultationStartTime) AND IS_DEFINED(c.patientArrivalTime)',
      parameters: [
        { name: '@organizationId', value: organizationId },
        { name: '@todayString', value: todayString },
      ],
    }
  },

  getUpcomingAppointmentsQuery: (organizationId, dateFilter, doctorId) => {
    let query = `
      SELECT 
        c.id as queueId,
        c.appointmentId,
        c.patientId,
        c.doctorId,
        c.date,
        c.time,
        c.status,
        c.type
      FROM c 
      WHERE c.organizationId = @organizationId
        AND IS_DEFINED(c.status)
        AND NOT ARRAY_CONTAINS(@excludedStatuses, LOWER(c.status))
    `

    // Default: restrict to today and future dates
    const parameters = [
      { name: '@organizationId', value: organizationId }
    ]
    parameters.push({ name: '@excludedStatuses', value: ['canceled', 'consultation-done'] })

    if (!dateFilter) {
      const today = new Date().toISOString().split('T')[0]
      query += ` AND c.date >= @today`
      parameters.push({ name: '@today', value: today })
    }

    if (dateFilter) {
      let specificDate = null
      if (typeof dateFilter === 'string') {
        specificDate = dateFilter.split('T')[0]
      } else if (dateFilter && typeof dateFilter === 'object') {
        if (dateFilter.specificDate) specificDate = dateFilter.specificDate
        else if (dateFilter.date) {
          const d = new Date(dateFilter.date)
          if (!isNaN(d.getTime())) specificDate = d.toISOString().split('T')[0]
        }
      }

      if (specificDate) {
        query += ` AND STARTSWITH(c.date, @specificDate)`
        parameters.push({ name: '@specificDate', value: specificDate })
      }
    }

    // Add doctor filtering
    if (doctorId) {
      query += ` AND c.doctorId = @doctorId`
      parameters.push({ name: '@doctorId', value: doctorId })
    }

    return {
      query,
      parameters
    }
  },

  getPatientsByIdsQuery: (patientIds = []) => {
    if (!patientIds || patientIds.length === 0) return null
    return {
      query: 'SELECT c.id, c.name FROM c WHERE ARRAY_CONTAINS(@ids, c.id)',
      parameters: [{ name: '@ids', value: patientIds }]
    }
  },

  getDoctorsByIdsBulkQuery: (doctorIds = []) => {
    if (!doctorIds || doctorIds.length === 0) return null
    return {
      query:
        'SELECT c.id, c.general, c.name FROM c WHERE ARRAY_CONTAINS(@ids, c.id) OR ARRAY_CONTAINS(@ids, c.general.doctorID)',
      parameters: [{ name: '@ids', value: doctorIds }]
    }
  },
  
}
