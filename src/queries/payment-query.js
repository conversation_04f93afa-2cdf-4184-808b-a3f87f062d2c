const { PaymentStatus } = require('../common/constant')

function baseSelect() {
  return 'SELECT * FROM c WHERE 1=1'
}

function getPaymentByIdQuery(paymentId) {
  return `SELECT * FROM c WHERE c.id = '${paymentId}'`
}

function getPaymentByRazorpayOrderIdQuery(orderId) {
  return `SELECT * FROM c WHERE c.razorpayOrderId = '${orderId}'`
}

function getPaymentByRazorpayPaymentIdQuery(paymentId) {
  return `SELECT * FROM c WHERE c.razorpayPaymentId = '${paymentId}'`
}

function getPaymentsByOrganizationQuery(
  organizationId,
  filters = {},
  continuationToken,
) {
  let query = baseSelect()

  if (organizationId) query += ` AND c.organizationId = '${organizationId}'`
  if (filters.patientId) query += ` AND c.patientId = '${filters.patientId}'`
  if (filters.status) query += ` AND c.status = '${filters.status}'`
  if (filters.paymentType)
    query += ` AND c.paymentType = '${filters.paymentType}'`
  if (filters.startDate) query += ` AND c.createdAt >= '${filters.startDate}'`
  if (filters.endDate) query += ` AND c.createdAt <= '${filters.endDate}'`
  if (filters.minAmount) query += ` AND c.amount >= ${filters.minAmount}`
  if (filters.maxAmount) query += ` AND c.amount <= ${filters.maxAmount}`

  if (continuationToken && continuationToken.startsWith('cursor_')) {
    const cursorValue = continuationToken.replace('cursor_', '')
    if (cursorValue.includes('T')) {
      query += ` AND c.createdAt < '${cursorValue}'`
    } else {
      query += ` AND c._ts < ${cursorValue}`
    }
  }

  return query + ' ORDER BY c.createdAt DESC'
}

function getPaymentStatisticsQuery(organizationId, startDate, endDate) {
  let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status = '${PaymentStatus.COMPLETED}'`
  if (startDate && endDate) {
    query += ` AND c.createdAt >= '${startDate}' AND c.createdAt <= '${endDate}'`
  }
  return query
}

function getPaymentBySubscriberEmailQuery(organizationId, subscriberEmail) {
  return `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.subscriberEmail = '${subscriberEmail}' AND c.paymentType = 'subscription' ORDER BY c.createdAt DESC`
}

// Batch query to get payments by multiple IDs
function getPaymentsByIdsQuery(paymentIds) {
  if (!paymentIds || paymentIds.length === 0) return null
  const idList = paymentIds.map(id => `'${id}'`).join(',')
  return `SELECT * FROM c WHERE c.id IN (${idList})`
}

// Batch query to get payments by multiple Razorpay payment IDs
function getPaymentsByRazorpayPaymentIdsQuery(razorpayPaymentIds) {
  if (!razorpayPaymentIds || razorpayPaymentIds.length === 0) return null
  const idList = razorpayPaymentIds.map(id => `'${id}'`).join(',')
  return `SELECT * FROM c WHERE c.razorpayPaymentId IN (${idList})`
}

// Batch query to get subscription payments for multiple emails in an organization
function getSubscriptionPaymentsByEmailsQuery(organizationId, emails) {
  if (!emails || emails.length === 0) return null
  const emailList = emails.map(email => `'${email}'`).join(',')
  return `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.subscriberEmail IN (${emailList}) AND c.paymentType = 'subscription'`
}

module.exports = {
  getPaymentByIdQuery,
  getPaymentByRazorpayOrderIdQuery,
  getPaymentByRazorpayPaymentIdQuery,
  getPaymentBySubscriberEmailQuery,
  getPaymentsByOrganizationQuery,
  getPaymentStatisticsQuery,
  getPaymentsByIdsQuery,
  getPaymentsByRazorpayPaymentIdsQuery,
  getSubscriptionPaymentsByEmailsQuery,
}
