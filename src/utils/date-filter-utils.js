const { DashboardFilter } = require('../common/constant')

class DateFilterUtils {
  /**
   * Get date range based on filter type
   * @param {string} dateFilter - Filter type from DashboardFilter enum
   * @param {Object} customDateRange - Custom date range with start and end dates
   * @returns {Object} Object with startDate and endDate in YYYY-MM-DD format
   */
  static getDateRange(dateFilter, customDateRange = null) {
    const now = new Date()
    let startDate, endDate

    switch (dateFilter) {
      case DashboardFilter.TODAY:
        startDate = endDate = now.toISOString().split('T')[0]
        break

      case DashboardFilter.YESTERDAY:
        const yesterday = new Date(now)
        yesterday.setDate(yesterday.getDate() - 1)
        startDate = endDate = yesterday.toISOString().split('T')[0]
        break

      case DashboardFilter.LAST_7_DAYS:
        const sevenDaysAgo = new Date(now)
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
        startDate = sevenDaysAgo.toISOString().split('T')[0]
        endDate = now.toISOString().split('T')[0]
        break

      case DashboardFilter.LAST_15_DAYS:
        const fifteenDaysAgo = new Date(now)
        fifteenDaysAgo.setDate(fifteenDaysAgo.getDate() - 15)
        startDate = fifteenDaysAgo.toISOString().split('T')[0]
        endDate = now.toISOString().split('T')[0]
        break

      case DashboardFilter.LAST_30_DAYS:
        const thirtyDaysAgo = new Date(now)
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        startDate = thirtyDaysAgo.toISOString().split('T')[0]
        endDate = now.toISOString().split('T')[0]
        break

      case DashboardFilter.LAST_MONTH:
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0)
        startDate = lastMonth.toISOString().split('T')[0]
        endDate = lastMonthEnd.toISOString().split('T')[0]
        break

      case DashboardFilter.LAST_90_DAYS:
        const ninetyDaysAgo = new Date(now)
        ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)
        startDate = ninetyDaysAgo.toISOString().split('T')[0]
        endDate = now.toISOString().split('T')[0]
        break

      case DashboardFilter.CUSTOM:
        if (customDateRange && customDateRange.start && customDateRange.end) {
          startDate = customDateRange.start
          endDate = customDateRange.end
        } else {
          // Default to last 30 days if custom range is invalid
          const defaultStart = new Date(now)
          defaultStart.setDate(defaultStart.getDate() - 30)
          startDate = defaultStart.toISOString().split('T')[0]
          endDate = now.toISOString().split('T')[0]
        }
        break

      case DashboardFilter.ALL:
        // Return null to indicate no date filtering
        return { startDate: null, endDate: null }

      default:
        // Default to last 30 days
        const defaultStart = new Date(now)
        defaultStart.setDate(defaultStart.getDate() - 30)
        startDate = defaultStart.toISOString().split('T')[0]
        endDate = now.toISOString().split('T')[0]
    }

    return { startDate, endDate }
  }

  /**
   * Validate if a date filter is valid
   * @param {string} dateFilter - Filter to validate
   * @returns {boolean} True if valid
   */
  static isValidDateFilter(dateFilter) {
    return Object.values(DashboardFilter).includes(dateFilter)
  }

  /**
   * Get all available date filters
   * @returns {Object} DashboardFilter enum
   */
  static getAvailableFilters() {
    return DashboardFilter
  }

  /**
   * Format date for display (e.g., "01 May")
   * @param {string} dateStr - Date string in YYYY-MM-DD format
   * @returns {string} Formatted date
   */
  static formatDateForDisplay(dateStr) {
    const date = new Date(dateStr)
    const day = date.getDate().toString().padStart(2, '0')
    const month = date.toLocaleDateString('en-US', { month: 'short' })
    return `${day} ${month}`
  }

  /**
   * Get date range description for UI
   * @param {string} dateFilter - Filter type
   * @param {Object} customDateRange - Custom date range
   * @returns {string} Human readable description
   */
  static getDateRangeDescription(dateFilter, customDateRange = null) {
    switch (dateFilter) {
      case DashboardFilter.TODAY:
        return 'Today'
      case DashboardFilter.YESTERDAY:
        return 'Yesterday'
      case DashboardFilter.LAST_7_DAYS:
        return 'Last 7 days'
      case DashboardFilter.LAST_15_DAYS:
        return 'Last 15 days'
      case DashboardFilter.LAST_30_DAYS:
        return 'Last 30 days'
      case DashboardFilter.LAST_MONTH:
        return 'Last month'
      case DashboardFilter.LAST_90_DAYS:
        return 'Last 90 days'
      case DashboardFilter.CUSTOM:
        if (customDateRange && customDateRange.start && customDateRange.end) {
          return `${customDateRange.start} to ${customDateRange.end}`
        }
        return 'Custom range'
      case DashboardFilter.ALL:
        return 'All time'
      default:
        return 'Last 30 days'
    }
  }
}

module.exports = DateFilterUtils
