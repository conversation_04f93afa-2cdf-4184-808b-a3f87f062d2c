const path = require('path')
const excelToJson = require('convert-excel-to-json')

/**
 * Utility function to get the file path for the medicines.xlsx file.
 * @returns {string} The absolute path to the medicines.xlsx file.
 */
function getFilePath() {
  return path.join(process.cwd(), 'data/medicines.xlsx')
}
function getTestFilePath() {
  return path.join(process.cwd(), 'data/Loinc.xlsx')
}

async function parseExcelFile(file) {
  return (
    excelToJson({
      sourceFile: file.path,
      header: { rows: 1 },
      columnToKey: {
        A: 'LOINC_NUM',
        B: 'COMPONENT',
        C: 'PROPERTY',
        D: 'TIME_ASPCT',
        E: 'SYSTEM',
        F: 'SCALE_TYP',
        G: 'METHOD_TYP',
        H: 'CLASS',
        I: 'UNITS',
        J: 'LONG_COMMON_NAME',
      },
    }).Loinc || []
  )
}

module.exports = {
  getFilePath,
  getTestFilePath,
  parseExcelFile,
}
