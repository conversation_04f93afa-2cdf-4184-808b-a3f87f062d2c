const { APIPermissions } = require('../common/permissions') // adjust path as needed

/**
 * Build API permission objects for a role from a list of permission keys
 * @param {string[]} permissionKeys - List of permission keys
 * @returns {Array} - Array of permission records to be saved
 */
function buildRoleAPIs(permissionKeys) {
  const roleAPIs = []

  for (const key of permissionKeys) {
    const permission = APIPermissions.find((p) => p.key === key)

    if (!permission) continue

    if (!permission.apis.length && !permission.methods.length) {
      roleAPIs.push({ permissionKey: key })
    } else {
      for (const api of permission.apis) {
        roleAPIs.push({
          api,
          methods: permission.methods,
          permissionKey: key,
        })
      }
    }
  }

  return roleAPIs
}
const organizationSuperAdminPermissionKeys = [
  // MRD Module Access & Permissions
  'mrd.access',
  'mrd.manage-patient.view',
  'mrd.manage-patient.edit',
  'mrd.patient-queue.manage',
  'mrd.dashboard.view',
  'mrd.consultation.book',
  'mrd.vitals.view',
  'mrd.vitals.manage',

  // EMR Module Access & Permissions
  'emr.access',
  'emr.patientinfo.view',
  'emr.patientinfo.edit',
  'emr.consultation.manage',
  'emr.consultation.view',
  'emr.consultation.create',
  'emr.consultation.edit',
  'emr.consultation.ambient-listening',
  'emr.consultation.diagnosis',
  'emr.consultation.chatbot',
  'emr.consultation.doc-assist',
  'emr.consultation.future.view',
  'emr.dashboard.view',
  'emr.prescription.view',
  'emr.prescription.manage',
  'emr.prescription.chatbot',
  'emr.prescription.doc-assist',
  'emr.prescription-package.view',
  'emr.prescription-package.manage',
  'emr.prescription-department-package.manage',
  'emr.prescription-user-package.manage',
  'emr.medicine-package.view',
  'emr.medicine-package.manage',
  'emr.reports.manage',
  'emr.lab-test.view',
  'emr.lab-test.manage',
  'emr.lab-test.search',
  'emr.lab-master.ocr',
  'emr.lab-master.chatbot',
  'emr.lab-master.doc-assist',
  'emr.test-package.view',
  'emr.test-package.manage',
  'emr.labmaster-department-package.manage',
  'emr.labmaster-user-package.manage',
  'emr.lifestyle',
  'emr.lifestyle.manage',
  'emr.lifestyle.ambient-listening',
  'emr.lifestyle.chatbot',
  'emr.lifestyle.dashboard.view',
  'emr.lifestyle.doc-assist',
  'emr.lifestyle.physical-activity.view',
  'emr.doctorprofile.view',
  'emr.doctorprofile.edit',
  'emr.customise-emr',

  // Admin Portal Permissions
  'role.manage',
  'permission.manage',
  'organization.manage',
  'organization.patients.view',
  'user.view',
  'user.manage',
  'dashboard.view',
  'emr.patientinfo.view.sensitive',
  'emr.patientinfo.view.aadhar',
  'emr.patientinfo.view.abha',
  'emr.patientinfo.search',

  // Payment Permissions
  'payment.view',
  'payment.create',
  'emr.payment.appointment_booking',
  'emr.payment.lab_test',
  'emr.payment.prescription',
  'mrd.payment.patient_registration',
]
const nonAdminSystemRolePermissionKeys = [
  // 'emr.lab-test.view',
  // 'emr.lab-test.manage',
  // 'emr.lab-test.search',
  'emr.test-package.view',
  'emr.prescription-package.view',
  'role.manage',
  'permission.manage',
  'organization.manage',
  'organization.patients.view',
  'dashboard.view',
  'user.view',
  'user.manage',
  'emr.lifestyle.manage',
  // 'mrd.patient-queue.manage',
  // 'emr.patientinfo.edit',
  'emr.doctorprofile.view',
  'emr.patientinfo.view.sensitive',
  'emr.patientinfo.view.aadhar',
  'emr.patientinfo.view.abha',
  'emr.prescription-package.manage',
  'emr.medicine-package.view',
  'emr.consultation.view',
  'emr.consultation.create',
  'emr.consultation.edit',
  'payment.view',
  // 'emr.patientinfo.view',
  'emr.patientinfo.search',
  'emr.lifestyle.physical-activity.view',
  // 'mrd.manage-patient.view',
  // 'mrd.manage-patient.edit',
  'emr.consultation.future.view',
]

module.exports = {
  buildRoleAPIs,
  organizationSuperAdminPermissionKeys,
  nonAdminSystemRolePermissionKeys,
}
