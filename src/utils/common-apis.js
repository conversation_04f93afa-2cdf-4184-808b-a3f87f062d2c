// Common APIs that don't require specific permissions but need authentication
const COMMON_APIS = [
  // Doctor APIs
  'doctor/profile-picture/upload',
  'doctor/profile-picture/url',
  'doctor/customise-emr',
  'doctor/summary',
  'doctor-profile-picture/upload',
  'doctor-profile-picture-url',
  'doctor-customise-emr',
  'doctor-summary',

  // Lab Test APIs
  'lab-test/departments',
  'lab-tests',
  'lab-tests/search',
  'patient-lab-test',
  'patient-lab-test/details',
  'patient-lab-test/search',
  'lab-report-upload',
  'lab-report/preview',

  'lab-test-departments',
  'lab-tests-search',
  'patient-lab-test-details',
  'patient-lab-test-search',
  'lab-report-preview',

  // Medicine APIs
  'medicine',
  'medicine/search',

  'medicine-search',

  // Package APIs
  'test-package',
  'package/tests',
  'package/add-tests',
  'package/remove-test',
  'packages/user-specific',

  'package-tests',
  'package-add-tests',
  'package-remove-test',
  'packages-user-specific',

  // Prescription Package APIs
  'prescription-package',
  'prescription-package/medicines',
  'prescription-package/details',

  'prescription-package-medicines',
  'prescription-package-details',

  // Food/Nutrition APIs
  'food/list',
  'food/serving-unit',
  'patient/nutrition/summary',
  'patient/nutrition/average',
  'patient/nutrition/chart',
  'patient/nutrition/percentage-chart',

  'food-list',
  'food-serving-unit',
  'patient-nutrition-summary',
  'patient-nutrition-average',
  'patient-nutrition-chart',
  'patient-nutrition-percentage-chart',

  // Dashboard APIs
  'dashboard/summary',
  'dashboard-summary',

  // LOINC APIs
  'loinc/list',
  'loinc/update',
  'loinc/remove',
  'loinc/tests-for-organization',

  'loinc-list',
  'loinc-update',
  'loinc-remove',
  'loinc-tests-for-organization',

  // Proxy APIs
  'icd-proxy',
  'icd-both-proxy',
  'snomed-proxy',

  // Customization APIs
  'customise-emr',

  // Summary APIs
  'summary',

  // Lifestyle APIs
  'lifestyle/question',
  'lifestyle/ambient-listening',
  'lifestyle/summary',
  'patient/lifestyle',
  'patient/lifestyle/note',
  'patient/lifestyle/demographics',
  'patient/lifestyle/medical-history-addiction',

  'lifestyle-question',
  'lifestyle-ambient-listening',
  'lifestyle-summary',
  'patient-lifestyle',
  'patient-lifestyle-note',
  'patient-lifestyle-demographics',
  'patient-lifestyle-medical-history-addiction',

  // Physical Activity APIs
  'physical-activity/dashboard',
  'physical-activity-dashboard',

  // Payment APIs (read-only)
  'payments/details',
  'payments/organization',
  'payments/stats',
  'payments/search',

  'payments-details',
  'payments-organization',
  'payments-stats',
  'payments-search',

  // ABDM APIs
  'abdm',
  'abdm/initiate/aadhaar',
  'abdm-verify-otp',
  'abdm/details/by-number',
  'abdm/details/by-mobile',
  'abdm/verify-number',
  'abdm/verify-otp-fetch-details',
  'abdm/verify-otp-fetch-details-by-mobile',
  'abdm/webhook',

  'abdm',
  'abdm-initiate-aadhaar',
  'abdm-verify-otp',
  'abdm-details-by-number',
  'abdm-details-by-mobile',
  'abdm-verify-number',
  'abdm-verify-otp-fetch-details',
  'abdm-verify-otp-fetch-details-by-mobile',
  'abdm-webhook',

  // User Document APIs
  'user/document/upload',
  'user/list',
  'user/set-password',

  'user-document-upload',
  'user-list',
  'user-set-password',

  // Auth APIs
  'auth/login',
  'auth/reset-password',

  // Organization APIs (read-only)
  'organization/patients',
  'organization/medicines',
  'organization/medicines/update',
  'organization/medicines/remove',

  'organization-patients',
  'organization-medicines',
  'organization-medicines-update',
  'organization-medicines-remove',

  // Appointment APIs (read-only)
  'book-consultation/type',
  'book-consultation/queue',

  'book-consultation-type',
  'book-consultation-queue',

  // Patient APIs (read-only)
  'patient/consulting',
  'patient/history',
  'patient/vitals',
  'patient/diagnosis-notes',

  'patient-consulting',
  'patient-history',
  'patient-vitals',
  'patient-diagnosis-notes',

  // Permissions API (read-only)
  'permissions/api-list',
  'permissions-api-list',

  // Payment Creation APIs
  'payments/verify',
  'payments/webhook',

  'payments-verify',
  'payments-webhook',

  //role
  'list-roles',
  'role',
  'summary',
  'test-package',
]

module.exports = {
  COMMON_APIS,
}
