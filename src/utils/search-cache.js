/**
 * In-Memory Search Cache for Lab Tests
 * Provides caching for frequently searched lab test queries to improve performance
 */

class SearchCache {
  constructor(maxSize = 1000, ttlMinutes = 30) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.ttl = ttlMinutes * 60 * 1000 // Convert to milliseconds
    this.hitCount = 0
    this.missCount = 0
    
    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000)
  }

  /**
   * Generate cache key from search parameters
   */
  generateKey(organizationId, searchText, department, pageSize, continuationToken) {
    const normalizedSearchText = (searchText || '').toLowerCase().trim()
    const normalizedDepartment = department || 'ALL'
    const normalizedPageSize = pageSize || 10
    const normalizedToken = continuationToken || 'null'
    
    return `${organizationId}:${normalizedSearchText}:${normalizedDepartment}:${normalizedPageSize}:${normalizedToken}`
  }

  /**
   * Get cached result
   */
  get(organizationId, searchText, department, pageSize, continuationToken) {
    const key = this.generateKey(organizationId, searchText, department, pageSize, continuationToken)
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.missCount++
      return null
    }
    
    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      this.missCount++
      return null
    }
    
    this.hitCount++
    console.log(`🎯 Cache hit for search: "${searchText}" (${this.getHitRate()}% hit rate)`)
    return entry.data
  }

  /**
   * Set cached result
   */
  set(organizationId, searchText, department, pageSize, continuationToken, data) {
    // Don't cache if search text is too short (likely to change frequently)
    if (!searchText || searchText.trim().length < 2) {
      return
    }
    
    const key = this.generateKey(organizationId, searchText, department, pageSize, continuationToken)
    
    // Remove oldest entry if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    const entry = {
      data: data,
      createdAt: Date.now(),
      expiresAt: Date.now() + this.ttl,
    }
    
    this.cache.set(key, entry)
    console.log(`💾 Cached search result for: "${searchText}" (cache size: ${this.cache.size})`)
  }

  /**
   * Clear cache for specific organization
   */
  clearOrganization(organizationId) {
    let cleared = 0
    for (const [key, entry] of this.cache.entries()) {
      if (key.startsWith(`${organizationId}:`)) {
        this.cache.delete(key)
        cleared++
      }
    }
    console.log(`🧹 Cleared ${cleared} cache entries for organization: ${organizationId}`)
  }

  /**
   * Clear all cache
   */
  clear() {
    const size = this.cache.size
    this.cache.clear()
    this.hitCount = 0
    this.missCount = 0
    console.log(`🧹 Cleared all ${size} cache entries`)
  }

  /**
   * Clean up expired entries
   */
  cleanup() {
    const now = Date.now()
    let cleaned = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key)
        cleaned++
      }
    }
    
    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} expired cache entries`)
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalRequests = this.hitCount + this.missCount
    const hitRate = totalRequests > 0 ? ((this.hitCount / totalRequests) * 100).toFixed(2) : 0
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: `${hitRate}%`,
      ttlMinutes: this.ttl / (60 * 1000),
    }
  }

  /**
   * Get hit rate percentage
   */
  getHitRate() {
    const totalRequests = this.hitCount + this.missCount
    return totalRequests > 0 ? ((this.hitCount / totalRequests) * 100).toFixed(1) : 0
  }

  /**
   * Check if cache should be used for this search
   */
  shouldCache(searchText) {
    // Don't cache very short searches or empty searches
    if (!searchText || searchText.trim().length < 2) {
      return false
    }
    
    // Don't cache searches with special characters that might be dynamic
    if (/[<>{}[\]\\|`~]/.test(searchText)) {
      return false
    }
    
    return true
  }
}

// Create singleton instance
const searchCache = new SearchCache(1000, 30)

// Log cache stats every 10 minutes
setInterval(() => {
  const stats = searchCache.getStats()
  console.log('📊 Search Cache Stats:', stats)
}, 10 * 60 * 1000)

module.exports = searchCache
