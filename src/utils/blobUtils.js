const {
  BlobServiceClient,
  StorageSharedKeyCredential,
  BlobSASPermissions,
  generateBlobSASQueryParameters,
} = require('@azure/storage-blob')
const secretManager = require('../services/secret-manager')
const { logError, logInfo } = require('../common/logging')

// Initialize Blob Service Client with secrets from Key Vault
let blobServiceClient = null
let sharedKeyCredential = null
let isInitialized = false
let initializationPromise = null

async function initializeBlobService() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logInfo('Initializing Blob Service with Key Vault secrets...')

    // Get secrets from Key Vault or fallback to environment variables
    const connectionString = await secretManager.getSecret(
      'AzureWebJobsStorage',
    )
    const accountName = await secretManager.getSecret(
      'AZURE_STORAGE_ACCOUNT_NAME',
    )
    const accountKey = await secretManager.getSecret(
      'AZURE_STORAGE_ACCOUNT_KEY',
    )

    if (!connectionString) {
      throw new Error(
        'Missing Azure Storage connection string in Key Vault or environment variables',
      )
    }

    // Create BlobServiceClient using connection string
    blobServiceClient = BlobServiceClient.fromConnectionString(connectionString)

    // Create shared key credential for SAS generation
    if (accountName && accountKey) {
      sharedKeyCredential = new StorageSharedKeyCredential(
        accountName,
        accountKey,
      )
    } else {
      logError('Missing Azure Storage account name or key for SAS generation')
    }

    isInitialized = true
    logInfo('Blob Service initialized successfully')
  } catch (error) {
    logError('Failed to initialize Blob Service:', error)
    isInitialized = false
    throw error
  }
}

/**
 * Get a container client.
 */
const getContainerClient = async (containerName) => {
  // Ensure blob service is initialized
  if (!isInitialized) {
    await initializeBlobService()
  }
  return blobServiceClient.getContainerClient(containerName)
}

/**
 * Find a blob by prefix.
 */
const findBlobByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = await getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(`Container ${containerName} does not exist`)
      return null
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    for await (const blob of blobs) {
      return blob.name
    }
    return null
  } catch (error) {
    console.error(
      `Error finding blob with prefix ${prefix} in container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Delete blobs by prefix.
 */
const deleteBlobsByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = await getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(
        `Container ${containerName} does not exist, nothing to delete`,
      )
      return
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    let deletedCount = 0

    for await (const blob of blobs) {
      const blockBlobClient = containerClient.getBlockBlobClient(blob.name)
      const deleteResult = await blockBlobClient.deleteIfExists()
      if (deleteResult.succeeded) {
        deletedCount++
      }
    }

    console.log(
      `Deleted ${deletedCount} blobs with prefix ${prefix} from container ${containerName}`,
    )
  } catch (error) {
    console.error(
      `Error deleting blobs with prefix ${prefix} from container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Generate SAS URL for a blob with read permissions
 */
const generateBlobSASUrl = async (
  containerName,
  blobName,
  expiryHours = 24,
) => {
  try {
    // Ensure blob service is initialized
    if (!isInitialized) {
      await initializeBlobService()
    }

    if (!sharedKeyCredential) {
      throw new Error('Shared key credential not available for SAS generation')
    }

    const accountName = await secretManager.getSecret(
      'AZURE_STORAGE_ACCOUNT_NAME',
    )
    if (!accountName) {
      throw new Error('Azure Storage account name not available')
    }

    const sasOptions = {
      containerName,
      blobName,
      permissions: BlobSASPermissions.parse('r'), // Read permission
      startsOn: new Date(),
      expiresOn: new Date(new Date().valueOf() + expiryHours * 60 * 60 * 1000), // Default 24 hours
    }

    const sasToken = generateBlobSASQueryParameters(
      sasOptions,
      sharedKeyCredential,
    ).toString()
    return `https://${accountName}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`
  } catch (error) {
    logError('Error generating SAS URL:', error.message)
    throw error
  }
}

/**
 * Upload a blob.
 */
const uploadBlob = async (containerName, blobName, buffer) => {
  const containerClient = await getContainerClient(containerName)
  // Create container without public access (private by default)
  await containerClient.createIfNotExists()
  const blockBlobClient = containerClient.getBlockBlobClient(blobName)
  await blockBlobClient.uploadData(buffer)

  // Return SAS URL instead of direct blob URL for secure access
  return generateBlobSASUrl(containerName, blobName)
}

module.exports = {
  getContainerClient,
  findBlobByPrefix,
  deleteBlobsByPrefix,
  uploadBlob,
  generateBlobSASUrl,
}
