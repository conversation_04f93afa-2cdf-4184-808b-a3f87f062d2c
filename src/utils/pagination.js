/**
 * Paginate an array based on page size and page number.
 * @param {Array} items - The array to paginate.
 * @param {number} pageSize - Number of items per page.
 * @param {number} page - Current page number.
 * @returns {Object} - Paginated data with metadata.
 */
function paginate(items, pageSize, page) {
  const totalItemCount = items.length
  const totalPages = Math.ceil(totalItemCount / pageSize)
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize

  const paginatedItems = items.slice(startIndex, endIndex)

  return {
    items: paginatedItems,
    totalItemCount,
    currentPage: page,
    totalPages,
  }
}

module.exports = { paginate }
