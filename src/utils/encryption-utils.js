const { encryptData, decryptData, encryptDataWithRandomIV, decryptDataWithRandomIV } = require('../common/helper');
const _ = require('lodash');

class EncryptionUtils {
  // New methods using random IV for enhanced security
  static encryptSensitiveFieldsSecure(data, sensitiveFields) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string' && value.trim() !== '') {
              _.set(result, `${arrayField}[${index}].${subField}`, encryptDataWithRandomIV(value));
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string' && value.trim() !== '') {
          _.set(result, field, encryptDataWithRandomIV(value));
        }
      }
    });

    return result;
  }

  static decryptSensitiveFieldsSecure(data, sensitiveFields, userPermissions = [], fieldPermissionMapping = {}) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      const requiredPermission = fieldPermissionMapping[field];
      const hasPermission = !requiredPermission || 
        userPermissions.includes(requiredPermission) ||
        userPermissions.includes('emr.patientinfo.view.sensitive'); // Master permission

      if (!hasPermission) {
        this.maskSensitiveField(result, field);
        return;
      }

      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string') {
              const decryptedValue = this.decryptFieldValue(value);
              _.set(result, `${arrayField}[${index}].${subField}`, decryptedValue);
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string') {
          const decryptedValue = this.decryptFieldValue(value);
          _.set(result, field, decryptedValue);
        }
      }
    });

    return result;
  }

  // Legacy methods for backward compatibility
  static encryptSensitiveFields(data, sensitiveFields) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string' && value.trim() !== '') {
              _.set(result, `${arrayField}[${index}].${subField}`, encryptData(value));
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string' && value.trim() !== '') {
          _.set(result, field, encryptData(value));
        }
      }
    });

    return result;
  }

  static decryptSensitiveFields(data, sensitiveFields, userPermissions = [], fieldPermissionMapping = {}) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      const requiredPermission = fieldPermissionMapping[field];
      const hasPermission = !requiredPermission || 
        userPermissions.includes(requiredPermission) ||
        userPermissions.includes('emr.patientinfo.view.sensitive'); // Master permission

      if (!hasPermission) {
        this.maskSensitiveField(result, field);
        return;
      }

      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string') {
              const decryptedValue = this.decryptFieldValue(value);
              _.set(result, `${arrayField}[${index}].${subField}`, decryptedValue);
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string') {
          const decryptedValue = this.decryptFieldValue(value);
          _.set(result, field, decryptedValue);
        }
      }
    });

    return result;
  }

  static decryptFieldValue(value) {
    if (!value || typeof value !== 'string') {
      return value;
    }

    // Try new method with random IV first
    try {
      return decryptDataWithRandomIV(value);
    } catch (error) {
      // If new method fails, try legacy method
      try {
        return decryptData(value);
      } catch (legacyError) {
        // If both methods fail, assume it's plain text and return as-is
        return value;
      }
    }
  }

  static maskSensitiveField(data, field) {
    if (field.includes("[]")) {
      const [arrayField, subField] = field.split("[]");
      const array = _.get(data, arrayField, []);
      if (Array.isArray(array)) {
        array.forEach((item, index) => {
          const value = _.get(item, subField);
          if (value) {
            _.set(data, `${arrayField}[${index}].${subField}`, '******');
          }
        });
      }
    } else {
      const value = _.get(data, field);
      if (value) {
        _.set(data, field, '******');
      }
    }
  }
  static getMaskedValue(value) {
    if (!value || typeof value !== 'string') {
      return value;
    }
    
    return '******';
  }

  static shouldEncryptField(fieldPath, encryptedFields) {
    return encryptedFields.includes(fieldPath);
  }
}

module.exports = EncryptionUtils;
