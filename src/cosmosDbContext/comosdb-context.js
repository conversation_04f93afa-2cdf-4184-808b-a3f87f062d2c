const { CosmosClient } = require('@azure/cosmos')
const secretManager = require('../services/secret-manager')
const { logError, logInfo } = require('../common/logging')

// Initialize Cosmos DB client with secrets from Key Vault
let client = null
let databaseId = null
let isInitialized = false
let initializationPromise = null

async function initializeCosmosClient() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logInfo('Initializing Cosmos DB client with Key Vault secrets...')

    // Get secrets from Key Vault or fallback to environment variables
    const connectionString = await secretManager.getSecret(
      'COSMOS_DB_CONNECTIONSTRING',
    )
    databaseId = await secretManager.getSecret('COSMOS_DB_DATABASE')

    if (!connectionString) {
      throw new Error(
        'COSMOS_DB_CONNECTIONSTRING not found in Key Vault or environment variables',
      )
    }

    // Configure HTTP agent for connection pooling and keep-alive
    const https = require('https')
    const http = require('http')
    
    const agentOptions = {
      keepAlive: true,
      keepAliveMsecs: 30000, // Send keep-alive probes every 30 seconds
      maxSockets: 50, // Maximum concurrent connections
      maxFreeSockets: 10, // Keep 10 idle connections open
      timeout: 60000, // Socket timeout: 60 seconds
      scheduling: 'fifo', // First-in-first-out scheduling
    }

    // Create agents for both HTTP and HTTPS
    const httpAgent = new http.Agent(agentOptions)
    const httpsAgent = new https.Agent(agentOptions)

    // Check if connection string is in AccountEndpoint format or simple format
    const isAccountEndpointFormat = connectionString.includes('AccountEndpoint=')
    
    let clientOptions
    if (isAccountEndpointFormat) {
      // Parse AccountEndpoint=...;AccountKey=... format
      const endpoint = connectionString.split('AccountEndpoint=')[1].split(';')[0]
      const key = connectionString.split('AccountKey=')[1].split(';')[0]
      
      clientOptions = {
        endpoint,
        key,
        connectionPolicy: {
          requestTimeout: 60000, // Request timeout: 60 seconds
          enableEndpointDiscovery: true,
          preferredLocations: [], // Let SDK choose optimal location
          retryOptions: {
            maxRetryAttemptCount: 5, // Retry up to 5 times on network failures
            fixedRetryIntervalInMs: 2000, // 2 second delay between retries
            maxRetryWaitTimeInSeconds: 30, // Maximum total retry time
          },
        },
        agent: httpsAgent, // Use custom HTTPS agent with connection pooling
      }
    } else {
      // Simple connection string format - use as-is with agent configuration
      clientOptions = connectionString
      // For simple connection string, we need to create client first, then configure
      // Unfortunately, we can't pass agent to simple string format
      // So we'll use the string directly but log a warning
      logInfo('Using simple connection string format. Advanced connection pooling may not be available.')
    }

    // Initialize Cosmos Client with robust connection configuration
    client = new CosmosClient(clientOptions)

    // Handle emulator mode - this is a local development flag, not a secret
    const cosmos_running_mode = await secretManager.getSecret(
      'cosmos_running_mode',
      process.env.cosmos_running_mode,
    )
    if (cosmos_running_mode && cosmos_running_mode == 'emulator') {
      process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = '0'
    }

    isInitialized = true
    logInfo('Cosmos DB client initialized successfully')
  } catch (error) {
    logError('Failed to initialize Cosmos DB client:', error)
    isInitialized = false
    throw error
  }
}

async function getCosmosClient() {
  if (!isInitialized) {
    await initializeCosmosClient()
  }
  return client
}

async function getDatabaseId() {
  if (!isInitialized) {
    await initializeCosmosClient()
  }
  return databaseId
}
// Container cache to avoid repeated metadata requests
const containerCache = new Map()
const containerPendingPromises = new Map() // Track pending container initializations
const databaseInitialized = { value: false }

// Metadata request semaphore to limit concurrent container access
let metadataRequestCount = 0
const MAX_METADATA_REQUESTS = 2

const getContainer = async (containerId) => {
  // Return cached container if available
  if (containerCache.has(containerId)) {
    return containerCache.get(containerId)
  }

  // If there's already a pending initialization for this container, wait for it
  if (containerPendingPromises.has(containerId)) {
    return containerPendingPromises.get(containerId)
  }

  // Create a promise for this container initialization
  const initPromise = _initializeContainer(containerId)
  containerPendingPromises.set(containerId, initPromise)

  try {
    const container = await initPromise
    return container
  } finally {
    // Clean up pending promise after resolution
    containerPendingPromises.delete(containerId)
  }
}

const _initializeContainer = async (containerId) => {
  // Ensure Cosmos client is initialized
  const cosmosClient = await getCosmosClient()
  const dbId = await getDatabaseId()

  // Double-check cache after acquiring initialization rights
  if (containerCache.has(containerId)) {
    return containerCache.get(containerId)
  }

  // Wait if too many metadata requests are in progress
  while (metadataRequestCount >= MAX_METADATA_REQUESTS) {
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  try {
    metadataRequestCount++

    if (!databaseInitialized.value) {
      await cosmosClient.databases.createIfNotExists({
        id: dbId,
      })
      databaseInitialized.value = true
    }

    // Determine partition key based on container
    // organization_subscriptions uses organizationId as partition key
    // All other containers use id as partition key
    const partitionKey = containerId === 'organization_subscriptions' ? '/organizationId' : '/id'
    
    // Initialize container only once per container
    await cosmosClient.database(dbId).containers.createIfNotExists({
      id: containerId,
      partitionKey: partitionKey,
      throughput: 400,
    })

    const container = cosmosClient.database(dbId).container(containerId)

    // Cache the container reference
    containerCache.set(containerId, container)

    console.log(`Container ${containerId} cached successfully`)
    return container
  } catch (error) {
    console.error(`Error getting container ${containerId}:`, error)
    throw error
  } finally {
    metadataRequestCount--
  }
}

const cosmosDbContext = {
  createItem: async (item, containerId, retryCount = 0) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    item.created_on = curDate
    item.updated_on = curDate

    try {
      const { resource } = await container.items.create(item)
      return resource
    } catch (error) {
      // Handle 429 rate limiting with exponential backoff
      if (error.code === 429 && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000 // 1s, 2s, 4s + jitter
        console.log(
          `Rate limited on create, retrying in ${delay}ms (attempt ${
            retryCount + 1
          }/3)`,
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
        return module.exports.createItem(item, containerId, retryCount + 1)
      }
      throw error
    }
  },

  readItem: async (id, partitionKey, containerId) => {
    const container = await getContainer(containerId)
    const { resource } = await container.item(id, partitionKey).read()
    return resource
  },

  queryItems: async (sqlQuery, containerId) => {
    const container = await getContainer(containerId)
    let querySpec

    // Support either raw SQL string or { query, parameters } object
    if (typeof sqlQuery === 'string') {
      const q = sqlQuery.trim()
      if (!q) {
        throw new Error('Invalid query provided to queryItems: empty string')
      }
      querySpec = { query: q }
    } else if (sqlQuery && typeof sqlQuery === 'object') {
      if (
        !sqlQuery.query ||
        typeof sqlQuery.query !== 'string' ||
        !sqlQuery.query.trim()
      ) {
        throw new Error('Invalid query object: missing non-empty query string')
      }
      querySpec = { query: sqlQuery.query.trim() }
      if (
        Array.isArray(sqlQuery.parameters) &&
        sqlQuery.parameters.length > 0
      ) {
        querySpec.parameters = sqlQuery.parameters
      }
    } else {
      throw new Error('Invalid query provided to queryItems')
    }

    const { resources: items } = await container.items
      .query(querySpec)
      .fetchAll()
    return items
  },

  getAllItems: async (containerId, pageSize = 10, continuationToken = null) => {
    const container = await getContainer(containerId)
    const iterator = continuationToken
      ? container.items.readAll({
          maxItemCount: pageSize,
          continuationToken: continuationToken,
        })
      : container.items.readAll({
          maxItemCount: pageSize,
        })
    const { resources: items, continuationToken: nextToken } =
      await iterator.fetchNext()
    return { items, nextToken }
  },

  getAllItemQuery: async (
    containerId,
    queryString,
    pageSize = 10,
    continuationToken = null,
  ) => {
    const container = await getContainer(containerId)

    const querySpec = {
      query: queryString,
    }

    const requestOptions = {
      maxItemCount: pageSize,
    }

    if (continuationToken) {
      requestOptions.continuationToken = continuationToken
    }

    const iterator = container.items.query(querySpec, requestOptions)
    const {
      resources: items,
      headers,
      continuationToken: responseContinuationToken,
    } = await iterator.fetchNext()

    // Try multiple ways to get the continuation token
    const nextToken =
      responseContinuationToken ||
      headers?.['x-ms-continuation'] ||
      headers?.continuationToken
    console.log('kkkk', nextToken)

    return { items, nextToken, continuationToken: nextToken }
  },
  getAllItemQueryWithPagination: async (
    containerId,
    queryString,
    pageSize = 10,
    continuationToken = null,
  ) => {
    try {
      const container = await getContainer(containerId)

      const querySpec = {
        query: queryString,
      }

      const requestOptions = {
        maxItemCount: pageSize,
      }

      if (continuationToken & (continuationToken != null)) {
        requestOptions.continuationToken = continuationToken
      }

      const queryIterator = container.items.query(querySpec, requestOptions)

      const {
        resources,
        headers,
        hasMoreResults,
        continuationToken: responseContinuationToken,
      } = await queryIterator.fetchNext()

      let nextToken = null
      const iteratorHasMore =
        typeof queryIterator.hasMoreResults === 'function'
          ? queryIterator.hasMoreResults()
          : hasMoreResults

      if (iteratorHasMore || hasMoreResults) {
        nextToken = responseContinuationToken || headers?.['x-ms-continuation']

        if (!nextToken && iteratorHasMore) {
          if (resources && resources.length > 0) {
            const lastItem = resources[resources.length - 1]
            nextToken = `cursor_${lastItem.createdAt || lastItem._ts}`
          }
        }
      }

      return {
        items: resources || [],
        nextToken,
        continuationToken: nextToken,
        hasMoreResults: iteratorHasMore,
      }
    } catch (error) {
      console.error('Error in getAllItemQuery:', error)
      throw error
    }
  },

  updateItem: async (item, containerId, retryCount = 0) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    item.updated_on = curDate

    try {
      const { resource } = await container.items.upsert(item)
      return resource
    } catch (error) {
      // Handle 429 rate limiting with exponential backoff
      if (error.code === 429 && retryCount < 3) {
        const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000 // 1s, 2s, 4s + jitter
        console.log(
          `Rate limited, retrying in ${delay}ms (attempt ${retryCount + 1}/3)`,
        )
        await new Promise((resolve) => setTimeout(resolve, delay))
        return module.exports.updateItem(item, containerId, retryCount + 1)
      }
      throw error
    }
  },

  upsertItem: async (id, data, containerId) => {
    const container = await getContainer(containerId)
    var curDate = new Date().toISOString()
    data.updated_on = curDate
    data.id = id
    const { resource } = await container.items.upsert(data)
    return resource
  },
  patchItem: async (id, updateData, containerId) => {
    const container = await getContainer(containerId)
    const { resource: existingItem } = await container.item(id, id).read()
    if (!existingItem) {
      throw new Error(`Item with id ${id} not found`)
    }

    updateData.updated_on = new Date().toISOString()

    const patchOperations = []
    for (const key in updateData) {
      const operation = existingItem.hasOwnProperty(key) ? 'replace' : 'add'
      patchOperations.push({
        op: operation,
        path: `/${key}`,
        value: updateData[key],
      })
    }

    if (containerId === 'DoctorCustomiseEmrs') {
      const chunkArray = (arr, size) => {
        const result = []
        for (let i = 0; i < arr.length; i += size) {
          result.push(arr.slice(i, i + size))
        }
        return result
      }

      const patchChunks = chunkArray(patchOperations, 10)
      let updatedItem = existingItem

      for (const chunk of patchChunks) {
        const { resource } = await container.item(id, id).patch(chunk)
        updatedItem = resource
      }

      return updatedItem
    } else {
      const { resource: updatedItem } = await container
        .item(id, id)
        .patch(patchOperations)
      return updatedItem
    }
  },
  deleteItem: async (id, partitionKey, containerId) => {
    const container = await getContainer(containerId)
    const { resource } = await container.item(id, partitionKey).delete()
    return resource
  },

  // Environment-aware configuration optimized for your RU/s limits
  getEnvironmentConfig: () => {
    const environment = process.env.NODE_ENV || 'development'
    const isProduction = environment === 'production'

    return {
      // Production: 400-4000 RU/s, Development: 100-1000 RU/s
      maxConcurrency: isProduction ? 10 : 3, // Reduced from 6 to 3 for dev/QA
      batchSize: isProduction ? 1000 : 500,
      maxRetries: isProduction ? 5 : 7,
      retryDelayMs: isProduction ? 300 : 500,
      delayBetweenBatches: isProduction ? 25 : 100, // Increased from 50 to 100ms for dev/QA
      smallDatasetThreshold: isProduction ? 3000 : 1500,
      largeDatasetThreshold: isProduction ? 15000 : 8000,
      environment: isProduction ? 'PRODUCTION' : 'DEVELOPMENT',
    }
  },
}

module.exports = cosmosDbContext
