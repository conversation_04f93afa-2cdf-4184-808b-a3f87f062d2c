const { HttpResponse } = require('@azure/functions')
const crypto = require('crypto')
const secretManager = require('../services/secret-manager')

// Secret key will be loaded from ENCRYPTION_SECRET_KEY environment variable (or default value if not set)
// NOTE: ENCRYPTION_SECRET_KEY was never stored in Key Vault originally, so we don't fetch it from there
function getSecretKeyFromEnvOrDefault() {
  const envKey = process.env.ENCRYPTION_SECRET_KEY
  if (!envKey) {
    return Buffer.from('a1b2c3d4e5f67890a1b2c3d4e5f67890', 'utf8') // default (32 chars)
  }

  // If hex (64 chars), parse as hex
  if (/^[0-9a-fA-F]{64}$/.test(envKey)) {
    return Buffer.from(envKey, 'hex')
  }

  // Try base64
  try {
    const b = Buffer.from(envKey, 'base64')
    if (b.length === 32) return b
  } catch (e) {
    // ignore
  }

  // Otherwise use utf8, pad/truncate to 32 bytes
  const utf8 = Buffer.from(envKey, 'utf8')
  if (utf8.length === 32) return utf8
  if (utf8.length < 32) {
    const padded = Buffer.alloc(32)
    utf8.copy(padded)
    return padded
  }
  return utf8.slice(0, 32)
}
// Initialize secret key asynchronously
let secretKey = null
let isSecretKeyInitialized = false

function initializeSecretKey() {
  if (!isSecretKeyInitialized) {
    secretKey = getSecretKeyFromEnvOrDefault()
    isSecretKeyInitialized = true
  }
  return secretKey
}

const iv = Buffer.alloc(16, 0) // Fixed 16-byte IV for deterministic encryption

const helper = {
  parseJSON: (jsonString) => {
    try {
      if (typeof jsonString == 'object') {
        return jsonString
      }

      // Handle null, undefined, or non-string values
      if (!jsonString || typeof jsonString !== 'string') {
        return jsonString || ''
      }

      // Clean up the JSON string to handle common issues
      let cleanedString = jsonString.trim()

      // Handle markdown code blocks and extract JSON
      if (cleanedString.includes('```json')) {
        // Extract JSON from markdown code block
        const jsonMatch = cleanedString.match(/```json\s*([\s\S]*?)(?:```|$)/)
        if (jsonMatch) {
          cleanedString = jsonMatch[1].trim()
        } else {
          // Fallback: remove markdown markers
          cleanedString = cleanedString
            .replace(/^```json\s*/, '')
            .replace(/\s*```$/, '')
        }
      }

      // Fix common JSON issues before parsing
      cleanedString = helper.fixJSONString(cleanedString)

      var obj = JSON.parse(cleanedString)
      return obj
    } catch (error) {
      console.error(
        'Unable to parse json string to object :: ' + jsonString,
        error,
      )

      // Try to salvage what we can from malformed JSON
      try {
        return helper.salvageJSONData(jsonString)
      } catch (salvageError) {
        console.error('Failed to salvage JSON data:', salvageError)
        return ''
      }
    }
  },

  fixJSONString: (jsonString) => {
    let fixed = jsonString

    // First, try to fix common issues with message content
    // Handle cases where quotes inside message content are not properly escaped
    fixed = helper.fixMessageQuotes(fixed)

    // Fix incomplete JSON by finding the last complete structure
    if (!fixed.endsWith('}') && !fixed.endsWith(']')) {
      let lastCompleteIndex = -1
      let braceCount = 0
      let bracketCount = 0
      let inString = false
      let escapeNext = false

      for (let i = 0; i < fixed.length; i++) {
        const char = fixed[i]

        if (escapeNext) {
          escapeNext = false
          continue
        }

        if (char === '\\') {
          escapeNext = true
          continue
        }

        if (char === '"' && !escapeNext) {
          inString = !inString
          continue
        }

        if (!inString) {
          if (char === '{') braceCount++
          else if (char === '}') {
            braceCount--
            if (braceCount === 0 && bracketCount === 0) {
              lastCompleteIndex = i
            }
          } else if (char === '[') bracketCount++
          else if (char === ']') {
            bracketCount--
            if (braceCount === 0 && bracketCount === 0) {
              lastCompleteIndex = i
            }
          }
        }
      }

      if (lastCompleteIndex > -1) {
        fixed = fixed.substring(0, lastCompleteIndex + 1)
      } else {
        // If no complete structure found, try to fix truncated JSON
        fixed = helper.fixTruncatedJSON(fixed)
      }
    }

    return fixed
  },

  fixTruncatedJSON: (jsonString) => {
    let fixed = jsonString.trim()

    // Handle markdown code blocks first
    if (fixed.includes('```json')) {
      const jsonMatch = fixed.match(/```json\s*([\s\S]*?)(?:```|$)/)
      if (jsonMatch) {
        fixed = jsonMatch[1].trim()
      }
    }

    // Find the last complete, valid JSON structure
    let bestValidJson = ''
    let maxValidLength = 0

    // Try to find the longest valid JSON by working backwards from different positions
    for (let endPos = fixed.length; endPos > 100; endPos -= 50) {
      let testJson = fixed.substring(0, endPos)

      // Try to complete this JSON fragment
      let completed = helper.completeJSONStructure(testJson)

      try {
        JSON.parse(completed)
        if (completed.length > maxValidLength) {
          maxValidLength = completed.length
          bestValidJson = completed
        }
      } catch (e) {
        // This position doesn't work, continue
      }
    }

    return bestValidJson || helper.completeJSONStructure(fixed)
  },

  completeJSONStructure: (jsonString) => {
    let fixed = jsonString.trim()
    let inString = false
    let escapeNext = false
    let braceCount = 0
    let bracketCount = 0
    let lastCompletePosition = -1
    let lastCommaPosition = -1

    // Analyze the JSON structure
    for (let i = 0; i < fixed.length; i++) {
      const char = fixed[i]

      if (escapeNext) {
        escapeNext = false
        continue
      }

      if (char === '\\') {
        escapeNext = true
        continue
      }

      if (char === '"' && !escapeNext) {
        inString = !inString
        continue
      }

      if (!inString) {
        if (char === '{') {
          braceCount++
        } else if (char === '}') {
          braceCount--
          if (braceCount >= 0) {
            lastCompletePosition = i
          }
        } else if (char === '[') {
          bracketCount++
        } else if (char === ']') {
          bracketCount--
          if (bracketCount >= 0) {
            lastCompletePosition = i
          }
        } else if (char === ',') {
          lastCommaPosition = i
        }
      }
    }

    // If we're in the middle of a string, truncate to last complete field
    if (inString) {
      // Find the last complete field
      let truncateAt = Math.max(lastCompletePosition, lastCommaPosition)
      if (truncateAt > 0) {
        fixed = fixed.substring(0, truncateAt)
        // Remove trailing comma if we truncated at a comma
        if (fixed.endsWith(',')) {
          fixed = fixed.slice(0, -1)
        }
      }
    }

    // Remove any incomplete property at the end
    fixed = fixed.replace(/,\s*"[^"]*"?\s*:\s*[^,}]*$/, '')
    fixed = fixed.replace(/,\s*"[^"]*"?\s*$/, '')

    // Close any open structures
    while (braceCount > 0) {
      fixed += '}'
      braceCount--
    }
    while (bracketCount > 0) {
      fixed += ']'
      bracketCount--
    }

    return fixed
  },

  fixMessageQuotes: (jsonString) => {
    // Use a character-by-character approach to fix quotes in message content
    let result = ''
    let i = 0

    while (i < jsonString.length) {
      // Look for "message": "
      const messageStart = jsonString.indexOf('"message":', i)
      if (messageStart === -1) {
        // No more message fields, append the rest
        result += jsonString.substring(i)
        break
      }

      // Add everything up to the message field
      result += jsonString.substring(i, messageStart)

      // Find the start of the message value
      let valueStart = jsonString.indexOf('"', messageStart + 10) // 10 = length of '"message":'
      if (valueStart === -1) {
        result += jsonString.substring(messageStart)
        break
      }

      // Add the message field name and opening quote
      result += jsonString.substring(messageStart, valueStart + 1)

      // Process the message content character by character
      let messageContent = ''
      let j = valueStart + 1
      let foundEnd = false

      while (j < jsonString.length && !foundEnd) {
        const char = jsonString[j]

        if (char === '"') {
          // Check if this quote is escaped
          let backslashCount = 0
          let k = j - 1
          while (k >= valueStart + 1 && jsonString[k] === '\\') {
            backslashCount++
            k--
          }

          // If even number of backslashes (including 0), the quote is not escaped
          if (backslashCount % 2 === 0) {
            // Check if this is the end of the message by looking ahead
            let nextChar = j + 1
            while (
              nextChar < jsonString.length &&
              /\s/.test(jsonString[nextChar])
            ) {
              nextChar++
            }

            if (
              nextChar < jsonString.length &&
              (jsonString[nextChar] === ',' || jsonString[nextChar] === '}')
            ) {
              // This is the end quote
              foundEnd = true
              result += messageContent + '"'
              i = j + 1
            } else {
              // This is an unescaped quote in the middle, escape it
              messageContent += '\\"'
            }
          } else {
            // Quote is already escaped, keep it
            messageContent += char
          }
        } else {
          messageContent += char
        }

        if (!foundEnd) {
          j++
        }
      }

      if (!foundEnd) {
        // Reached end of string without finding closing quote
        result += messageContent
        break
      }
    }

    return result
  },

  salvageJSONData: (jsonString) => {
    // Try to extract conversation data even from malformed JSON
    try {
      // Handle null, undefined, or non-string values
      if (!jsonString || typeof jsonString !== 'string') {
        return []
      }

      // First, try to find and extract the conversation array
      const arrayStart = jsonString.indexOf('[')
      if (arrayStart === -1) {
        return []
      }

      // Find the matching closing bracket
      let arrayEnd = -1
      let bracketCount = 0
      let inString = false
      let escapeNext = false

      for (let i = arrayStart; i < jsonString.length; i++) {
        const char = jsonString[i]

        if (escapeNext) {
          escapeNext = false
          continue
        }

        if (char === '\\') {
          escapeNext = true
          continue
        }

        if (char === '"' && !escapeNext) {
          inString = !inString
          continue
        }

        if (!inString) {
          if (char === '[') {
            bracketCount++
          } else if (char === ']') {
            bracketCount--
            if (bracketCount === 0) {
              arrayEnd = i
              break
            }
          }
        }
      }

      if (arrayEnd !== -1) {
        let arrayString = jsonString.substring(arrayStart, arrayEnd + 1)

        // Try to fix the array string
        arrayString = helper.fixMessageQuotes(arrayString)

        try {
          return JSON.parse(arrayString)
        } catch (e) {
          // If parsing still fails, try to extract individual objects
          return helper.extractConversationObjects(arrayString)
        }
      }

      // If we can't find a proper array, try to extract individual objects
      return helper.extractConversationObjects(jsonString)
    } catch (error) {
      console.error('Error in salvageJSONData:', error)
      return []
    }
  },

  extractConversationObjects: (text) => {
    const conversations = []

    // Look for speaker/message patterns
    const patterns = [
      /\{\s*"speaker"\s*:\s*"([^"]+)"\s*,\s*"message"\s*:\s*"([^"]*(?:\\.[^"]*)*)"\s*\}/g,
      /\{\s*"speaker"\s*:\s*"([^"]+)"\s*,\s*"message"\s*:\s*"([^}]*)"\s*\}/g,
    ]

    for (const pattern of patterns) {
      let match
      while ((match = pattern.exec(text)) !== null) {
        conversations.push({
          speaker: match[1],
          message: match[2].replace(/\\"/g, '"'), // Unescape quotes
        })
      }

      if (conversations.length > 0) {
        break // If we found matches with one pattern, use those
      }
    }

    // If no structured objects found, try to extract from plain text
    if (conversations.length === 0) {
      // Look for speaker: message patterns in plain text
      const lines = text.split('\n')
      for (const line of lines) {
        const speakerMatch = line.match(/^(patient|doctor):\s*(.+)$/i)
        if (speakerMatch) {
          conversations.push({
            speaker: speakerMatch[1].toLowerCase(),
            message: speakerMatch[2].trim(),
          })
        }
      }
    }

    return conversations
  },

  validateMedicalRecord: async (obj) => {
    const summaryInfo = await secretManager.getSecret('SummaryInfo', '')
    const newKeysArray = summaryInfo.split(',').map((key) => key.trim())
    // Add the additional required fields
    const additionalFields = [
      'vitals',
      'anthropometry',
      'generalphysicalexamination',
      'heent',
      'systemicexamination',
    ]
    const allRequiredKeys = [...newKeysArray, ...additionalFields]
    return allRequiredKeys.every((key) => key in obj)
  },

  getDefaultSummary: async () => {
    const summaryInfo = await secretManager.getSecret('SummaryInfo', '')
    const keys = summaryInfo.split(',').map((key) => key.trim())
    const obj = {}

    keys.forEach((key) => {
      obj[key] = ''
    })

    // Add default values for additional fields
    obj.vitals = {
      heartRate: 0,
      systolicPressure: 0,
      diastolicPressure: 0,
      respiratoryRate: 0,
      spO2: 0,
      temperature: {
        value: 0,
        unit: 'Celsius',
      },
    }
    obj.anthropometry = {
      height: '',
      weight: '',
      bmi: 0,
      waistCircumference: '',
    }
    obj.generalphysicalexamination = {
      pallor: false,
      icterus: false,
      cyanosis: false,
      clubbing: false,
      pedalEnema: false,
      pedalEnemaNotes: '',
      lymphadenopathy: false,
      lymphadenopathyNotes: '',
    }
    obj.heent = ''
    obj.systemicexamination = {
      neurologicalExamination: '',
      cardiovascularExamination: '',
      respiratoryExamination: '',
      abdomenExamination: '',
      rheumatologicalExamination: '',
    }

    return obj
  },

  cleanEmptyHtmlLists: (summary) => {
    if (!summary || typeof summary !== 'object') {
      return summary
    }

    // Create a deep copy to avoid modifying the original
    const cleanedSummary = JSON.parse(JSON.stringify(summary))

    // List of fields that should be cleaned of empty HTML lists
    const listFields = [
      'presentingcomplaint',
      'historyofpresenting',
      'pastmedicalhistory',
      'pastsurgicalhistory',
      'familyhistory',
      'addictionhistory',
      'diethistory',
      'physicalactivityhistory',
      'stresshistory',
      'sleephistory',
      'currentmedicationhistory',
      'heent',
    ]

    // Clean main list fields
    listFields.forEach((field) => {
      if (cleanedSummary[field]) {
        cleanedSummary[field] = module.exports.cleanHtmlListField(
          cleanedSummary[field],
        )
      }
    })

    // Clean systemic examination fields
    if (
      cleanedSummary.systemicexamination &&
      typeof cleanedSummary.systemicexamination === 'object'
    ) {
      Object.keys(cleanedSummary.systemicexamination).forEach((key) => {
        if (cleanedSummary.systemicexamination[key]) {
          cleanedSummary.systemicexamination[key] =
            module.exports.cleanHtmlListField(
              cleanedSummary.systemicexamination[key],
            )
        }
      })
    }

    return cleanedSummary
  },

  cleanHtmlListField: (htmlContent) => {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return ''
    }

    // Check if it's an empty HTML list
    const trimmed = htmlContent.trim()
    if (
      trimmed === '<ul></ul>' ||
      trimmed === '<ul> </ul>' ||
      trimmed === '<ul>\n</ul>'
    ) {
      return ''
    }

    // Check if it contains only empty list items
    const emptyListItemPattern = /^<ul>\s*(<li>\s*<\/li>\s*)*<\/ul>$/
    if (emptyListItemPattern.test(trimmed)) {
      return ''
    }

    // Return the original content if it has actual content
    return htmlContent
  },

  jsonResponse: (bodyContent, statusCode = 200) => {
    const response = new HttpResponse({
      jsonBody: bodyContent,
      status: statusCode,
    })
    response.headers.set('content-type', 'application/json')
    return response
  },
  encryptData: (data) => {
    const key = initializeSecretKey()
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    return encrypted
  },
  decryptData: (data) => {
    const key = initializeSecretKey()
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
    let decrypted = decipher.update(data, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  },
  encryptDataWithRandomIV: (data) => {
    const key = initializeSecretKey()
    const randomIV = crypto.randomBytes(16) // Generate random 16-byte IV
    const cipher = crypto.createCipheriv('aes-256-cbc', key, randomIV)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    // Prepend IV to encrypted data for storage/transmission
    return randomIV.toString('hex') + ':' + encrypted
  },
  decryptDataWithRandomIV: (data) => {
    const key = initializeSecretKey()
    const parts = data.split(':')
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format')
    }
    const iv = Buffer.from(parts[0], 'hex')
    const encryptedData = parts[1]
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  },
}

module.exports = helper
