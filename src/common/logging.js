const { parseJ<PERSON><PERSON> } = require('./helper')

const appName = process.env.APP_NAME || 'ARCAAI-MS-EMR'
const logging = {
  logError: (message, error) => {
    console.error(`${appName} :: ${message}`, parseJSON(error))
  },

  logInfo: (info) => {
    console.log(`${appName} :: `, info)
  },

  logWarning: (message, data = null) => {
    console.warn(`${appName} :: ${message}`, data ? parseJSON(data) : '')
  },
}

module.exports = logging
