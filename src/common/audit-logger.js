const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const auditLogContainer = 'audit-logs'

class AuditLogger {
  async logAction(action, performedBy, details) {
    const logEntry = {
      action,
      performedBy,
      details,
      timestamp: new Date().toISOString(),
    }
    await cosmosDbContext.createItem(logEntry, auditLogContainer)
  }
}

module.exports = new AuditLogger()
