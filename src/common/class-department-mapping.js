const classToDepartmentMapping = {
  CHEM: 'Clinical Chemistry',
  'HEM/BC': 'Hematology / Blood Count',
  MICRO: 'Microbiology',
  SERO: 'Serology',
  PATHOLOGY: 'Surgical/Anatomic Path',
  RAD: 'Radiology / Imaging Obs',
}

const getDepartmentByClass = (department) => {
  const classEntry = Object.entries(classToDepartmentMapping).find(
    ([, dept]) => dept === department,
  )
  return classEntry ? classEntry[0] : null
}

const getDepartmentByClassCode = (classCode) => {
  return classToDepartmentMapping[classCode] || 'Others'
}

module.exports = {
  getDepartmentByClass,
  getDepartmentByClassCode,
  classToDepartmentMapping,
  getDepartmentByClassCode,
}
