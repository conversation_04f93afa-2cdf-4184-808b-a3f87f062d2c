const {
  getTestsByPackageId,
  getTestPackageById,
  getTestPackageByName,
  getTestPackagesByType,
  getUserSpecificPackages,
} = require('../queries/test-package-query')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const testPackageContainer = 'test-packages'
const organizationTestsContainer = 'OrganizationTests'
const labTestsContainer = 'lab_tests'

class TestPackageRepository {
  async getTestsByPackageId(packageId) {
    const query = getTestsByPackageId(packageId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getTestPackageById(packageId) {
    const query = getTestPackageById(packageId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getTestPackageByName(name) {
    const query = getTestPackageByName(name)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async createTestPackage(testPackageData) {
    return cosmosDbContext.createItem(testPackageData, testPackageContainer)
  }

  async upsertTestPackage(testPackageData) {
    return cosmosDbContext.upsertItem(
      testPackageData.id,
      testPackageData,
      testPackageContainer,
    )
  }

  async getTestPricingInfo(testId, organizationId) {
    try {
      const orgTestQuery = `SELECT c.price, c.organizationCost FROM c WHERE c.testId = "${testId}" AND c.organizationId = "${organizationId}" AND c.isActive = true`
      const orgTestResult = await cosmosDbContext.queryItems(
        orgTestQuery,
        organizationTestsContainer,
      )

      if (orgTestResult && orgTestResult.length > 0) {
        return {
          price: orgTestResult[0].price || 0,
          organizationCost: orgTestResult[0].organizationCost || 0,
        }
      }

      const labTestQuery = `SELECT c.cost as price, c.cost as organizationCost FROM c WHERE c.id = "${testId}"`
      const labTestResult = await cosmosDbContext.queryItems(
        labTestQuery,
        labTestsContainer,
      )

      if (labTestResult && labTestResult.length > 0) {
        return {
          price: labTestResult[0].price || 0,
          organizationCost: labTestResult[0].organizationCost || 0,
        }
      }

      return {
        price: 0,
        organizationCost: 0,
      }
    } catch (error) {
      console.error(`Error fetching pricing info for test ${testId}:`, error)
      return {
        price: 0,
        organizationCost: 0,
      }
    }
  }

  async getTestPackagesByType(type) {
    const query = getTestPackagesByType(type)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }

  async getUserSpecificPackages(userId) {
    const query = getUserSpecificPackages(userId)
    return cosmosDbContext.queryItems(query, testPackageContainer)
  }
}

module.exports = new TestPackageRepository()
