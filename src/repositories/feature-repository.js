const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const Feature = require('../models/feature-model')
const logging = require('../common/logging')
const {
  getAllFeaturesQuery,
  searchFeaturesQuery,
  getFeaturesByPermissionKeyQuery,
  getAllPermissionKeysQuery,
  getFeaturesByIdsQuery,
} = require('../queries/feature-query')

class FeatureRepository {
  constructor() {
    this.containerName = 'features'
  }

  async createFeature(featureData) {
    try {
      const feature = new Feature(featureData)

      const validation = feature.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.createItem(feature.toJSON(), this.containerName)
      return resource
    } catch (error) {
      logging.logError('Error creating feature:', error)
      throw error
    }
  }

  async updateFeature(featureId, featureData) {
    try {
      // Get existing feature
      const existingFeature = await cosmosDbContext.readItem(
        featureId,
        featureId,
        this.containerName
      )

      if (!existingFeature) {
        throw new Error('Feature not found')
      }

      // Partial update - merge with existing data
      const updatedFeature = new Feature({
        ...existingFeature,
        ...featureData,
        id: featureId,
        updated_on: new Date().toISOString(),
      })

      const validation = updatedFeature.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.updateItem(
        updatedFeature.toJSON(),
        this.containerName
      )
      return resource
    } catch (error) {
      logging.logError('Error updating feature:', error)
      throw error
    }
  }

  async deleteFeature(featureId) {
    try {
      await cosmosDbContext.deleteItem(featureId, featureId, this.containerName)
      return { success: true, message: 'Feature deleted successfully' }
    } catch (error) {
      logging.logError('Error deleting feature:', error)
      throw error
    }
  }

  async getFeatureById(featureId) {
    try {
      const resource = await cosmosDbContext.readItem(
        featureId,
        featureId,
        this.containerName
      )
      return resource
    } catch (error) {
      if (error.code === 404) {
        return null
      }
      logging.logError('Error getting feature:', error)
      throw error
    }
  }

  async getAllFeatures(includeInactive = false) {
    try {
      const query = getAllFeaturesQuery(includeInactive)
      const resources = await cosmosDbContext.queryItems(query, this.containerName)
      return resources
    } catch (error) {
      logging.logError('Error getting all features:', error)
      throw error
    }
  }

  async searchFeatures(searchParams) {
    try {
      const querySpec = searchFeaturesQuery(searchParams)
      const resources = await cosmosDbContext.queryItems(querySpec, this.containerName)
      return resources
    } catch (error) {
      logging.logError('Error searching features:', error)
      throw error
    }
  }

  async getFeaturesByPermissionKey(permissionKey) {
    try {
      const query = getFeaturesByPermissionKeyQuery(permissionKey)
      const resources = await cosmosDbContext.queryItems(query, this.containerName)
      return resources
    } catch (error) {
      logging.logError('Error getting features by permission key:', error)
      throw error
    }
  }

  async getAllPermissionKeys() {
    try {
      const query = getAllPermissionKeysQuery()
      const resources = await cosmosDbContext.queryItems(query, this.containerName)
      // Sort in memory since Cosmos DB doesn't support ORDER BY with JOIN
      return resources.sort((a, b) => a.localeCompare(b))
    } catch (error) {
      logging.logError('Error getting all permission keys:', error)
      throw error
    }
  }

  async deactivateFeature(featureId, updatedBy) {
    try {
      const existingFeature = await this.getFeatureById(featureId)

      if (!existingFeature) {
        throw new Error('Feature not found')
      }

      const updatedFeature = new Feature({
        ...existingFeature,
        isActive: false,
        updated_on: new Date().toISOString(),
        updated_by: updatedBy,
      })

      const resource = await cosmosDbContext.updateItem(
        updatedFeature.toJSON(),
        this.containerName
      )
      return resource
    } catch (error) {
      logging.logError('Error deactivating feature:', error)
      throw error
    }
  }

  // Batch load features by multiple IDs (single DB call instead of N calls)
  async getFeaturesByIds(featureIds) {
    try {
      if (!featureIds || featureIds.length === 0) return []
      const query = getFeaturesByIdsQuery(featureIds)
      if (!query) return []
      const resources = await cosmosDbContext.queryItems(query, this.containerName)
      return resources || []
    } catch (error) {
      logging.logError('Error fetching features by IDs:', error)
      throw error
    }
  }
}

module.exports = new FeatureRepository()
