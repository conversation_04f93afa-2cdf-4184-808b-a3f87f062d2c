const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { getPatientLifestyleByDateRangeQuery } = require('../queries/patient-lifestyle-query')

const patientLifeStyleContainer = 'PatientLifeStyles'

class PatientLifestyleRepository {
  
  async getPatientLifestyleByDateRange(patientId, fromDate, toDate) {
    try {
      const query = getPatientLifestyleByDateRangeQuery(patientId, fromDate, toDate)
      const result = await cosmosDbContext.queryItems(query, patientLifeStyleContainer)
      return result || []
    } catch (error) {
      logging.logError(`Failed to fetch patient lifestyle data for patient: ${patientId}`, error)
      throw new Error('Failed to fetch patient lifestyle data from database')
    }
  }
}

module.exports = new PatientLifestyleRepository()
