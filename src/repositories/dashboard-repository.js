const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { 
  getTotalPatientsQuery,
  getTodaysAppointmentsQuery,
  getPatientQueueQuery,
  getAverageWaitingTimeQuery,
  getUpcomingAppointmentsQuery,
} = require('../queries/dashboard-query')

const patientContainer = 'PatientProfiles'
const appointmentContainer = 'Appointments'
const queueContainer = 'Queues'

class DashboardRepository {
  
  async getTotalPatients(organizationId) {
    try {
      const query = getTotalPatientsQuery(organizationId)
      const result = await cosmosDbContext.queryItems(query, patientContainer)
      return result && result.length > 0 ? result[0] : 0
    } catch (error) {
      logging.logError(`Failed to fetch total patients for organization: ${organizationId}`, error)
      throw new Error('Failed to fetch total patients from database')
    }
  }

  async getTodaysAppointments(organizationId, todayString) {
    try {
      const query = getTodaysAppointmentsQuery(organizationId, todayString)
      const result = await cosmosDbContext.queryItems(query, queueContainer)
      return result && result.length > 0 ? result[0] : 0
    } catch (error) {
      logging.logError(`Failed to fetch today's appointments for organization: ${organizationId}`, error)
      throw new Error('Failed to fetch today\'s appointments from database')
    }
  }

  async getPatientQueue(organizationId, todayString) {
    try {
      const query = getPatientQueueQuery(organizationId, todayString)
      const result = await cosmosDbContext.queryItems(query, queueContainer)
      return result && result.length > 0 ? result[0] : 0
    } catch (error) {
      logging.logError(`Failed to fetch patient queue for organization: ${organizationId}`, error)
      throw new Error('Failed to fetch patient queue from database')
    }
  }

  async getAverageWaitingTimeData(organizationId, todayString) {
    try {
      const query = getAverageWaitingTimeQuery(organizationId, todayString)
      const result = await cosmosDbContext.queryItems(query, queueContainer)
      return result || []
    } catch (error) {
      logging.logError(`Failed to fetch average waiting time data for organization: ${organizationId}`, error)
      throw new Error('Failed to fetch average waiting time data from database')
    }
  }

  async getUpcomingAppointments(organizationId, dateFilter, doctorId) {
    try {
      const queryConfig = getUpcomingAppointmentsQuery(organizationId, dateFilter, doctorId)
      const result = await cosmosDbContext.queryItems(queryConfig, queueContainer)
      return result || []
    } catch (error) {
      logging.logError(`Failed to fetch upcoming appointments for organization: ${organizationId}`, error)
      throw new Error('Failed to fetch upcoming appointments from database')
    }
  }
}

module.exports = new DashboardRepository()
