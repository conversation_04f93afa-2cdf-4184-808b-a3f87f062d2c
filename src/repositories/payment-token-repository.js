const { CosmosClient } = require('@azure/cosmos')
const { PaymentTokenModel, PaymentSessionTokenModel } = require('../models/payment-token-model')
const logging = require('../common/logging')
const secretManager = require('../services/secret-manager')

class PaymentTokenRepository {
  constructor() {
    this.client = null
    this.database = null
    this.paymentTokenContainer = null
    this.sessionTokenContainer = null
    this.isInitialized = false
    this.initializationPromise = null
  }

  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._doInitialize()
    return this.initializationPromise
  }

  async _doInitialize() {
    try {
      logging.logInfo('Initializing Payment Token Repository...')
      
      const connectionString = await secretManager.getSecret('COSMOS-DB-CONNECTIONSTRING')
      if (!connectionString) {
        throw new Error('Cosmos DB connection string not found')
      }

      this.client = new CosmosClient(connectionString)
      this.database = this.client.database('emr-database')
      
      // Initialize containers
      this.paymentTokenContainer = this.database.container('payment-tokens')
      this.sessionTokenContainer = this.database.container('payment-sessions')
      
      this.isInitialized = true
      logging.logInfo('Payment Token Repository initialized successfully')
    } catch (error) {
      logging.logError('Failed to initialize Payment Token Repository:', error)
      this.isInitialized = false
      throw error
    }
  }

  async ensureInitialized() {
    if (!this.isInitialized) {
      await this.initialize()
    }
  }

  /**
   * Create a payment token record
   * @param {PaymentTokenModel} paymentToken - Payment token model
   * @returns {PaymentTokenModel} - Created payment token
   */
  async createPaymentToken(paymentToken) {
    await this.ensureInitialized()
    
    try {
      const { resource } = await this.paymentTokenContainer.items.create(paymentToken)
      logging.logInfo(`Payment token created: ${resource.id}`)
      return new PaymentTokenModel(resource)
    } catch (error) {
      logging.logError('Error creating payment token:', error)
      throw new Error(`Failed to create payment token: ${error.message}`)
    }
  }

  /**
   * Get payment token by ID
   * @param {string} tokenId - Token ID
   * @returns {PaymentTokenModel} - Payment token
   */
  async getPaymentTokenById(tokenId) {
    await this.ensureInitialized()
    
    try {
      const { resource } = await this.paymentTokenContainer.item(tokenId, tokenId).read()
      if (!resource) {
        throw new Error('Payment token not found')
      }
      return new PaymentTokenModel(resource)
    } catch (error) {
      if (error.code === 404) {
        throw new Error('Payment token not found')
      }
      logging.logError('Error fetching payment token:', error)
      throw new Error(`Failed to fetch payment token: ${error.message}`)
    }
  }

  /**
   * Get payment token by payment ID
   * @param {string} paymentId - Payment ID
   * @returns {PaymentTokenModel} - Payment token
   */
  async getPaymentTokenByPaymentId(paymentId) {
    await this.ensureInitialized()
    
    try {
      const query = {
        query: 'SELECT * FROM c WHERE c.paymentId = @paymentId AND c.isTokenActive = true',
        parameters: [{ name: '@paymentId', value: paymentId }]
      }
      
      const { resources } = await this.paymentTokenContainer.items.query(query).fetchAll()
      if (resources.length === 0) {
        throw new Error('Payment token not found')
      }
      
      return new PaymentTokenModel(resources[0])
    } catch (error) {
      logging.logError('Error fetching payment token by payment ID:', error)
      throw new Error(`Failed to fetch payment token: ${error.message}`)
    }
  }

  /**
   * Update payment token
   * @param {PaymentTokenModel} paymentToken - Updated payment token
   * @returns {PaymentTokenModel} - Updated payment token
   */
  async updatePaymentToken(paymentToken) {
    await this.ensureInitialized()
    
    try {
      const { resource } = await this.paymentTokenContainer
        .item(paymentToken.id, paymentToken.id)
        .replace(paymentToken)
      
      logging.logInfo(`Payment token updated: ${resource.id}`)
      return new PaymentTokenModel(resource)
    } catch (error) {
      logging.logError('Error updating payment token:', error)
      throw new Error(`Failed to update payment token: ${error.message}`)
    }
  }

  /**
   * Create payment session token
   * @param {PaymentSessionTokenModel} sessionToken - Session token model
   * @returns {PaymentSessionTokenModel} - Created session token
   */
  async createSessionToken(sessionToken) {
    await this.ensureInitialized()
    
    try {
      const { resource } = await this.sessionTokenContainer.items.create(sessionToken)
      logging.logInfo(`Payment session token created: ${resource.id}`)
      return new PaymentSessionTokenModel(resource)
    } catch (error) {
      logging.logError('Error creating session token:', error)
      throw new Error(`Failed to create session token: ${error.message}`)
    }
  }

  /**
   * Get session token by session ID
   * @param {string} sessionId - Session ID
   * @returns {PaymentSessionTokenModel} - Session token
   */
  async getSessionTokenBySessionId(sessionId) {
    await this.ensureInitialized()
    
    try {
      const query = {
        query: 'SELECT * FROM c WHERE c.sessionId = @sessionId',
        parameters: [{ name: '@sessionId', value: sessionId }]
      }
      
      const { resources } = await this.sessionTokenContainer.items.query(query).fetchAll()
      if (resources.length === 0) {
        throw new Error('Session token not found')
      }
      
      return new PaymentSessionTokenModel(resources[0])
    } catch (error) {
      logging.logError('Error fetching session token:', error)
      throw new Error(`Failed to fetch session token: ${error.message}`)
    }
  }

  /**
   * Update session token
   * @param {PaymentSessionTokenModel} sessionToken - Updated session token
   * @returns {PaymentSessionTokenModel} - Updated session token
   */
  async updateSessionToken(sessionToken) {
    await this.ensureInitialized()
    
    try {
      const { resource } = await this.sessionTokenContainer
        .item(sessionToken.id, sessionToken.id)
        .replace(sessionToken)
      
      logging.logInfo(`Session token updated: ${resource.id}`)
      return new PaymentSessionTokenModel(resource)
    } catch (error) {
      logging.logError('Error updating session token:', error)
      throw new Error(`Failed to update session token: ${error.message}`)
    }
  }

  /**
   * Clean up expired tokens
   * @returns {number} - Number of tokens cleaned up
   */
  async cleanupExpiredTokens() {
    await this.ensureInitialized()
    
    try {
      const now = new Date().toISOString()
      
      // Clean up expired payment tokens
      const expiredTokensQuery = {
        query: 'SELECT * FROM c WHERE c.tokenExpiresAt < @now OR c.tokenRevoked = true',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: expiredTokens } = await this.paymentTokenContainer
        .items.query(expiredTokensQuery).fetchAll()
      
      // Clean up expired session tokens
      const expiredSessionsQuery = {
        query: 'SELECT * FROM c WHERE c.sessionExpiresAt < @now',
        parameters: [{ name: '@now', value: now }]
      }
      
      const { resources: expiredSessions } = await this.sessionTokenContainer
        .items.query(expiredSessionsQuery).fetchAll()
      
      let cleanedCount = 0
      
      // Delete expired tokens (or mark as inactive)
      for (const token of expiredTokens) {
        token.isTokenActive = false
        token.tokenRevoked = true
        token.revokedReason = 'Expired - Auto cleanup'
        await this.updatePaymentToken(new PaymentTokenModel(token))
        cleanedCount++
      }
      
      for (const session of expiredSessions) {
        session.isActive = false
        await this.updateSessionToken(new PaymentSessionTokenModel(session))
        cleanedCount++
      }
      
      logging.logInfo(`Cleaned up ${cleanedCount} expired payment tokens`)
      return cleanedCount
    } catch (error) {
      logging.logError('Error cleaning up expired tokens:', error)
      throw new Error(`Failed to cleanup expired tokens: ${error.message}`)
    }
  }
}

module.exports = new PaymentTokenRepository()
