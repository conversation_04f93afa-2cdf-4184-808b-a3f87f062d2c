const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const paymentQuery = require('../queries/payment-query')

const paymentsContainer = 'Payments'

class PaymentRepository {
  async createPayment(payment) {
    try {
      return await cosmosDbContext.createItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error creating payment record', error)
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }

  async updatePayment(payment) {
    try {
      return await cosmosDbContext.updateItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error updating payment record', error)
      throw new Error(`Failed to update payment: ${error.message}`)
    }
  }

  async getPaymentById(paymentId) {
    try {
      const query = paymentQuery.getPaymentByIdQuery(paymentId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by ID', error)
      throw new Error(`Failed to fetch payment by ID: ${error.message}`)
    }
  }

  async getPaymentByRazorpayOrderId(razorpayOrderId) {
    try {
      const query =
        paymentQuery.getPaymentByRazorpayOrderIdQuery(razorpayOrderId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay order ID', error)
      throw new Error(
        `Failed to fetch payment by Razorpay order ID: ${error.message}`,
      )
    }
  }

  async getPaymentByRazorpayPaymentId(razorpayPaymentId) {
    try {
      const query =
        paymentQuery.getPaymentByRazorpayPaymentIdQuery(razorpayPaymentId)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay payment ID', error)
      throw new Error(
        `Failed to fetch payment by Razorpay payment ID: ${error.message}`,
      )
    }
  }

  async getPaymentsByOrganization(
    organizationId,
    filters = {},
    continuationToken = null,
    pageSize = 20,
  ) {
    try {
      const query = paymentQuery.getPaymentsByOrganizationQuery(
        organizationId,
        filters,
        continuationToken,
      )
      const result = await cosmosDbContext.getAllItemQueryWithPagination(
        paymentsContainer,
        query,
        pageSize,
        continuationToken,
      )
      return result
    } catch (error) {
      logging.logError('Error fetching payments by organization', error)
      throw new Error(
        `Failed to fetch payments by organization: ${error.message}`,
      )
    }
  }

  async getPaymentStatistics(organizationId, startDate = null, endDate = null) {
    try {
      const query = paymentQuery.getPaymentStatisticsQuery(
        organizationId,
        startDate,
        endDate,
      )
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  async getPaymentBySubscriberEmail(organizationId, subscriberEmail) {
    try {
      const query = paymentQuery.getPaymentBySubscriberEmailQuery(organizationId, subscriberEmail)
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result && result.length ? result : []
    } catch (error) {
      logging.logError('Error fetching payment by subscriber email', error)
      throw new Error(
        `Failed to fetch payment by subscriber email: ${error.message}`,
      )
    }
  }

  // Batch load payments by multiple IDs (single DB call instead of N calls)
  async getPaymentsByIds(paymentIds) {
    try {
      if (!paymentIds || paymentIds.length === 0) return []
      const query = paymentQuery.getPaymentsByIdsQuery(paymentIds)
      if (!query) return []
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result || []
    } catch (error) {
      logging.logError('Error fetching payments by IDs', error)
      throw new Error(`Failed to fetch payments by IDs: ${error.message}`)
    }
  }

  // Batch load payments by multiple Razorpay payment IDs
  async getPaymentsByRazorpayPaymentIds(razorpayPaymentIds) {
    try {
      if (!razorpayPaymentIds || razorpayPaymentIds.length === 0) return []
      const query = paymentQuery.getPaymentsByRazorpayPaymentIdsQuery(razorpayPaymentIds)
      if (!query) return []
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result || []
    } catch (error) {
      logging.logError('Error fetching payments by Razorpay payment IDs', error)
      throw new Error(`Failed to fetch payments by Razorpay payment IDs: ${error.message}`)
    }
  }

  // Batch load subscription payments for multiple emails
  async getSubscriptionPaymentsByEmails(organizationId, emails) {
    try {
      if (!emails || emails.length === 0) return []
      const query = paymentQuery.getSubscriptionPaymentsByEmailsQuery(organizationId, emails)
      if (!query) return []
      const result = await cosmosDbContext.queryItems(query, paymentsContainer)
      return result || []
    } catch (error) {
      logging.logError('Error fetching subscription payments by emails', error)
      throw new Error(`Failed to fetch subscription payments by emails: ${error.message}`)
    }
  }
}

module.exports = new PaymentRepository()
