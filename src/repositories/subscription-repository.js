const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const {
  SubscriptionPlan,
  OrganizationSubscription,
  ORGANIZATION_PLAN_ID,
} = require('../models/subscription-model')
const logging = require('../common/logging')
const {
  getActivePlansQuery,
  searchPlansQuery,
  getPlansByValidityQuery,
  getPlanFeaturesQuery,
  getActiveSubscriptionByOrganizationQuery,
  getSubscriptionsByStatusQuery,
  getSubscriptionAnalyticsQuery,
  getUpcomingRenewalsQuery,
  getSubscriptionsByDateRangeQuery,
  getRevenueByPlanQuery,
  getActiveSubscriptionFeatureAccessQuery,
  getAllPlansQuery,
  getOrganizationSubscriptionQuery,
  getOrganizationSubscriptionHistoryQuery,
  getExpiringSubscriptionsQuery,
  getExpiredSubscriptionsQuery,
  getSubscriptionsByPlanIdQuery,
  hasUsedFreeTrialQuery,
  getActiveSubscriptionByEmailAndOrganizationQuery,
  getAnySubscriptionByEmailAndOrganizationQuery,
  getAnySubscriptionByEmailQuery,
  getSubscriptionByIdQuery,
  getPlanByNameQuery,
} = require('../queries/subscription-query')

class SubscriptionRepository {
  constructor() {
    this.containerName = 'subscription_plans'
    this.subscriptionContainerName = 'organization_subscriptions'
  }

  async createPlan(planData) {
    try {
      const plan = new SubscriptionPlan(planData)

      const validation = plan.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.createItem(
        plan.toJSON(),
        this.containerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error creating subscription plan:', error)
      throw error
    }
  }

  async replacePlan(planId, planData) {
    try {
      // Get existing plan to preserve audit fields
      const existingPlan = await cosmosDbContext.readItem(
        planId,
        planId,
        this.containerName,
      )

      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // Full replacement - only preserve id, created_by, created_on
      const replacedPlan = new SubscriptionPlan({
        ...planData,
        id: planId,
        created_by: existingPlan.created_by,
        created_on: existingPlan.created_on,
        updated_on: new Date().toISOString(),
      })

      const validation = replacedPlan.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.updateItem(
        replacedPlan.toJSON(),
        this.containerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error replacing subscription plan:', error)
      throw error
    }
  }

  async updatePlan(planId, planData) {
    try {
      // Get existing plan
      const existingPlan = await cosmosDbContext.readItem(
        planId,
        planId,
        this.containerName,
      )

      if (!existingPlan) {
        throw new Error('Subscription plan not found')
      }

      // Partial update - merge with existing data
      const updatedPlan = new SubscriptionPlan({
        ...existingPlan,
        ...planData,
        id: planId,
        updated_on: new Date().toISOString(),
      })

      const validation = updatedPlan.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.updateItem(
        updatedPlan.toJSON(),
        this.containerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error updating subscription plan:', error)
      throw error
    }
  }

  async deletePlan(planId) {
    try {
      await cosmosDbContext.deleteItem(planId, planId, this.containerName)
      return { success: true, message: 'Plan deleted successfully' }
    } catch (error) {
      logging.logError('Error deleting subscription plan:', error)
      throw error
    }
  }

  async getPlanById(planId) {
    try {
      const resource = await cosmosDbContext.readItem(
        planId,
        planId,
        this.containerName,
      )
      return resource
    } catch (error) {
      if (error.code === 404) {
        return null
      }
      logging.logError('Error getting subscription plan:', error)
      throw error
    }
  }

  async getAllPlans() {
    try {
      const query = getAllPlansQuery()

      const resources = await cosmosDbContext.queryItems(
        query,
        this.containerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting all subscription plans:', error)
      throw error
    }
  }

  // Organization Plan Methods (special plan for organization-level settings)

  async createOrganizationPlan(planData) {
    try {
      // Check if organization plan already exists
      const existingPlan = await this.getOrganizationPlan()

      if (existingPlan) {
        throw new Error(
          'Organization plan already exists. Use PATCH to update it.',
        )
      }

      // Create organization plan with fixed ID and minimal fields
      const plan = new SubscriptionPlan({
        id: ORGANIZATION_PLAN_ID,
        planName: planData.planName || 'Organization Plan',
        description:
          planData.description || 'Default organization-level plan settings',
        validity: 'Both', // Default value
        features: {
          MRD: [],
          EMR: [],
          Billing: [],
        },
        addOnFeatures: {
          MRD: [],
          EMR: [],
          Billing: [],
        },
        isActive: true,
        created_by: planData.created_by,
        created_on: new Date().toISOString(),
      })

      const resource = await cosmosDbContext.createItem(
        plan.toJSON(),
        this.containerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error creating organization plan:', error)
      throw error
    }
  }

  async getOrganizationPlan() {
    try {
      const resource = await cosmosDbContext.readItem(
        ORGANIZATION_PLAN_ID,
        ORGANIZATION_PLAN_ID,
        this.containerName,
      )
      return resource
    } catch (error) {
      if (error.code === 404) {
        return null
      }
      logging.logError('Error getting organization plan:', error)
      throw error
    }
  }

  async updateOrganizationPlan(planData) {
    try {
      // Get existing organization plan
      const existingPlan = await this.getOrganizationPlan()

      if (!existingPlan) {
        throw new Error('Organization plan not found. Please create it first.')
      }

      // Update only planName and description
      const updatedPlan = new SubscriptionPlan({
        ...existingPlan,
        planName: planData.planName || existingPlan.planName,
        description: planData.description || existingPlan.description,
        id: ORGANIZATION_PLAN_ID,
        updated_on: new Date().toISOString(),
        updated_by: planData.updated_by,
      })

      const resource = await cosmosDbContext.updateItem(
        updatedPlan.toJSON(),
        this.containerName,
      )

      return resource
    } catch (error) {
      logging.logError('Error updating organization plan:', error)
      throw error
    }
  }

  // Organization Subscription Methods

  async createOrganizationSubscription(subscriptionData) {
    try {
      const subscription = new OrganizationSubscription(subscriptionData)

      const validation = subscription.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.createItem(
        subscription.toJSON(),
        this.subscriptionContainerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error creating organization subscription:', error)
      throw error
    }
  }

  async updateOrganizationSubscription(
    subscriptionId,
    organizationId,
    updateData,
  ) {
    try {
      // Get existing subscription
      const existingSubscription = await cosmosDbContext.readItem(
        subscriptionId,
        organizationId,
        this.subscriptionContainerName,
      )

      if (!existingSubscription) {
        throw new Error('Organization subscription not found')
      }

      // Update fields
      const updatedSubscription = new OrganizationSubscription({
        ...existingSubscription,
        ...updateData,
        id: subscriptionId,
        organizationId: organizationId,
        updated_on: new Date().toISOString(),
      })

      const validation = updatedSubscription.validate()
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      const resource = await cosmosDbContext.updateItem(
        updatedSubscription.toJSON(),
        this.subscriptionContainerName,
      )
      return resource
    } catch (error) {
      logging.logError('Error updating organization subscription:', error)
      throw error
    }
  }

  async getOrganizationSubscription(organizationId) {
    try {
      const query = getOrganizationSubscriptionQuery(organizationId)

      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting organization subscription:', error)
      throw error
    }
  }

  async getOrganizationSubscriptionHistory(organizationId) {
    try {
      const query = getOrganizationSubscriptionHistoryQuery(organizationId)

      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError(
        'Error getting organization subscription history:',
        error,
      )
      throw error
    }
  }

  async cancelOrganizationSubscription(subscriptionId, organizationId) {
    try {
      const updateData = {
        status: 'cancelled',
        autoRenew: false,
        updated_on: new Date().toISOString(),
      }

      return await this.updateOrganizationSubscription(
        subscriptionId,
        organizationId,
        updateData,
      )
    } catch (error) {
      logging.logError('Error cancelling organization subscription:', error)
      throw error
    }
  }

  async getExpiringSubscriptions(daysBeforeExpiry = 7) {
    try {
      const query = getExpiringSubscriptionsQuery(daysBeforeExpiry)

      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting expiring subscriptions:', error)
      throw error
    }
  }

  async updateExpiredSubscriptions() {
    try {
      const query = getExpiredSubscriptionsQuery()

      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )

      const updatePromises = resources.map((subscription) => {
        return this.updateOrganizationSubscription(
          subscription.id,
          subscription.organizationId,
          { status: 'expired' },
        )
      })

      await Promise.all(updatePromises)
      return resources.length
    } catch (error) {
      logging.logError('Error updating expired subscriptions:', error)
      throw error
    }
  }

  // Query execution methods
  async getAllActivePlans() {
    try {
      const query = getActivePlansQuery()
      const resources = await cosmosDbContext.queryItems(
        query,
        this.containerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting active plans:', error)
      throw error
    }
  }

  async searchPlans(searchParams) {
    try {
      const querySpec = searchPlansQuery(searchParams)
      const resources = await cosmosDbContext.queryItems(
        querySpec,
        this.containerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error searching plans:', error)
      throw error
    }
  }

  async getPlansByValidity(validity) {
    try {
      const query = getPlansByValidityQuery(validity)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.containerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting plans by validity:', error)
      throw error
    }
  }

  async getPlanFeatures(planId) {
    try {
      const query = getPlanFeaturesQuery(planId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.containerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting plan features:', error)
      throw error
    }
  }

  async getActiveSubscriptionByOrganization(organizationId) {
    try {
      const query = getActiveSubscriptionByOrganizationQuery(organizationId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )

      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError(
        'Error getting active subscription by organization:',
        error,
      )
      throw error
    }
  }

  async getSubscriptionById(subscriptionId) {
    try {
      const query = getSubscriptionByIdQuery(subscriptionId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting subscription by ID:', error)
      throw error
    }
  }

  async getActiveSubscriptionByEmailAndOrganization(email, organizationId) {
    try {
      const query = getActiveSubscriptionByEmailAndOrganizationQuery(
        email,
        organizationId,
      )
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError(
        'Error getting active subscription by email and organization:',
        error,
      )
      throw error
    }
  }

  async getAnySubscriptionByEmailAndOrganization(email, organizationId) {
    try {
      const query = getAnySubscriptionByEmailAndOrganizationQuery(
        email,
        organizationId,
      )
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError(
        'Error getting any subscription by email and organization:',
        error,
      )
      throw error
    }
  }

  async getAnySubscriptionByEmail(email) {
    try {
      const query = getAnySubscriptionByEmailQuery(email)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting any subscription by email:', error)
      throw error
    }
  }

  async getSubscriptionsByStatus(status, organizationId = null) {
    try {
      const query = getSubscriptionsByStatusQuery(status, organizationId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting subscriptions by status:', error)
      throw error
    }
  }

  async getSubscriptionsByPlanId(planId) {
    try {
      const query = getSubscriptionsByPlanIdQuery(planId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting subscriptions by planId:', error)
      throw error
    }
  }

  async hasUsedFreeTrial(contactEmail) {
    try {
      const query = hasUsedFreeTrialQuery(contactEmail)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0
    } catch (error) {
      logging.logError('Error checking trial usage:', error)
      throw error
    }
  }

  async getSubscriptionAnalytics(organizationId = null) {
    try {
      const query = getSubscriptionAnalyticsQuery(organizationId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting subscription analytics:', error)
      throw error
    }
  }

  async getUpcomingRenewals(daysAhead, organizationId = null) {
    try {
      const query = getUpcomingRenewalsQuery(daysAhead, organizationId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting upcoming renewals:', error)
      throw error
    }
  }

  async getSubscriptionsByDateRange(startDate, endDate, organizationId = null) {
    try {
      const query = getSubscriptionsByDateRangeQuery(
        startDate,
        endDate,
        organizationId,
      )
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting subscriptions by date range:', error)
      throw error
    }
  }

  async getRevenueByPlan(startDate = null, endDate = null) {
    try {
      const query = getRevenueByPlanQuery(startDate, endDate)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources
    } catch (error) {
      logging.logError('Error getting revenue by plan:', error)
      throw error
    }
  }

  async getActiveSubscriptionFeatureAccess(organizationId) {
    try {
      const query = getActiveSubscriptionFeatureAccessQuery(organizationId)
      const resources = await cosmosDbContext.queryItems(
        query,
        this.subscriptionContainerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError(
        'Error getting active subscription feature access:',
        error,
      )
      throw error
    }
  }

  async getPlanByName(planName, excludePlanId = null) {
    try {
      const querySpec = getPlanByNameQuery(planName, excludePlanId)
      const resources = await cosmosDbContext.queryItems(
        querySpec,
        this.containerName,
      )
      return resources.length > 0 ? resources[0] : null
    } catch (error) {
      logging.logError('Error getting plan by name:', error)
      throw error
    }
  }
}

module.exports = new SubscriptionRepository()
