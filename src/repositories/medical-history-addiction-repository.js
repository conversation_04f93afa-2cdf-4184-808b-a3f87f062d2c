const cosmosDbContext = require('../cosmosDbContext/comosdb-context');
const logging = require('../common/logging');
const {
    getMedicalHistoryAddictionByPatientIdQuery,
    getMedicalHistoryAddictionByIdQuery,
    getMedicalHistoryAddictionByStatusQuery,
    getMedicalHistoryAddictionByPatientAndDateRangeQuery,
    getMedicalHistoryAddictionByDiagnosisQuery,
    getMedicalHistoryAddictionBySubstanceHistoryQuery,
    getMedicalHistoryAddictionWithNicotineTestQuery,
    getMedicalHistoryAddictionByCreatedByQuery,
    getMedicalHistoryAddictionByDateRangeQuery
} = require('../queries/medical-history-addiction-query');

const containerId = "MedicalHistoryAddiction";

class MedicalHistoryAddictionRepository {

    async create(medicalHistory) {
        try {
            const result = await cosmosDbContext.createItem(medicalHistory, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to create medical history addiction", error);
            throw error;
        }
    }

    async getByPatientId(patientId) {
        try {
            const query = getMedicalHistoryAddictionByPatientIdQuery(patientId);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result && result.length > 0 ? result[0] : null;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by patient ID", error);
            throw error;
        }
    }

    async getById(id) {
        try {
            const result = await cosmosDbContext.readItem(id, id, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by ID", error);
            throw error;
        }
    }

    async update(id, updateData) {
        try {
            updateData.id = id;
            const result = await cosmosDbContext.updateItem(updateData, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to update medical history addiction", error);
            throw error;
        }
    }

    async patch(id, patchPayload) {
        try {
            const result = await cosmosDbContext.patchItem(id, patchPayload, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to patch medical history addiction", error);
            throw error;
        }
    }

    async delete(id) {
        try {
            const result = await cosmosDbContext.deleteItem(id, id, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to delete medical history addiction", error);
            throw error;
        }
    }

    async getByQuery(query) {
        try {
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by query", error);
            throw error;
        }
    }

    async getByStatus(status) {
        try {
            const query = getMedicalHistoryAddictionByStatusQuery(status);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by status", error);
            throw error;
        }
    }

    async getByDateRange(patientId, startDate, endDate) {
        try {
            const query = getMedicalHistoryAddictionByPatientAndDateRangeQuery(patientId, startDate, endDate);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by date range", error);
            throw error;
        }
    }

    async getByDiagnosis(diseaseName) {
        try {
            const query = getMedicalHistoryAddictionByDiagnosisQuery(diseaseName);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by diagnosis", error);
            throw error;
        }
    }

    async getBySubstanceHistory(substanceType, history) {
        try {
            const query = getMedicalHistoryAddictionBySubstanceHistoryQuery(substanceType, history);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by substance history", error);
            throw error;
        }
    }

    async getWithNicotineTest() {
        try {
            const query = getMedicalHistoryAddictionWithNicotineTestQuery();
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction with nicotine test", error);
            throw error;
        }
    }

    async getByCreatedBy(createdBy) {
        try {
            const query = getMedicalHistoryAddictionByCreatedByQuery(createdBy);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by created by", error);
            throw error;
        }
    }

    async getByDateRangeGlobal(startDate, endDate) {
        try {
            const query = getMedicalHistoryAddictionByDateRangeQuery(startDate, endDate);
            const result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("Unable to get medical history addiction by global date range", error);
            throw error;
        }
    }

    async checkExistingRecord(patientId) {
        try {
            const existing = await this.getByPatientId(patientId);
            return existing !== null;
        } catch (error) {
            logging.logError("Unable to check existing record", error);
            throw error;
        }
    }
}

module.exports = new MedicalHistoryAddictionRepository();