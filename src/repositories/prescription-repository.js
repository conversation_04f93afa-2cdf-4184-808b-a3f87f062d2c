const {
  getPrescriptionById,
  searchPrescriptionQuery,
  getPrescriptionsByPatient,
} = require('../queries/prescription-query')
const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const prescriptionContainer = 'prescriptions'

class PrescriptionRepository {
  async getPrescriptionsByPatient(
    patientId,
    dateFilter = null,
    customDateRange = null,
    searchText = null,
  ) {
    if (customDateRange) {
      customDateRange.end =
        new Date(customDateRange.end).toISOString().split('T')[0] +
        'T23:59:59.999Z'
    }

    const query = getPrescriptionsByPatient(
      patientId,
      dateFilter,
      customDateRange,
      searchText,
    )

    const data = await cosmosDbContext.queryItems(query, prescriptionContainer)

    function sortSearchResults(items, searchText) {
      if (!searchText) return items
      const lowerSearch = searchText.toLowerCase()

      return items.sort((a, b) => {
        const aName = a.doctor?.toLowerCase() || ''
        const bName = b.doctor?.toLowerCase() || ''

        const aMedicineMatch = a.medicines?.some((m) =>
          m.genericName?.toLowerCase().startsWith(lowerSearch),
        )
        const bMedicineMatch = b.medicines?.some((m) =>
          m.genericName?.toLowerCase().startsWith(lowerSearch),
        )

        const aStarts = aName.startsWith(lowerSearch) || aMedicineMatch ? 0 : 1
        const bStarts = bName.startsWith(lowerSearch) || bMedicineMatch ? 0 : 1

        if (aStarts !== bStarts) return aStarts - bStarts
        return new Date(b.updated_on) - new Date(a.updated_on) // Sort by last updated first
      })
    }

    const sortedItems = sortSearchResults(data, searchText)

    return sortedItems
  }

  async getPrescriptionById(prescriptionId) {
    const query = getPrescriptionById(prescriptionId)
    return cosmosDbContext.queryItems(query, prescriptionContainer)
  }

  async createPrescription(patientId, prescriptionData) {
    const prescription = {
      ...prescriptionData,
      patientId,
      doctorEmail: prescriptionData.doctorEmail,
    }

    return cosmosDbContext.createItem(prescription, prescriptionContainer)
  }

  async updatePrescription(prescriptionId, patientId, prescriptionData) {
    if (!prescriptionId) {
      throw new Error('Prescription ID is required for update')
    }

    const updatedPrescription = {
      ...prescriptionData,
      id: prescriptionId,
      patientId,
      doctorEmail: prescriptionData.doctorEmail, // Ensure doctorEmail is added
    }

    return cosmosDbContext.upsertItem(
      prescriptionId,
      updatedPrescription,
      prescriptionContainer,
    )
  }
  async searchPrescription(
    queryString,
    pageSize = 10,
    continuationToken = null,
    patientId,
  ) {
    try {
      const query = searchPrescriptionQuery(queryString, patientId)

      const data = await cosmosDbContext.getAllItemQuery(
        prescriptionContainer,
        query,
        pageSize,
        continuationToken,
      )

      return data
    } catch (error) {
      logging.logError(
        `Unable to search prescriptions with query: ${queryString}`,
        error,
      )
      return { items: [], nextToken: null }
    }
  }
}

module.exports = new PrescriptionRepository()
