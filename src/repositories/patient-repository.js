const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const { paginate } = require('../utils/pagination')
const logging = require('../common/logging')
const {
  getPatientsByOrganizationQuery,
  getPatientByRegistrationNumberQuery,
} = require('../queries/patient-query')

const patientContainer = 'PatientProfiles'

class PatientRepository {
  async fetchPatientsForOrganization(
    organizationId,
    searchText,
    filters,
    sortBy,
    sortOrder,
    pageSize,
    page,
  ) {
    try {
      const query = getPatientsByOrganizationQuery(organizationId)
      const patients = await cosmosDbContext.queryItems(query, patientContainer)

      let filteredPatients = patients

      if (searchText) {
        const lowerSearchText = searchText.toLowerCase()

        const isNumericSearch = /^\d+$/.test(searchText)

        filteredPatients = filteredPatients.filter((patient) => {
          const idMatch = patient.id?.toLowerCase().startsWith(lowerSearchText)
          const nameMatch = patient.name?.toLowerCase().startsWith(lowerSearchText)
          const phoneMatch = isNumericSearch && patient.contact?.phone?.startsWith(searchText)

          return idMatch || nameMatch || phoneMatch
        })
      }

      if (filters.gender) {
        filteredPatients = filteredPatients.filter(
          (patient) => patient.sex === filters.gender,
        )
      }

      if (filters.ageRange) {
        const [minAge, maxAge] = filters.ageRange
        filteredPatients = filteredPatients.filter((patient) => {
          const dob = new Date(patient.dob)
          const today = new Date()

          let age = today.getFullYear() - dob.getFullYear()
          const hasHadBirthdayThisYear =
            today.getMonth() > dob.getMonth() ||
            (today.getMonth() === dob.getMonth() &&
              today.getDate() >= dob.getDate())

          if (!hasHadBirthdayThisYear) {
            age -= 1
          }

          return age >= minAge && age <= maxAge
        })
      }
      if (filters.registrationDateRange) {
        const [startDate, endDate] = filters.registrationDateRange
        filteredPatients = filteredPatients.filter(
          (patient) =>
            new Date(patient.created_on) >= new Date(startDate) &&
            new Date(patient.created_on) <=
              new Date(endDate + 'T23:59:59.999Z'),
        )
      }
      if (filters.fromAge !== undefined && filters.toAge !== undefined) {
        const { fromAge, toAge } = filters
        filteredPatients = filteredPatients.filter(
          (patient) => patient.age >= fromAge && patient.age <= toAge,
        )
      }
      if (filters.fromDate && filters.toDate) {
        const { fromDate, toDate } = filters
        filteredPatients = filteredPatients.filter(
          (patient) =>
            new Date(patient.created_on) >= new Date(fromDate) &&
            new Date(patient.created_on) <= new Date(toDate),
        )
      }

      if (sortBy) {
        filteredPatients.sort((a, b) => {
          const valueA = a[sortBy]?.toLowerCase() || ''
          const valueB = b[sortBy]?.toLowerCase() || ''
          if (sortOrder === 'desc') {
            return valueB.localeCompare(valueA)
          }
          return valueA.localeCompare(valueB)
        })
      }

      const paginatedResults = paginate(filteredPatients, pageSize, page)

      return {
        items: paginatedResults.items,
        totalItemCount: filteredPatients.length,
        currentPage: paginatedResults.currentPage,
        totalPages: Math.ceil(filteredPatients.length / pageSize),
      }
    } catch (error) {
      logging.logError(
        `Failed to fetch patients for organization: ${error.message}`,
        error,
      )
      throw new Error('Failed to fetch patients for organization')
    }
  }

  async getPatientById(patientId) {
    try {
      const patient = await cosmosDbContext.readItem(patientId, patientId, patientContainer)
      if (patient) {
        const PatientEncryptionService = require('../services/patient-encryption-service')
        return PatientEncryptionService.decryptPatientDataSecure(patient, [])
      }
      return patient
    } catch (error) {
      logging.logError(`Failed to fetch patient by ID ${patientId}: ${error.message}`, error)
      return null
    }
  }

  async getPatientByRegistrationNumber(registrationNumber, organizationId) {
    try {
      const query = getPatientByRegistrationNumberQuery(
        registrationNumber,
        organizationId,
      )
      logging.logInfo(`Searching patient with query: ${query}`)
      const patients = await cosmosDbContext.queryItems(query, patientContainer)
      logging.logInfo(`Found ${patients?.length || 0} patients matching registration number ${registrationNumber}`)

      if (patients && patients.length > 0) {
        const PatientEncryptionService = require('../services/patient-encryption-service')
        return PatientEncryptionService.decryptPatientDataSecure(patients[0], [])
      }
      return null
    } catch (error) {
      logging.logError(`Failed to fetch patient by registration number ${registrationNumber}: ${error.message}`, error)
      return null
    }
  }
}

module.exports = new PatientRepository()
