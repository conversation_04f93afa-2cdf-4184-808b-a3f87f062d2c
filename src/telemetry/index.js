/**
 * Telemetry Module Entry Point
 * Exports all tracing and instrumentation utilities
 */

const {
  initializeTracing,
  getTracer,
  createSpan,
  withSpan,
  addSpanAttributes,
  recordException,
  getTraceContext,
  trace,
  context,
  SpanStatusCode,
} = require('./tracing')

const {
  cosmosDbInstrumentation,
  externalApiInstrumentation,
  businessInstrumentation,
} = require('./instrumentations')

const {
  setupTracingPreInvocation,
  setupTracingPostInvocation,
  addAuthToSpan,
} = require('./hooks')

module.exports = {
  // Core tracing functions
  initializeTracing,
  getTracer,
  createSpan,
  withSpan,
  addSpanAttributes,
  recordException,
  getTraceContext,
  trace,
  context,
  SpanStatusCode,

  // CosmosDB instrumentation
  cosmosDb: cosmosDbInstrumentation,

  // External API instrumentation
  externalApi: externalApiInstrumentation,

  // Business logic instrumentation
  business: businessInstrumentation,

  // Azure Functions hooks
  setupTracingPreInvocation,
  setupTracingPostInvocation,
  addAuthToSpan,
}
