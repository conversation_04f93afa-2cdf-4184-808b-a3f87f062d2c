/**
 * OpenTelemetry Hooks for Azure Functions
 * Provides tracing integration without modifying core application logic
 */

const { getTracer, addSpanAttributes, SpanStatusCode } = require('./tracing')

/**
 * Pre-invocation tracing hook
 * Creates a span for the incoming request
 */
function setupTracingPreInvocation(context, req) {
  if (!req || !req.url) {
    return null
  }

  try {
    const route = req.url.split('?')[0]
    const method = req.method || 'GET'
    const functionName = context.invocationContext?.functionName || 'unknown'

    const tracer = getTracer('azure-functions')
    const span = tracer.startSpan(`${method} ${route}`, {
      attributes: {
        'http.route': route,
        'http.method': method,
        'http.url': req.url,
        'azure.function.name': functionName,
        'azure.invocation.id': context.invocationContext?.invocationId || 'unknown',
        'span.kind': 'server',
      },
    })

    context.invocationContext.extraInputs.set('tracingSpan', span)

    addSpanAttributes({
      'http.route': route,
      'http.method': method,
      'azure.function.name': functionName,
      'azure.invocation.id': context.invocationContext?.invocationId || 'unknown',
    })

    return span
  } catch (error) {
    console.error('[Telemetry] Error in pre-invocation tracing:', error)
    return null
  }
}

/**
 * Add auth result to tracing span
 */
function addAuthToSpan(success, authMessage, decode) {
  try {
    if (success) {
      addSpanAttributes({
        'auth.success': true,
        'user.id': decode?.sub || decode?.oid || 'unknown',
        'user.email_domain': decode?.emails?.[0]?.split('@')[1] || 'unknown',
        'organization.id': decode?.organizationId || 'unknown',
      })
    } else {
      addSpanAttributes({
        'auth.success': false,
        'auth.error': authMessage,
      })
    }
  } catch (error) {
    console.error('[Telemetry] Error adding auth to span:', error)
  }
}

/**
 * Post-invocation tracing hook
 * Ends the span and records status
 */
function setupTracingPostInvocation(context) {
  try {
    const span = context.invocationContext?.extraInputs?.get('tracingSpan')
    if (!span) {
      return
    }

    const result = context.result
    if (result) {
      const statusCode = result.status || result.statusCode || 200
      span.setAttribute('http.status_code', statusCode)

      if (statusCode >= 400) {
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message: `HTTP ${statusCode}`,
        })
      } else {
        span.setStatus({ code: SpanStatusCode.OK })
      }
    }

    if (context.error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: context.error.message,
      })
      span.recordException(context.error)
    }

    span.end()
  } catch (error) {
    console.error('[Telemetry] Error in post-invocation tracing:', error)
  }
}

module.exports = {
  setupTracingPreInvocation,
  setupTracingPostInvocation,
  addAuthToSpan,
}
