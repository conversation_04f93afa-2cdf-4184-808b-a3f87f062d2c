/**
 * Custom Instrumentations for EMR Microservice
 * Provides tracing for CosmosDB, External APIs, and Business Logic
 */

const { withSpan, addSpanAttributes, recordException, SpanStatusCode } = require('./tracing')

/**
 * CosmosDB Instrumentation
 * Wraps CosmosDB operations with tracing
 */
const cosmosDbInstrumentation = {
  /**
   * Trace a CosmosDB query operation
   */
  traceQuery: async (containerName, query, fn) => {
    return withSpan(`cosmosdb.query.${containerName}`, async (span) => {
      span.setAttribute('db.system', 'cosmosdb')
      span.setAttribute('db.name', process.env.COSMOS_DB_DATABASE || 'emr')
      span.setAttribute('db.cosmosdb.container', containerName)
      span.setAttribute('db.operation', 'query')

      if (typeof query === 'string') {
        span.setAttribute('db.statement', query.substring(0, 1000)) // Limit query length
      } else if (query?.query) {
        span.setAttribute('db.statement', query.query.substring(0, 1000))
      }

      const startTime = Date.now()
      try {
        const result = await fn()
        const duration = Date.now() - startTime

        span.setAttribute('db.cosmosdb.duration_ms', duration)
        span.setAttribute('db.cosmosdb.result_count', Array.isArray(result) ? result.length : 1)

        return result
      } catch (error) {
        span.setAttribute('db.cosmosdb.error_code', error.code || 'UNKNOWN')
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace a CosmosDB read operation
   */
  traceRead: async (containerName, itemId, partitionKey, fn) => {
    return withSpan(`cosmosdb.read.${containerName}`, async (span) => {
      span.setAttribute('db.system', 'cosmosdb')
      span.setAttribute('db.name', process.env.COSMOS_DB_DATABASE || 'emr')
      span.setAttribute('db.cosmosdb.container', containerName)
      span.setAttribute('db.operation', 'read')
      span.setAttribute('db.cosmosdb.item_id', itemId)
      span.setAttribute('db.cosmosdb.partition_key', String(partitionKey))

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('db.cosmosdb.duration_ms', Date.now() - startTime)
        span.setAttribute('db.cosmosdb.found', result !== null)
        return result
      } catch (error) {
        span.setAttribute('db.cosmosdb.error_code', error.code || 'UNKNOWN')
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace a CosmosDB create operation
   */
  traceCreate: async (containerName, fn) => {
    return withSpan(`cosmosdb.create.${containerName}`, async (span) => {
      span.setAttribute('db.system', 'cosmosdb')
      span.setAttribute('db.name', process.env.COSMOS_DB_DATABASE || 'emr')
      span.setAttribute('db.cosmosdb.container', containerName)
      span.setAttribute('db.operation', 'create')

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('db.cosmosdb.duration_ms', Date.now() - startTime)
        span.setAttribute('db.cosmosdb.item_id', result?.id || 'unknown')
        return result
      } catch (error) {
        span.setAttribute('db.cosmosdb.error_code', error.code || 'UNKNOWN')
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace a CosmosDB update operation
   */
  traceUpdate: async (containerName, itemId, fn) => {
    return withSpan(`cosmosdb.update.${containerName}`, async (span) => {
      span.setAttribute('db.system', 'cosmosdb')
      span.setAttribute('db.name', process.env.COSMOS_DB_DATABASE || 'emr')
      span.setAttribute('db.cosmosdb.container', containerName)
      span.setAttribute('db.operation', 'update')
      span.setAttribute('db.cosmosdb.item_id', itemId)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('db.cosmosdb.duration_ms', Date.now() - startTime)
        return result
      } catch (error) {
        span.setAttribute('db.cosmosdb.error_code', error.code || 'UNKNOWN')
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace a CosmosDB delete operation
   */
  traceDelete: async (containerName, itemId, fn) => {
    return withSpan(`cosmosdb.delete.${containerName}`, async (span) => {
      span.setAttribute('db.system', 'cosmosdb')
      span.setAttribute('db.name', process.env.COSMOS_DB_DATABASE || 'emr')
      span.setAttribute('db.cosmosdb.container', containerName)
      span.setAttribute('db.operation', 'delete')
      span.setAttribute('db.cosmosdb.item_id', itemId)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('db.cosmosdb.duration_ms', Date.now() - startTime)
        return result
      } catch (error) {
        span.setAttribute('db.cosmosdb.error_code', error.code || 'UNKNOWN')
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },
}

/**
 * External API Instrumentation
 * Wraps external API calls with tracing
 */
const externalApiInstrumentation = {
  /**
   * Trace Razorpay API calls
   */
  traceRazorpay: async (operation, fn) => {
    return withSpan(`razorpay.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'razorpay')
      span.setAttribute('http.url', 'https://api.razorpay.com')
      span.setAttribute('razorpay.operation', operation)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('razorpay.duration_ms', Date.now() - startTime)
        span.setAttribute('razorpay.success', true)
        return result
      } catch (error) {
        span.setAttribute('razorpay.success', false)
        span.setAttribute('razorpay.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace ABDM (Ayushman Bharat) API calls
   */
  traceAbdm: async (operation, endpoint, fn) => {
    return withSpan(`abdm.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'abdm')
      span.setAttribute('abdm.operation', operation)
      span.setAttribute('abdm.endpoint', endpoint)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('abdm.duration_ms', Date.now() - startTime)
        span.setAttribute('abdm.success', true)
        return result
      } catch (error) {
        span.setAttribute('abdm.success', false)
        span.setAttribute('abdm.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace Azure OpenAI API calls
   */
  traceOpenAI: async (operation, model, fn) => {
    return withSpan(`openai.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'azure-openai')
      span.setAttribute('gen_ai.system', 'openai')
      span.setAttribute('gen_ai.request.model', model)
      span.setAttribute('openai.operation', operation)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('openai.duration_ms', Date.now() - startTime)
        span.setAttribute('openai.success', true)

        // Track token usage if available
        if (result?.usage) {
          span.setAttribute('gen_ai.usage.input_tokens', result.usage.prompt_tokens || 0)
          span.setAttribute('gen_ai.usage.output_tokens', result.usage.completion_tokens || 0)
        }

        return result
      } catch (error) {
        span.setAttribute('openai.success', false)
        span.setAttribute('openai.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace Azure B2C / Microsoft Graph API calls
   */
  traceGraphApi: async (operation, fn) => {
    return withSpan(`msgraph.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'microsoft-graph')
      span.setAttribute('msgraph.operation', operation)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('msgraph.duration_ms', Date.now() - startTime)
        span.setAttribute('msgraph.success', true)
        return result
      } catch (error) {
        span.setAttribute('msgraph.success', false)
        span.setAttribute('msgraph.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace Email Service calls
   */
  traceEmail: async (operation, recipient, fn) => {
    return withSpan(`email.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'smtp')
      span.setAttribute('email.operation', operation)
      span.setAttribute('email.recipient_domain', recipient.split('@')[1] || 'unknown')

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('email.duration_ms', Date.now() - startTime)
        span.setAttribute('email.success', true)
        return result
      } catch (error) {
        span.setAttribute('email.success', false)
        span.setAttribute('email.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace Redis cache operations
   */
  traceRedis: async (operation, key, fn) => {
    return withSpan(`redis.${operation}`, async (span) => {
      span.setAttribute('db.system', 'redis')
      span.setAttribute('db.operation', operation)
      span.setAttribute('db.redis.key_prefix', key.split(':')[0] || key)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('db.redis.duration_ms', Date.now() - startTime)
        span.setAttribute('db.redis.hit', result !== null && result !== undefined)
        return result
      } catch (error) {
        span.setAttribute('db.redis.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },

  /**
   * Trace Azure Blob Storage operations
   */
  traceBlobStorage: async (operation, containerName, blobName, fn) => {
    return withSpan(`blob.${operation}`, async (span) => {
      span.setAttribute('peer.service', 'azure-blob-storage')
      span.setAttribute('blob.operation', operation)
      span.setAttribute('blob.container', containerName)
      span.setAttribute('blob.name', blobName)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('blob.duration_ms', Date.now() - startTime)
        span.setAttribute('blob.success', true)
        return result
      } catch (error) {
        span.setAttribute('blob.success', false)
        span.setAttribute('blob.error', error.message)
        throw error
      }
    }, { attributes: { 'span.kind': 'client' } })
  },
}

/**
 * Business Logic Instrumentation
 * Wraps business operations with tracing
 */
const businessInstrumentation = {
  /**
   * Trace a handler operation
   */
  traceHandler: async (handlerName, operation, fn) => {
    return withSpan(`handler.${handlerName}.${operation}`, async (span) => {
      span.setAttribute('handler.name', handlerName)
      span.setAttribute('handler.operation', operation)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('handler.duration_ms', Date.now() - startTime)
        span.setAttribute('handler.success', true)
        return result
      } catch (error) {
        span.setAttribute('handler.success', false)
        span.setAttribute('handler.error', error.message)
        throw error
      }
    })
  },

  /**
   * Trace a service operation
   */
  traceService: async (serviceName, operation, fn) => {
    return withSpan(`service.${serviceName}.${operation}`, async (span) => {
      span.setAttribute('service.name', serviceName)
      span.setAttribute('service.operation', operation)

      const startTime = Date.now()
      try {
        const result = await fn()
        span.setAttribute('service.duration_ms', Date.now() - startTime)
        span.setAttribute('service.success', true)
        return result
      } catch (error) {
        span.setAttribute('service.success', false)
        span.setAttribute('service.error', error.message)
        throw error
      }
    })
  },

  /**
   * Add patient context to current span
   */
  addPatientContext: (patientId, organizationId) => {
    addSpanAttributes({
      'patient.id': patientId,
      'organization.id': organizationId,
    })
  },

  /**
   * Add user context to current span
   */
  addUserContext: (userId, email, role) => {
    addSpanAttributes({
      'user.id': userId,
      'user.email_domain': email?.split('@')[1] || 'unknown',
      'user.role': role,
    })
  },

  /**
   * Add appointment context to current span
   */
  addAppointmentContext: (appointmentId, doctorId, status) => {
    addSpanAttributes({
      'appointment.id': appointmentId,
      'appointment.doctor_id': doctorId,
      'appointment.status': status,
    })
  },
}

module.exports = {
  cosmosDbInstrumentation,
  externalApiInstrumentation,
  businessInstrumentation,
}
