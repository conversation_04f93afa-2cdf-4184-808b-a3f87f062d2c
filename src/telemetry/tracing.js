/**
 * OpenTelemetry Distributed Tracing Configuration
 * Grafana Cloud + Tempo Integration for EMR Microservice
 */

const { NodeSDK } = require('@opentelemetry/sdk-node')
const {
  getNodeAutoInstrumentations,
} = require('@opentelemetry/auto-instrumentations-node')
const {
  OTLPTraceExporter,
} = require('@opentelemetry/exporter-trace-otlp-http')
const {
  OTLPMetricExporter,
} = require('@opentelemetry/exporter-metrics-otlp-http')
const { PeriodicExportingMetricReader } = require('@opentelemetry/sdk-metrics')
const { resourceFromAttributes } = require('@opentelemetry/resources')
const {
  ATTR_SERVICE_NAME,
  ATTR_SERVICE_VERSION,
  ATTR_DEPLOYMENT_ENVIRONMENT,
} = require('@opentelemetry/semantic-conventions')
const {
  diag,
  DiagConsoleLogger,
  DiagLogLevel,
  trace,
  context,
  SpanStatusCode,
} = require('@opentelemetry/api')
const { W3CTraceContextPropagator } = require('@opentelemetry/core')

// Enable diagnostic logging in development
if (process.env.OTEL_DEBUG === 'true') {
  diag.setLogger(new DiagConsoleLogger(), DiagLogLevel.DEBUG)
}

/**
 * Grafana Cloud Configuration
 * These values come from your Grafana Cloud account
 */
const getGrafanaConfig = () => {
  const environment = process.env.environment || 'dev'

  return {
    // Grafana Cloud OTLP endpoint
    endpoint:
      process.env.GRAFANA_OTLP_ENDPOINT ||
      'https://otlp-gateway-prod-us-central-0.grafana.net/otlp',

    // Grafana Cloud Instance ID and API Key (Base64 encoded)
    authorization: process.env.GRAFANA_OTLP_TOKEN || '',

    // Service identification
    serviceName: process.env.OTEL_SERVICE_NAME || 'emr-microservice-qa',
    serviceVersion: process.env.npm_package_version || '1.0.0',
    environment: environment,

    // Sampling rate (1.0 = 100% of traces)
    samplingRate: environment === 'production' ? 0.1 : 1.0,
  }
}

/**
 * Create Resource with service metadata
 */
const createResource = (config) => {
  return resourceFromAttributes({
    [ATTR_SERVICE_NAME]: config.serviceName,
    [ATTR_SERVICE_VERSION]: config.serviceVersion,
    [ATTR_DEPLOYMENT_ENVIRONMENT]: config.environment,
    'service.namespace': 'arcaai',
    'service.instance.id': process.env.WEBSITE_INSTANCE_ID || 'local',
    'cloud.provider': 'azure',
    'cloud.platform': 'azure_functions',
    'cloud.region': process.env.REGION_NAME || 'unknown',
  })
}

/**
 * Create OTLP Trace Exporter for Grafana Cloud
 */
const createTraceExporter = (config) => {
  const authHeader = config.authorization.startsWith('glc_')
    ? `Bearer ${config.authorization}`
    : `Basic ${config.authorization}`

  return new OTLPTraceExporter({
    url: `${config.endpoint}/v1/traces`,
    headers: {
      Authorization: authHeader,
    },
    timeoutMillis: 30000,
  })
}

/**
 * Create OTLP Metric Exporter for Grafana Cloud
 */
const createMetricExporter = (config) => {
  // Grafana Cloud tokens starting with 'glc_' use Bearer auth
  const authHeader = config.authorization.startsWith('glc_')
    ? `Bearer ${config.authorization}`
    : `Basic ${config.authorization}`

  return new OTLPMetricExporter({
    url: `${config.endpoint}/v1/metrics`,
    headers: {
      Authorization: authHeader,
    },
    timeoutMillis: 30000,
  })
}

/**
 * Initialize OpenTelemetry SDK
 */
let sdk = null

const initializeTracing = () => {
  if (sdk) {
    console.log('[Telemetry] SDK already initialized')
    return sdk
  }

  const config = getGrafanaConfig()

  // Skip initialization if no token provided
  if (!config.authorization) {
    console.warn(
      '[Telemetry] GRAFANA_OTLP_TOKEN not set. Tracing disabled.',
    )
    return null
  }

  console.log(`[Telemetry] Initializing OpenTelemetry for ${config.serviceName}`)
  console.log(`[Telemetry] Environment: ${config.environment}`)
  console.log(`[Telemetry] Endpoint: ${config.endpoint}`)

  try {
    const resource = createResource(config)
    const traceExporter = createTraceExporter(config)

    sdk = new NodeSDK({
      resource,
      traceExporter,
      // Metrics disabled - requires separate Prometheus endpoint authentication
      textMapPropagator: new W3CTraceContextPropagator(),
      instrumentations: [
        getNodeAutoInstrumentations({
          // HTTP instrumentation
          '@opentelemetry/instrumentation-http': {
            enabled: true,
            ignoreIncomingRequestHook: (request) => {
              // Ignore health check endpoints and OPTIONS requests
              const ignorePaths = ['/health', '/api/health', '/favicon.ico']
              const shouldIgnore = ignorePaths.some((path) => request.url?.includes(path))

              // Also ignore OPTIONS requests (CORS preflight)
              if (request.method === 'OPTIONS') {
                return true
              }

              return shouldIgnore
            },
            // Hook for incoming requests (server-side)
            startIncomingSpanHook: (request) => {
              // Extract route from URL for incoming requests
              const route = request.url?.split('?')[0] || request.url || '/'
              const method = request.method || 'GET'
              return {
                'http.route': route,
                'http.method': method,
                'span.name': `${method} ${route}`,
              }
            },
            // Hook for outgoing requests (client-side, e.g., CosmosDB calls)
            startOutgoingSpanHook: (request) => {
              return {
                'http.request.id': request.getHeader?.('x-request-id') || '',
              }
            },
            // Server request hook - update span name after creation
            serverName: 'emr-microservice',
            requestHook: (span, request) => {
              const method = request.method || 'UNKNOWN'

              // For incoming server requests (API calls to our service)
              if (request.url && !request.hostname) {
                // This is an incoming request (no hostname means server-side)
                const fullUrl = request.url || '/'
                const route = fullUrl.split('?')[0]

                // Update span name to include full route
                span.updateName(`${method} ${route}`)
                span.setAttribute('http.route', route)
                span.setAttribute('http.target', fullUrl)
                span.setAttribute('http.url', fullUrl)
                span.setAttribute('api.endpoint', route)
                span.setAttribute('url.path', route)

                // Extract query parameters if present
                if (fullUrl.includes('?')) {
                  const queryString = fullUrl.split('?')[1]
                  span.setAttribute('http.query', queryString)
                }
              } else if (request.hostname || request.host) {
                const host = request.hostname || request.host
                span.setAttribute('peer.service', host)

                if (request.path) {
                  span.updateName(`${method} ${host}${request.path}`)
                  span.setAttribute('http.target', request.path)
                }
              }              
            },
            responseHook: (span, response) => {
              // Add response details
              const statusCode = response.statusCode || 0
              span.setAttribute('http.status_code', statusCode)
              span.setAttribute('http.response.content_length', response.headers?.['content-length'] || 0)

              // Mark span as error if status code is 4xx or 5xx
              if (statusCode >= 400) {
                span.setStatus({
                  code: statusCode >= 500 ? SpanStatusCode.ERROR : SpanStatusCode.OK,
                  message: statusCode >= 500 ? 'Server Error' : 'Client Error'
                })

                // For 5xx errors, record as exception
                if (statusCode >= 500) {
                  span.recordException(new Error(`HTTP ${statusCode} - Server Error`))
                }
              }
            },
          },
          // Disable file system instrumentation (too noisy)
          '@opentelemetry/instrumentation-fs': {
            enabled: false,
          },
          // Redis instrumentation
          '@opentelemetry/instrumentation-redis-4': {
            enabled: true,
          },
        }),
      ],
    })

    sdk.start()
    console.log('[Telemetry] OpenTelemetry SDK started successfully')

    // Graceful shutdown
    process.on('SIGTERM', () => {
      sdk
        .shutdown()
        .then(() => console.log('[Telemetry] SDK shut down successfully'))
        .catch((error) => console.error('[Telemetry] Error shutting down SDK:', error))
    })

    return sdk
  } catch (error) {
    console.error('[Telemetry] Failed to initialize OpenTelemetry:', error)
    return null
  }
}

/**
 * Get the current tracer
 */
const getTracer = (name = 'emr-microservice') => {
  return trace.getTracer(name)
}

/**
 * Create a new span for custom instrumentation
 */
const createSpan = (name, options = {}) => {
  const tracer = getTracer()
  return tracer.startSpan(name, options)
}

/**
 * Execute a function within a span context
 */
const withSpan = async (spanName, fn, options = {}) => {
  const tracer = getTracer()
  return tracer.startActiveSpan(spanName, options, async (span) => {
    try {
      const result = await fn(span)
      span.setStatus({ code: SpanStatusCode.OK })
      return result
    } catch (error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      })
      span.recordException(error)
      throw error
    } finally {
      span.end()
    }
  })
}

/**
 * Add attributes to the current span
 */
const addSpanAttributes = (attributes) => {
  const currentSpan = trace.getActiveSpan()
  if (currentSpan) {
    Object.entries(attributes).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        currentSpan.setAttribute(key, value)
      }
    })
  }
}

/**
 * Record an exception on the current span
 */
const recordException = (error) => {
  const currentSpan = trace.getActiveSpan()
  if (currentSpan) {
    currentSpan.recordException(error)
    currentSpan.setStatus({
      code: SpanStatusCode.ERROR,
      message: error.message,
    })
  }
}

/**
 * Get current trace context for propagation
 */
const getTraceContext = () => {
  const currentSpan = trace.getActiveSpan()
  if (!currentSpan) return null

  const spanContext = currentSpan.spanContext()
  return {
    traceId: spanContext.traceId,
    spanId: spanContext.spanId,
    traceFlags: spanContext.traceFlags,
  }
}

module.exports = {
  initializeTracing,
  getTracer,
  createSpan,
  withSpan,
  addSpanAttributes,
  recordException,
  getTraceContext,
  trace,
  context,
  SpanStatusCode,
}
