const Razorpay = require('razorpay')
const crypto = require('crypto')
const { v4: uuidv4 } = require('uuid')
const paymentRepository = require('../repositories/payment-repository')
const PaymentModel = require('../models/payment-model')
const { PaymentStatus } = require('../common/constant')
const logging = require('../common/logging')
const secretManager = require('./secret-manager')
const paymentTokenizationService = require('./payment-tokenization-service')
const paymentTokenRepository = require('../repositories/payment-token-repository')
const {
  PaymentTokenModel,
  PaymentSessionTokenModel,
} = require('../models/payment-token-model')

class PaymentService {
  constructor() {
    this.razorpay = null
    this.webhookSecret = null
    this.isInitialized = false
    this.initializationPromise = null
  }

  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._doInitialize()
    return this.initializationPromise
  }

  async _doInitialize() {
    try {
      logging.logInfo('Initializing Payment Service with Key Vault secrets...')

      // Get secrets from Key Vault or fallback to environment variables
      const keyId = await secretManager.getSecret('RAZORPAY_KEY_ID')
      const keySecret = await secretManager.getSecret('RAZORPAY_KEY_SECRET')
      this.webhookSecret = await secretManager.getSecret(
        'RAZORPAY_WEBHOOK_SECRET',
      )

      if (!keyId || !keySecret) {
        throw new Error(
          'Missing Razorpay credentials in Key Vault or environment variables',
        )
      }

      this.razorpay = new Razorpay({
        key_id: keyId,
        key_secret: keySecret,
      })

      this.isInitialized = true
      logging.logInfo('Payment Service initialized successfully')
    } catch (error) {
      logging.logError('Failed to initialize Payment Service:', error)
      this.isInitialized = false
      throw error
    }
  }

  async getRazorpayInstance() {
    if (!this.isInitialized) {
      await this.initialize()
    }

    return this.razorpay
  }

  async createOrder(paymentData) {
    try {
      const payment = new PaymentModel(paymentData)

      // Create Razorpay order first
      const orderOptions = {
        amount: payment.amount,
        currency: payment.currency,
        receipt: `receipt_${Date.now()}`,
        notes: {
          paymentType: payment.paymentType,
          patientId: payment.patientId || '',
          subscriberEmail: payment.subscriberEmail || '',
          organizationId: payment.organizationId,
          description: payment.description,
          ...payment.metadata,
        },
      }
      const razorpay = await this.getRazorpayInstance()
      const razorpayOrder = await razorpay.orders.create(orderOptions)

      payment.razorpayOrderId = razorpayOrder.id
      payment.id = uuidv4()

      // Save payment record (without sensitive data)
      const savedPayment = await paymentRepository.createPayment(payment)

      // Create payment reference token for secure handling
      const paymentReference = {
        paymentId: savedPayment.id,
        organizationId: payment.organizationId,
        amount: payment.amount,
        timestamp: Date.now(),
      }

      const paymentReferenceToken =
        await paymentTokenizationService.createPaymentReferenceToken(
          paymentReference,
        )

      // Create payment token record
      const paymentToken = new PaymentTokenModel({
        paymentId: savedPayment.id,
        organizationId: payment.organizationId,
        paymentReferenceToken: paymentReferenceToken,
        amount: payment.amount,
        currency: payment.currency,
        paymentType: payment.paymentType,
        status: payment.status,
        tokenExpiresAt: new Date(
          Date.now() + 24 * 60 * 60 * 1000,
        ).toISOString(), // 24 hours
        maxTokenUsage: 5, // Allow multiple verification attempts
        createdBy: payment.created_by,
      })

      await paymentTokenRepository.createPaymentToken(paymentToken)

      // Create payment session token for frontend
      const sessionData = {
        paymentId: savedPayment.id,
        userId: payment.created_by,
        organizationId: payment.organizationId,
      }

      const sessionToken =
        await paymentTokenizationService.generatePaymentSessionToken(
          sessionData,
        )

      const keyId = await secretManager.getSecret('RAZORPAY_KEY_ID')

      return {
        success: true,
        data: {
          orderId: razorpayOrder.id,
          paymentId: savedPayment.id,
          paymentReferenceToken: paymentReferenceToken,
          sessionToken: sessionToken,
          keyId: keyId,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
          subscriberEmail: payment.subscriberEmail || null,
        },
      }
    } catch (error) {
      logging.logError('Error creating payment order', error)
      throw new Error(`Failed to create payment order: ${error.message}`)
    }
  }

  async verifyPayment(verificationData) {
    try {
      await this.initialize() // Ensure service is initialized
      const {
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
        paymentReferenceToken,
        sessionToken,
      } = verificationData

      // Validate tokens if provided
      if (paymentReferenceToken || sessionToken) {
        const isTokenValid = await this.validatePaymentTokens(
          paymentReferenceToken,
          sessionToken,
          razorpay_order_id,
        )

        if (!isTokenValid) {
          return {
            success: false,
            verified: false,
            message: 'Invalid payment tokens',
          }
        }
      }

      const body = razorpay_order_id + '|' + razorpay_payment_id

      // Get Razorpay key secret for signature verification
      const razorpayKeySecret = await secretManager.getSecret(
        'RAZORPAY_KEY_SECRET',
      )
      const expectedSignature = crypto
        .createHmac('sha256', razorpayKeySecret)
        .update(body.toString())
        .digest('hex')

      const isSignatureValid = expectedSignature === razorpay_signature

      if (isSignatureValid) {
        const payment = await paymentRepository.getPaymentByRazorpayOrderId(
          razorpay_order_id,
        )

        if (!payment) {
          throw new Error('Payment record not found')
        }

        // Tokenize sensitive payment data before storing
        const tokenizedPaymentData =
          await paymentTokenizationService.tokenizePaymentData({
            razorpayPaymentId: razorpay_payment_id,
            razorpaySignature: razorpay_signature,
          })

        payment.status = PaymentStatus.COMPLETED
        // Store tokenized sensitive data instead of plain text
        payment.razorpayPaymentId = tokenizedPaymentData.razorpayPaymentId
        payment.razorpaySignature = tokenizedPaymentData.razorpaySignature
        payment.verifiedAt = new Date().toISOString()

        await paymentRepository.updatePayment(payment)

        // Update payment token usage
        if (paymentReferenceToken) {
          await this.markTokenAsUsed(payment.id, verificationData.userId)
        }

        // Return response without sensitive data
        const safePayment = { ...payment }
        delete safePayment._tokenMap // Remove token mapping from response

        return {
          success: true,
          verified: true,
          message: 'Payment verified successfully',
          paymentId: payment.id, // Use our internal payment ID, not Razorpay's
          payment: safePayment,
        }
      } else {
        // Log failed verification attempt
        logging.logWarning('Payment signature verification failed', {
          razorpay_order_id,
          expected_signature: expectedSignature,
          received_signature: razorpay_signature,
        })

        return {
          success: false,
          verified: false,
          message: 'Invalid payment signature',
        }
      }
    } catch (error) {
      logging.logError('Error verifying payment', error)
      throw new Error(`Payment verification failed: ${error.message}`)
    }
  }

  async handleWebhook(webhookData, signature) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(JSON.stringify(webhookData))
        .digest('hex')

      if (expectedSignature !== signature) {
        throw new Error('Invalid webhook signature')
      }

      const { event, payload } = webhookData

      const paymentEntity = payload.payment.entity

      const orderId = paymentEntity.order_id
      const paymentId = paymentEntity.id

      const errorDescription = paymentEntity.error_description || null

      const payments = await paymentRepository.getPaymentByRazorpayOrderId(
        orderId,
      )

      if (!payments) {
        logging.logError(`Payment record not found for order ID: ${orderId}`)
        return { success: false, message: 'Payment record not found' }
      }

      const paymentRecord = payments

      switch (event) {
        case 'payment.captured':
          paymentRecord.status = PaymentStatus.COMPLETED
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        case 'payment.failed':
          paymentRecord.status = PaymentStatus.FAILED
          paymentRecord.failureReason = errorDescription || 'Payment failed'
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        default:
          logging.logInfo(`Unhandled webhook event: ${event}`)
          return { success: true, message: 'Event not processed' }
      }

      await paymentRepository.updatePayment(paymentRecord)

      return {
        success: true,
        message: `Webhook processed for event: ${event}`,
        paymentId: paymentRecord.id,
      }
    } catch (error) {
      logging.logError('Error handling webhook', error)
      throw new Error(`Webhook processing failed: ${error.message}`)
    }
  }

  async getPaymentById(id) {
    try {
      return await paymentRepository.getPaymentById(id)
    } catch (error) {
      logging.logError('Error fetching payment details', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  async getPaymentsByOrganization(
    organizationId = null,
    pageSize = 20,
    continuationToken = null,
    filters = {},
  ) {
    try {
      const result = await paymentRepository.getPaymentsByOrganization(
        organizationId,
        filters,
        continuationToken,
        pageSize,
      )
      return {
        payments: result.items || [],
        continuationToken: result.continuationToken,
        hasMoreResults: result.hasMoreResults || false,
        pageSize: pageSize,
        itemCount: result.items ? result.items.length : 0,
      }
    } catch (error) {
      logging.logError('Error fetching organization payments', error)
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  async getPaymentStatistics(organizationId, startDate = null, endDate = null) {
    try {
      const payments = await paymentRepository.getPaymentStatistics(
        organizationId,
        startDate,
        endDate,
      )

      const totalAmount = payments.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      )
      const totalCount = payments.length

      const paymentTypeStats = {}
      payments.forEach((payment) => {
        if (!paymentTypeStats[payment.paymentType]) {
          paymentTypeStats[payment.paymentType] = {
            count: 0,
            amount: 0,
          }
        }
        paymentTypeStats[payment.paymentType].count++
        paymentTypeStats[payment.paymentType].amount += payment.amount
      })

      const monthlyStats = {}
      payments.forEach((payment) => {
        const month = new Date(payment.createdAt).toISOString().substring(0, 7)
        if (!monthlyStats[month]) {
          monthlyStats[month] = {
            count: 0,
            amount: 0,
          }
        }
        monthlyStats[month].count++
        monthlyStats[month].amount += payment.amount
      })

      return {
        totalAmount: totalAmount / 100,
        totalCount,
        paymentTypeBreakdown: Object.keys(paymentTypeStats).map((type) => ({
          type,
          count: paymentTypeStats[type].count,
          amount: paymentTypeStats[type].amount / 100,
        })),
        monthlyBreakdown: Object.keys(monthlyStats)
          .map((month) => ({
            month,
            count: monthlyStats[month].count,
            amount: monthlyStats[month].amount / 100,
          }))
          .sort((a, b) => a.month.localeCompare(b.month)),
      }
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  async updatePaymentStatus(paymentId, status, reason = null) {
    try {
      const payment = await this.getPaymentById(paymentId)

      payment.status = status
      if (reason) {
        payment.failureReason = reason
      }

      const updatedPayment = await paymentRepository.updatePayment(payment)
      return updatedPayment
    } catch (error) {
      logging.logError('Error updating payment status', error)
      throw new Error(`Failed to update payment status: ${error.message}`)
    }
  }

  /**
   * Validate payment tokens
   * @param {string} paymentReferenceToken - Payment reference token
   * @param {string} sessionToken - Session token
   * @param {string} razorpayOrderId - Razorpay order ID for validation
   * @returns {boolean} - True if tokens are valid
   */
  async validatePaymentTokens(
    paymentReferenceToken,
    sessionToken,
    razorpayOrderId,
  ) {
    try {
      let isValid = true

      // Validate payment reference token
      if (paymentReferenceToken) {
        const payment = await paymentRepository.getPaymentByRazorpayOrderId(
          razorpayOrderId,
        )
        if (!payment) {
          return false
        }

        const paymentToken =
          await paymentTokenRepository.getPaymentTokenByPaymentId(payment.id)
        if (!paymentToken || !paymentToken.isValidForUse()) {
          logging.logWarning('Invalid or expired payment reference token', {
            paymentId: payment.id,
            tokenValid: paymentToken?.isValidForUse(),
          })
          return false
        }

        // Validate the token against stored reference
        const expectedReference = {
          paymentId: payment.id,
          organizationId: payment.organizationId,
          amount: payment.amount,
          timestamp: new Date(paymentToken.tokenCreatedAt).getTime(),
        }

        const isReferenceValid =
          await paymentTokenizationService.validatePaymentReferenceToken(
            paymentReferenceToken,
            expectedReference,
          )

        if (!isReferenceValid) {
          logging.logWarning('Payment reference token validation failed')
          return false
        }
      }

      // Validate session token
      if (sessionToken) {
        // Session token validation would depend on your session management
        // For now, we'll validate it exists and is not expired
        const isSessionValid = await this.validateSessionToken(sessionToken)
        if (!isSessionValid) {
          logging.logWarning('Session token validation failed')
          return false
        }
      }

      return isValid
    } catch (error) {
      logging.logError('Error validating payment tokens:', error)
      return false
    }
  }

  /**
   * Validate session token
   * @param {string} sessionToken - Session token to validate
   * @returns {boolean} - True if valid
   */
  async validateSessionToken(sessionToken) {
    try {
      // This is a simplified validation - you might want to decode and validate the session data
      return sessionToken && sessionToken.length > 0
    } catch (error) {
      logging.logError('Error validating session token:', error)
      return false
    }
  }

  /**
   * Mark payment token as used
   * @param {string} paymentId - Payment ID
   * @param {string} userId - User ID who used the token
   */
  async markTokenAsUsed(paymentId, userId = null) {
    try {
      const paymentToken =
        await paymentTokenRepository.getPaymentTokenByPaymentId(paymentId)
      if (paymentToken) {
        paymentToken.markAsUsed(userId)
        await paymentTokenRepository.updatePaymentToken(paymentToken)

        logging.logInfo('Payment token marked as used', {
          paymentId,
          tokenId: paymentToken.id,
          usageCount: paymentToken.tokenUsageCount,
        })
      }
    } catch (error) {
      logging.logError('Error marking token as used:', error)
      // Don't throw error as this is not critical for payment processing
    }
  }

  /**
   * Clean up expired payment tokens
   * @returns {number} - Number of tokens cleaned up
   */
  async cleanupExpiredTokens() {
    try {
      return await paymentTokenRepository.cleanupExpiredTokens()
    } catch (error) {
      logging.logError('Error cleaning up expired tokens:', error)
      throw new Error(`Failed to cleanup expired tokens: ${error.message}`)
    }
  }

  /**
   * Get payment by ID with token validation
   * @param {string} paymentId - Payment ID
   * @param {string} paymentReferenceToken - Optional payment reference token for validation
   * @returns {Object} - Payment data (sanitized)
   */
  async getSecurePaymentById(paymentId, paymentReferenceToken = null) {
    try {
      const payment = await this.getPaymentById(paymentId)

      // If token provided, validate it
      if (paymentReferenceToken) {
        const paymentToken =
          await paymentTokenRepository.getPaymentTokenByPaymentId(paymentId)
        if (!paymentToken || !paymentToken.isValidForUse()) {
          throw new Error('Invalid or expired payment token')
        }
      }

      // Return sanitized payment data (remove sensitive tokenized fields)
      const safePayment = { ...payment }
      delete safePayment._tokenMap

      return safePayment
    } catch (error) {
      logging.logError('Error fetching secure payment:', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }
}

module.exports = new PaymentService()
