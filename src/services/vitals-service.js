const { parseJSON } = require('../common/helper')
const logging = require('../common/logging')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const patientService = require('./patient-service')
const {
  calculateAge,
  getAgeGroup,
  evaluateVitalSigns,
} = require('../utils/vitals-utils')
const vitalsContainer = 'Vitals'

class VitalsService {
  async addVitals(vitals) {
    try {
      var res = await cosmosDbContext.createItem(vitals, vitalsContainer)
      return res
    } catch (error) {
      logging.logError(`Unable to add Vitals ${parseJSON(vitals)}`, error)
      return null
    }
  }

  async updateVitals(vitals) {
    try {
      var res = await cosmosDbContext.updateItem(vitals, vitalsContainer)
      return res || null
    } catch (error) {
      logging.logError(`Unable to update Vitals ${parseJSON(vitals)}`, error)
      return null
    }
  }

  async getVitalsByPatient(patientId) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' ORDER BY c._ts DESC`
      var data = await cosmosDbContext.queryItems(query, vitalsContainer)

      let ageGroup = null
      let patientAge = null
      const patient = await patientService.GetPatientProfile(patientId)
      if (patient) {
        if (patient.age) {
          patientAge = patient.age
        } else if (patient.dob) {
          patientAge = calculateAge(patient.dob)
        }
        if (patientAge !== null) {
          ageGroup = getAgeGroup(patientAge)
        }
      }

      if (data && Array.isArray(data)) {
        data = data.map((vitalRecord) => {
          const statuses = evaluateVitalSigns(
            vitalRecord.vitals || {},
            ageGroup,
          )
          return {
            ...vitalRecord,
            vitalStatuses: statuses,
            ageGroup: ageGroup,
            patientAge: patientAge,
          }
        })
      }

      return data
    } catch (error) {
      logging.logError(
        `Unable to get Vitals by patient ID: ${patientId}`,
        error,
      )
      return null
    }
  }

  async getVitalsByPatientIdAndDateRange(patientId, startDate, endDate) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c._ts >= ${
        new Date(startDate).getTime() / 1000
      } AND c._ts <= ${new Date(endDate).getTime() / 1000} ORDER BY c._ts DESC`
      var data = await cosmosDbContext.queryItems(query, vitalsContainer)

      // Get patient information to determine age group
      let ageGroup = null
      let patientAge = null
      const patient = await patientService.GetPatientProfile(patientId)
      if (patient) {
        if (patient.age) {
          patientAge = patient.age
        } else if (patient.dob) {
          patientAge = calculateAge(patient.dob)
        }
        if (patientAge !== null) {
          ageGroup = getAgeGroup(patientAge)
        }
      }

      if (data && Array.isArray(data)) {
        data = data.map((vitalRecord) => {
          const statuses = evaluateVitalSigns(
            vitalRecord.vitals || {},
            ageGroup,
          )
          return {
            ...vitalRecord,
            vitalStatuses: statuses,
            ageGroup: ageGroup,
            patientAge: patientAge,
          }
        })
      }

      return data
    } catch (error) {
      logging.logError(
        `Unable to get Vitals by patient ID and date range: ${patientId}`,
        error,
      )
      return null
    }
  }

  async getVitalsByQuery(query) {
    try {
      var data = await cosmosDbContext.queryItems(query, vitalsContainer)

      // Try to add status indicators if we can determine patient ID from the data
      if (data && Array.isArray(data) && data.length > 0) {
        // Group by patient ID to minimize patient service calls
        const patientIds = [
          ...new Set(data.map((record) => record.patientId).filter(Boolean)),
        ]

        const patientAgeGroups = {}
        for (const patientId of patientIds) {
          try {
            const patient = await patientService.GetPatientProfile(patientId)
            if (patient && patient.dob) {
              const age = calculateAge(patient.dob)
              patientAgeGroups[patientId] = {
                ageGroup: getAgeGroup(age),
                age: age,
              }
            }
          } catch (error) {
            logging.logError(
              `Error getting patient data for vitals evaluation: ${patientId}`,
              error,
            )
          }
        }

        // Add status indicators to each vital record
        data = data.map((vitalRecord) => {
          const patientData = patientAgeGroups[vitalRecord.patientId]
          const ageGroup = patientData ? patientData.ageGroup : null
          const statuses = evaluateVitalSigns(
            vitalRecord.vitals || {},
            ageGroup,
          )

          return {
            ...vitalRecord,
            vitalStatuses: statuses,
            ageGroup: ageGroup,
            patientAge: patientData ? patientData.age : null,
          }
        })
      }

      return data
    } catch (error) {
      logging.logError(`Unable to get Vitals by query`, error)
      return null
    }
  }
}

module.exports = new VitalsService()
