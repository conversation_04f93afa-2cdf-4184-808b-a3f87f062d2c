const demographicsRepository = require('../repositories/demographics-repository');
const logging = require('../common/logging');

class DemographicsService {
    async createDemographics(demographics) {
        try {
            return await demographicsRepository.create(demographics);
        } catch (error) {
            logging.logError(
                `Unable to create demographics for patient ${demographics.patientId}`,
                error
            );
            throw error;
        }
    }

    async getDemographicsByPatientId(patientId) {
        try {
            return await demographicsRepository.getByPatientId(patientId);
        } catch (error) {
            logging.logError(
                `Unable to get demographics for patient ${patientId}`,
                error
            );
            throw error;
        }
    }

    async getDemographicsById(demographicsId) {
        try {
            return await demographicsRepository.getById(demographicsId);
        } catch (error) {
            logging.logError(
                `Unable to get demographics by id ${demographicsId}`,
                error
            );
            throw error;
        }
    }

    async updateDemographics(demographics) {
        try {
            return await demographicsRepository.update(demographics);
        } catch (error) {
            logging.logError(
                `Unable to update demographics for patient ${demographics.patientId}`,
                error
            );
            throw error;
        }
    }

    async deleteDemographics(demographicsId) {
        try {
            return await demographicsRepository.delete(demographicsId);
        } catch (error) {
            logging.logError(
                `Unable to delete demographics with id ${demographicsId}`,
                error
            );
            throw error;
        }
    }

    async getDemographicsByQuery(query) {
        try {
            return await demographicsRepository.getByQuery(query);
        } catch (error) {
            logging.logError(
                `Unable to get demographics by query: ${query}`,
                error
            );
            throw error;
        }
    }
}

module.exports = new DemographicsService();