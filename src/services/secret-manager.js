/**
 * Secret Manager Service
 * Manages secret retrieval from Azure Key Vault with caching and fallback to environment variables
 */

const keyVaultConfig = require('../config/keyvault-config')
const NodeCache = require('node-cache')

const logInfo = (message, ...args) => console.log(`[INFO] ${message}`, ...args)
const logError = (message, ...args) =>
  console.error(`[ERROR] ${message}`, ...args)
const logWarning = (message, ...args) =>
  console.warn(`[WARNING] ${message}`, ...args)

class SecretManager {
  constructor() {
    // Cache secrets for 30 minutes to improve performance
    this.cache = new NodeCache({ stdTTL: 1800, checkperiod: 300 })
    this.isKeyVaultAvailable = null
    this.keyVaultCheckTime = null

    // Mapping of environment variable names to Key Vault secret names
    // Key Vault secret names must match exactly what's in Azure Key Vault
    this.secretMapping = {
      // Cosmos DB
      COSMOS_DB_CONNECTIONSTRING: 'COSMOS-DB-CONNECTIONSTRING',
      COSMOS_DB_DATABASE: 'COSMOS-DB-DATABASE',

      // Azure B2C / Graph API
      CLIENT_ID: 'CLIENT-ID',
      CLIENT_SECRET: 'CLIENT-SECRET',
      TENANT_ID: 'TENANT-ID',
      TENANT_NAME: 'TENANT-NAME',

      // OpenAI
      OPENAI_ENDPOINT: 'OPENAI-ENDPOINT',
      OPENAI_KEY: 'OPENAI-KEY',
      OPENAI_MODEL: 'OPENAI-MODEL',

      // Redis
      REDIS_CONNECTION_STRING: 'REDIS-CONNECTION-STRING',
      REDISCACHEHOSTNAME: 'REDISCACHEHOSTNAME',
      REDISCACHEKEY: 'REDISCACHEKEY',

      // Email
      EMAIL_USER: 'EMAIL-USER',
      EMAIL_PASSWORD: 'EMAIL-PASSWORD',

      // JWT
      JWT_SECRET: 'JWT-SECRET',

      // Razorpay
      RAZORPAY_KEY_ID: 'RAZORPAY-KEY-ID',
      RAZORPAY_KEY_SECRET: 'RAZORPAY-KEY-SECRET',
      RAZORPAY_WEBHOOK_SECRET: 'RAZORPAY-WEBHOOK-SECRET',

      // ABDM
      ABDM_CLIENT_ID: 'ABDM-CLIENT-ID',
      ABDM_CLIENT_SECRET: 'ABDM-CLIENT-SECRET',
      ABDM_BASE_URL: 'ABDM-BASE-URL',
      ABDM_AUTH_URL: 'ABDM-AUTH-URL',
      ABDM_SESSION_URL: 'ABDM-SESSION-URL',
      ABDM_AUTH_URL: 'ABDM-AUTH-URL',
      ABDM_SESSION_URL: 'ABDM-SESSION-URL',

      // B2C Additional
      signin_policy: 'signin-policy',
      BASE_ADMIN_URL: 'BASE-ADMIN-URL',
      BASE_URL: 'BASE-URL',

      // Azure Storage - CRITICAL for timer functions
      AzureWebJobsStorage: 'AzureWebJobsStorage',
      AZURE_STORAGE_ACCOUNT_NAME: 'AZURE-STORAGE-ACCOUNT-NAME',
      AZURE_STORAGE_ACCOUNT_KEY: 'AZURE-STORAGE-ACCOUNT-KEY',

      // Application Insights
      APPLICATIONINSIGHTS_CONNECTION_STRING:
        'APPLICATIONINSIGHTS-CONNECTION-STRING',
      APPINSIGHTS_INSTRUMENTATIONKEY: 'APPINSIGHTS-INSTRUMENTATIONKEY',

      // NOTE: ENCRYPTION_SECRET_KEY is not stored in Key Vault - it uses default value from code

      // URLs
      ARCAQUEST_EHR_BASE_URL: 'ARCAQUEST-EHR-BASE-URL',
      OCR_SERVICE_URL: 'OCR-SERVICE-URL',
      REDASH_URL: 'REDASH-URL',
      API_BASE_URL: 'API-BASE-URL',

      // Application Configuration
      SummaryInfo: 'SummaryInfo',
      EXPIRED_TIME: 'EXPIRED-TIME',
      environment: 'environment',
      INTERNAL_API_BASE: 'INTERNAL-API-BASE',
      INTERNAL_API_BASE: 'INTERNAL-API-BASE',

      // Docker Registry
      DOCKER_CUSTOM_IMAGE_NAME: 'DOCKER-CUSTOM-IMAGE-NAME',
      DOCKER_REGISTRY_SERVER_URL: 'DOCKER-REGISTRY-SERVER-URL',
      DOCKER_REGISTRY_SERVER_USERNAME: 'DOCKER-REGISTRY-SERVER-USERNAME',
      DOCKER_REGISTRY_SERVER_PASSWORD: 'DOCKER-REGISTRY-SERVER-PASSWORD',

      // Azure Function Configuration
      WEBSITE_CONTENTAZUREFILECONNECTIONSTRING:
        'WEBSITE-CONTENTAZUREFILECONNECTIONSTRING',
      WEBSITE_CONTENTSHARE: 'WEBSITE-CONTENTSHARE',
      FUNCTIONS_EXTENSION_VERSION: 'FUNCTIONS-EXTENSION-VERSION',
      WEBSITES_ENABLE_APP_SERVICE_STORAGE:
        'WEBSITES-ENABLE-APP-SERVICE-STORAGE',
      FUNCTIONS_REQUEST_BODY_SIZE_LIMIT: 'FUNCTIONS-REQUEST-BODY-SIZE-LIMIT',
      FUNCTIONS_WORKER_RUNTIME: 'FUNCTIONS-WORKER-RUNTIME',
      WEBSITE_ENABLE_SYNC_UPDATE_SITE: 'WEBSITE-ENABLE-SYNC-UPDATE-SITE',
      WEBSITE_HTTPLOGGING_RETENTION_DAYS: 'WEBSITE-HTTPLOGGING-RETENTION-DAYS',
    }
  }

  /**
   * Get a secret value by environment variable name
   * @param {string} envVarName - Environment variable name
   * @param {string} defaultValue - Default value if secret not found
   * @returns {Promise<string>} - Secret value
   */
  async getSecret(envVarName, defaultValue = null) {
    try {
      // Check cache first
      const cacheKey = `secret_${envVarName}`
      const cachedValue = this.cache.get(cacheKey)
      if (cachedValue !== undefined) {
        return cachedValue
      }

      // Check if Key Vault is available
      const keyVaultAvailable = await this.isKeyVaultAccessible()

      if (keyVaultAvailable && this.secretMapping[envVarName]) {
        try {
          const secretName = this.secretMapping[envVarName]
          const secretValue = await keyVaultConfig.getSecret(secretName)

          // Cache the value
          this.cache.set(cacheKey, secretValue)
          logInfo(`Retrieved secret '${envVarName}' from Key Vault`)
          return secretValue
        } catch (error) {
          logWarning(
            `Failed to retrieve '${envVarName}' from Key Vault, falling back to environment variable:`,
            error,
          )
        }
      }

      // Fallback to environment variable
      const envValue = process.env[envVarName] || defaultValue
      if (envValue) {
        // Cache the environment value too (shorter TTL)
        this.cache.set(cacheKey, envValue, 300) // 5 minutes for env vars
        logInfo(`Using environment variable for '${envVarName}'`)
        return envValue
      }

      logWarning(
        `Secret '${envVarName}' not found in Key Vault or environment variables`,
      )
      return defaultValue
    } catch (error) {
      logError(`Error retrieving secret '${envVarName}':`, error)

      // Final fallback to environment variable
      const envValue = process.env[envVarName] || defaultValue
      if (envValue) {
        logWarning(
          `Using environment variable as final fallback for '${envVarName}'`,
        )
        return envValue
      }

      return defaultValue
    }
  }

  /**
   * Get multiple secrets at once
   * @param {string[]} envVarNames - Array of environment variable names
   * @returns {Promise<Object>} - Object with env var names as keys and secret values
   */
  async getSecrets(envVarNames) {
    const secrets = {}
    const promises = envVarNames.map(async (envVarName) => {
      secrets[envVarName] = await this.getSecret(envVarName)
    })

    await Promise.all(promises)
    return secrets
  }

  /**
   * Preload critical secrets into cache
   * @param {string[]} criticalSecrets - Array of critical secret names
   */
  async preloadSecrets(criticalSecrets = []) {
    try {
      logInfo('Preloading critical secrets...')

      const defaultCriticalSecrets = [
        'COSMOS_DB_CONNECTIONSTRING',
        'COSMOS_DB_DATABASE',
        'CLIENT_ID',
        'CLIENT_SECRET',
        'TENANT_ID',
        'JWT_SECRET',
      ]

      const secretsToLoad =
        criticalSecrets.length > 0 ? criticalSecrets : defaultCriticalSecrets
      await this.getSecrets(secretsToLoad)

      logInfo(`Preloaded ${secretsToLoad.length} critical secrets`)
    } catch (error) {
      logError('Error preloading secrets:', error)
    }
  }

  /**
   * Check if Key Vault is accessible (with caching to avoid repeated checks)
   * @returns {Promise<boolean>}
   */
  async isKeyVaultAccessible() {
    try {
      const now = Date.now()

      // Cache the Key Vault availability check for 5 minutes
      if (this.keyVaultCheckTime && now - this.keyVaultCheckTime < 300000) {
        return this.isKeyVaultAvailable
      }

      this.isKeyVaultAvailable = await keyVaultConfig.isAvailable()
      this.keyVaultCheckTime = now

      return this.isKeyVaultAvailable
    } catch (error) {
      logError('Error checking Key Vault accessibility:', error)
      return false
    }
  }

  /**
   * Clear the secret cache
   */
  clearCache() {
    this.cache.flushAll()
    logInfo('Secret cache cleared')
  }

  /**
   * Get cache statistics
   * @returns {Object}
   */
  getCacheStats() {
    return this.cache.getStats()
  }

  /**
   * Add or update secret mapping
   * @param {string} envVarName - Environment variable name
   * @param {string} secretName - Key Vault secret name
   */
  addSecretMapping(envVarName, secretName) {
    this.secretMapping[envVarName] = secretName
    logInfo(`Added secret mapping: ${envVarName} -> ${secretName}`)
  }
}

// Export singleton instance
module.exports = new SecretManager()
