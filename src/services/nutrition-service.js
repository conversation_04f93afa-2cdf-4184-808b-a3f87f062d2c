const logging = require('../common/logging')
const nutritionRepository = require('../repositories/nutrition-repository')
const patientLifestyleRepository = require('../repositories/patient-lifestyle-repository')
const { DashboardFilter, NutritionMetric, MealCategory } = require('../common/constant')

class NutritionService {

    /**
     * Calculate date range based on dashboard filter
     * @param {string} filter - The dashboard filter (LAST_7_DAYS, LAST_15_DAYS, LAST_MONTH)
     * @returns {object} Object containing fromDate, toDate, and dayCount
     */
    _calculateDateRange(filter) {
        const today = new Date()
        let fromDate, toDate, dayCount

        switch (filter) {
            case DashboardFilter.LAST_7_DAYS:
                const sevenDaysAgo = new Date(today)
                sevenDaysAgo.setDate(today.getDate() - 6) // 7 days including today
                fromDate = sevenDaysAgo.toISOString().split('T')[0]
                toDate = today.toISOString().split('T')[0]
                dayCount = 7
                break
            case DashboardFilter.LAST_15_DAYS:
                const fifteenDaysAgo = new Date(today)
                fifteenDaysAgo.setDate(today.getDate() - 14) // 15 days including today
                fromDate = fifteenDaysAgo.toISOString().split('T')[0]
                toDate = today.toISOString().split('T')[0]
                dayCount = 15
                break
            case DashboardFilter.LAST_MONTH:
                const oneMonthAgo = new Date(today)
                oneMonthAgo.setMonth(today.getMonth() - 1)
                fromDate = oneMonthAgo.toISOString().split('T')[0]
                toDate = today.toISOString().split('T')[0]
                dayCount = Math.ceil((today - oneMonthAgo) / (1000 * 60 * 60 * 24)) + 1
                break
            default:
                throw new Error(`Invalid filter: ${filter}`)
        }

        logging.logInfo(`Date range for ${filter}: ${fromDate} to ${toDate}`)
        
        return { fromDate, toDate, dayCount }
    }

    _countMealsAndSnacks(lifestyleData) {
        const dailySeen = {}

        const normalizeLabel = (s) => {
            if (!s) return ''
            const str = s.toString().trim().toLowerCase()
            const cleaned = str.replace(/[^a-z\s]/g, ' ').replace(/\s+/g, ' ').trim()
            if (cleaned.endsWith('s')) return cleaned.slice(0, -1)
            return cleaned
        }

        lifestyleData.forEach(record => {
            const recordDate = record.created_on ? record.created_on.split('T')[0] : null
            if (!recordDate) return

            if (!dailySeen[recordDate]) {
                dailySeen[recordDate] = {
                    meals: new Set(),
                    snacks: new Set()
                }
            }

            if (record.questions && record.questions.length > 0) {
                const dietaryRecall = record.questions.find(q => q.id === 'dietary_recall')
                if (dietaryRecall && dietaryRecall.fields && dietaryRecall.fields.length > 0) {
                    const dietaryTable = dietaryRecall.fields.find(f => f.id === 'dietary_table')
                    if (dietaryTable && dietaryTable.value) {
                        dietaryTable.value.forEach(mealGroup => {
                            if (mealGroup.label && mealGroup.rows && mealGroup.rows.length > 0) {
                                const hasFood = mealGroup.rows.some(row => row.food_item && row.quantity)
                                const normalizedGroup = normalizeLabel(mealGroup.label)

                                const isMeal = MealCategory.MEALS.some(m => normalizeLabel(m) === normalizedGroup)
                                const isSnack = MealCategory.SNACKS.some(m => normalizeLabel(m) === normalizedGroup)

                                if (hasFood) {
                                    if (isMeal) {
                                        dailySeen[recordDate].meals.add(normalizedGroup)
                                    } else if (isSnack) {
                                        dailySeen[recordDate].snacks.add(normalizedGroup)
                                    } else {
                                        // Fallback: simple keyword checks
                                        if (['breakfast', 'lunch', 'dinner'].includes(normalizedGroup)) {
                                            dailySeen[recordDate].meals.add(normalizedGroup)
                                        } else if (normalizedGroup.includes('snack') || normalizedGroup.includes('mid')) {
                                            dailySeen[recordDate].snacks.add(normalizedGroup)
                                        }
                                    }
                                }
                            }
                        })
                    }
                }
            }
        })

        const dailyCounts = {}
        Object.keys(dailySeen).forEach(date => {
            dailyCounts[date] = {
                meals: dailySeen[date].meals.size,
                snacks: dailySeen[date].snacks.size
            }
        })

        return dailyCounts
    }

    async getFoodNames(searchTerm = '') {
        try {
            let result
            if (searchTerm && searchTerm.trim().length > 0) {
                result = await nutritionRepository.getFoodNamesBySearch(searchTerm.trim())
            } else {
                result = await nutritionRepository.getAllFoodNames()
            }
            return result
        } catch (error) {
            logging.logError("unable to get food names", error)
            return null
        }
    }

    async getServingUnitByFoodName(foodName) {
        try {
            var result = await nutritionRepository.getServingUnitByFoodName(foodName)
            return result
        } catch (error) {
            logging.logError("unable to get serving unit for food", error)
            return null
        }
    }

    async getNutritionSummary(patientId, filter, metrics, sort = 'asc') {
        try {
            const allMetrics = [
                'calories', 'carbs', 'protein', 'fat', 'sugar', 'fiber', 'salt',
                'calcium', 'magnesium', 'phosphorus', 'iron', 'sodium', 'potassium',
                'sfa', 'mufa', 'pufa', 'cholesterol', 'oil'
            ];
            const targetMetrics = metrics && metrics.length > 0 ? metrics : allMetrics;

            const { fromDate, toDate } = this._calculateDateRange(filter);

            logging.logInfo(`Getting nutrition summary for patient: ${patientId}, filter: ${filter}, from: ${fromDate}, to: ${toDate}`)

            const lifestyleData = await patientLifestyleRepository.getPatientLifestyleByDateRange(
                patientId, 
                fromDate, 
                toDate
            )

            logging.logInfo(`Found ${lifestyleData?.length || 0} lifestyle records`)

            if (!lifestyleData || lifestyleData.length === 0) {
                logging.logInfo(`No lifestyle data found for patient ${patientId}`)
                return []
            }

            const dailyMealCounts = this._countMealsAndSnacks(lifestyleData)

            const generalIntakePerDate = {}
            lifestyleData.forEach(rec => {
                const d = rec.created_on ? rec.created_on.split('T')[0] : fromDate
                if (!d) return

                let giValue = null
                if (rec.questions && Array.isArray(rec.questions)) {
                    const dietaryRecall = rec.questions.find(q => q.id === 'dietary_recall')
                    if (dietaryRecall && Array.isArray(dietaryRecall.fields)) {
                        const generalField = dietaryRecall.fields.find(f => f.id === 'general_intake')
                        if (generalField && generalField.value) {
                            giValue = generalField.value
                        }
                    }
                }

                if (giValue) {
                    if (!generalIntakePerDate[d]) {
                        generalIntakePerDate[d] = { sugar_g: 0, salt_g: 0, oil_ml: 0 }
                    }

                    generalIntakePerDate[d].sugar_g += parseFloat(giValue.sugar_g) || 0
                    generalIntakePerDate[d].salt_g += parseFloat(giValue.salt_g) || 0
                    generalIntakePerDate[d].oil_ml += parseFloat(giValue.oil_ml) || 0
                }
            })

            const allFoodItems = []
            
            const seenFoodPerDate = {}

            lifestyleData.forEach(record => {
                if (record.questions && record.questions.length > 0) {
                    const dietaryRecall = record.questions.find(q => q.id === 'dietary_recall')
                    if (dietaryRecall && dietaryRecall.fields && dietaryRecall.fields.length > 0) {
                        const dietaryTable = dietaryRecall.fields.find(f => f.id === 'dietary_table')
                                        if (dietaryTable && dietaryTable.value) {
                                        const recordDate = record.created_on ? record.created_on.split('T')[0] : fromDate

                                        if (!seenFoodPerDate[recordDate]) seenFoodPerDate[recordDate] = new Set()

                                        dietaryTable.value.forEach(mealGroup => {
                                            if (mealGroup.rows && mealGroup.rows.length > 0) {
                                                mealGroup.rows.forEach(foodRow => {
                                                    if (foodRow.food_item && foodRow.quantity) {
                                                        const foodName = foodRow.food_item.toString().trim()
                                                        const mealLabel = (mealGroup.label || '').toString().trim().toLowerCase()
                                                        const key = `${recordDate}|${mealLabel}|${foodName.toLowerCase()}`
                                                        if (seenFoodPerDate[recordDate].has(key)) return
                                                        seenFoodPerDate[recordDate].add(key)

                                                        allFoodItems.push({
                                                            food_item: foodName,
                                                            serving_type: foodRow.serving_type?.value || foodRow.serving_type,
                                                            quantity: parseFloat(foodRow.quantity) || 0,
                                                            date: recordDate,
                                                            meal: mealGroup.label
                                                        })
                                                    }
                                                })
                                            }
                                        })
                                    }
                    }
                }
            })

            logging.logInfo(`Extracted ${allFoodItems.length} food items from lifestyle data`)

            if (allFoodItems.length === 0) {
                logging.logInfo(`No food items found in lifestyle data`)
                return []
            }

            const uniqueFoodItems = [...new Set(allFoodItems.map(item => item.food_item.trim()))]

            logging.logInfo(`Found ${uniqueFoodItems.length} unique food items`)

            const nutritionData = await nutritionRepository.getNutritionDataByFoodNames(uniqueFoodItems)

            logging.logInfo(`Found ${nutritionData?.length || 0} nutrition records`)

            const nutritionLookup = {}
            nutritionData.forEach(item => {
                if (item.food_name) {
                    nutritionLookup[item.food_name.trim().toLowerCase()] = item
                }
            })


            const dailyTotals = {}

            allFoodItems.forEach(foodItem => {
                const entryDate = foodItem.date

                const nutritionInfo = nutritionLookup[foodItem.food_item.trim().toLowerCase()]
                if (!nutritionInfo) {
                    logging.logInfo(`No nutrition data found for food item: ${foodItem.food_item}`)
                    return 
                }

                const quantity = foodItem.quantity
                if (quantity <= 0) {
                    return
                }

                if (!dailyTotals[entryDate]) {
                    dailyTotals[entryDate] = {}
                    targetMetrics.forEach(metric => {
                        dailyTotals[entryDate][metric] = 0
                    })
                }

                targetMetrics.forEach(metric => {
                    let value = 0
                    switch (metric.toLowerCase()) {
                        case NutritionMetric.CALORIES:
                            value = (parseFloat(nutritionInfo.unit_serving_energy_kcal) || 0) * quantity
                            break
                        case NutritionMetric.CARBS:
                            value = (parseFloat(nutritionInfo.unit_serving_carb_g) || 0) * quantity
                            break
                        case NutritionMetric.PROTEIN:
                            value = (parseFloat(nutritionInfo.unit_serving_protein_g) || 0) * quantity
                            break
                        case NutritionMetric.FAT:
                            value = (parseFloat(nutritionInfo.unit_serving_fat_g) || 0) * quantity
                            break
                        case NutritionMetric.SUGAR:
                            value = (parseFloat(nutritionInfo.unit_serving_freesugar_g) || 0) * quantity
                            break
                        case NutritionMetric.FIBER:
                            value = (parseFloat(nutritionInfo.unit_serving_fibre_g) || 0) * quantity
                            break
                        case NutritionMetric.CALCIUM:
                            value = (parseFloat(nutritionInfo.unit_serving_calcium_mg) || 0) * quantity
                            break
                        case NutritionMetric.MAGNESIUM:
                            value = (parseFloat(nutritionInfo.unit_serving_magnesium_mg) || 0) * quantity
                            break
                        case NutritionMetric.PHOSPHORUS:
                            value = (parseFloat(nutritionInfo.unit_serving_phosphorus_mg) || 0) * quantity
                            break
                        case NutritionMetric.IRON:
                            value = (parseFloat(nutritionInfo.unit_serving_iron_mg) || 0) * quantity
                            break
                        case NutritionMetric.SODIUM:
                            value = (parseFloat(nutritionInfo.unit_serving_sodium_mg) || 0) * quantity
                            break
                        case NutritionMetric.POTASSIUM:
                            value = (parseFloat(nutritionInfo.unit_serving_potassium_mg) || 0) * quantity
                            break
                        case NutritionMetric.SALT:
                            // Salt (g) = (Sodium (mg) × 2.54) / 1000
                            const sodiumMg = (parseFloat(nutritionInfo.unit_serving_sodium_mg) || 0) * quantity
                            value = (sodiumMg * 2.54) / 1000
                            break
                        case NutritionMetric.SFA:
                            value = (parseFloat(nutritionInfo.unit_serving_sfa_mg) || 0) * quantity
                            break
                        case NutritionMetric.MUFA:
                            value = (parseFloat(nutritionInfo.unit_serving_mufa_mg) || 0) * quantity
                            break
                        case NutritionMetric.PUFA:
                            value = (parseFloat(nutritionInfo.unit_serving_pufa_mg) || 0) * quantity
                            break
                        case NutritionMetric.CHOLESTEROL:
                            value = (parseFloat(nutritionInfo.unit_serving_cholesterol_mg) || 0) * quantity
                            break
                        default:
                            value = 0
                    }
                    dailyTotals[entryDate][metric] += value
                })
            })

            Object.keys(generalIntakePerDate).forEach(date => {
                const gi = generalIntakePerDate[date]
                if (!dailyTotals[date]) {
                    dailyTotals[date] = {}
                    targetMetrics.forEach(metric => {
                        dailyTotals[date][metric] = 0
                    })
                }

                if (targetMetrics.includes('sugar')) {
                    dailyTotals[date]['sugar'] = (dailyTotals[date]['sugar'] || 0) + (gi.sugar_g || 0)
                }
              
                if (targetMetrics.includes('salt')) {
                    dailyTotals[date]['salt'] = (dailyTotals[date]['salt'] || 0) + (gi.salt_g || 0)
                }
         
                if (targetMetrics.includes('oil')) {
                    dailyTotals[date]['oil'] = (dailyTotals[date]['oil'] || 0) + (gi.oil_ml || 0)
                }
            })

            const result = Object.entries(dailyTotals)
                .map(([date, totals]) => {
                    const roundedTotals = {}
                    Object.keys(totals).forEach(metric => {
                        roundedTotals[metric] = Math.round(totals[metric] * 100) / 100
                    })
                    
                    const hasMacros = ['fat', 'carbs', 'protein'].some(macro => targetMetrics.includes(macro))
                    if (hasMacros) {
                        const fat = roundedTotals.fat || 0
                        const carbs = roundedTotals.carbs || 0
                        const protein = roundedTotals.protein || 0
                        const total = fat + carbs + protein
                        
                        if (total > 0) {
                            if (targetMetrics.includes('fat')) {
                                roundedTotals.fat_percentage = Math.round((fat / total) * 100 * 100) / 100
                            }
                            if (targetMetrics.includes('carbs')) {
                                roundedTotals.carbs_percentage = Math.round((carbs / total) * 100 * 100) / 100
                            }
                            if (targetMetrics.includes('protein')) {
                                roundedTotals.protein_percentage = Math.round((protein / total) * 100 * 100) / 100
                            }
                        } else {
                            if (targetMetrics.includes('fat')) {
                                roundedTotals.fat_percentage = 0
                            }
                            if (targetMetrics.includes('carbs')) {
                                roundedTotals.carbs_percentage = 0
                            }
                            if (targetMetrics.includes('protein')) {
                                roundedTotals.protein_percentage = 0
                            }
                        }
                    }
                    
                    const dayMealCounts = dailyMealCounts[date] || { meals: 0, snacks: 0 }
                    
                    return {
                        date,
                        ...roundedTotals,
                        meals: dayMealCounts.meals,
                        snacks: dayMealCounts.snacks
                    }
                })

            if (sort === 'desc') {
                result.sort((a, b) => new Date(b.date) - new Date(a.date)) 
            } else {
                result.sort((a, b) => new Date(a.date) - new Date(b.date)) 
            }

            return result

        } catch (error) {
            logging.logError(`Unable to get nutrition summary for patient ${patientId}`, error)
            return null
        }
    }

    async getNutritionAverage(patientId, filter, metrics) {
        try {
            logging.logInfo(`Calculating nutrition average for patient: ${patientId}, filter: ${filter}`)

            const { fromDate, toDate } = this._calculateDateRange(filter)

            const dailySummary = await this.getNutritionSummary(patientId, filter, metrics, 'asc')

            if (!dailySummary || dailySummary.length === 0) {
                logging.logInfo(`No nutrition data found for the specified filter`)
                return {
                    filter: filter,
                    fromDate: fromDate,
                    toDate: toDate,
                    daysWithData: 0,
                    averages: {}
                }
            }

            const totals = {}
            metrics.forEach(metric => {
                totals[metric] = 0
            })

            dailySummary.forEach(day => {
                metrics.forEach(metric => {
                    if (day[metric] !== undefined) {
                        totals[metric] += day[metric]
                    }
                })
            })

            const averages = {}
            metrics.forEach(metric => {
                averages[metric] = Math.round((totals[metric] / dailySummary.length) * 100) / 100
            })

            const result = {
                filter: filter,
                fromDate: fromDate,
                toDate: toDate,
                daysWithData: dailySummary.length,
                averages: averages
            }

            logging.logInfo(`Nutrition averages calculated: ${JSON.stringify(result)}`)
            return result

        } catch (error) {
            logging.logError(`Unable to get nutrition average for patient ${patientId}`, error)
            return null
        }
    }

    async getNutritionChart(patientId, filter, metrics) {
        try {
            const targetMetrics = Array.isArray(metrics) ? metrics : [metrics];
            
            logging.logInfo(`Generating nutrition chart for patient: ${patientId}, filter: ${filter}, metrics: ${targetMetrics.join(', ')}`)

            const { fromDate, toDate, dayCount } = this._calculateDateRange(filter)

            const dailySummary = await this.getNutritionSummary(patientId, filter, targetMetrics, 'asc')

            const dataMap = {}
            dailySummary.forEach(day => {
                dataMap[day.date] = {}
                targetMetrics.forEach(metric => {
                    dataMap[day.date][metric] = day[metric] || 0
                })
            })

            const chartData = []
            const startDate = new Date(fromDate)
            const endDate = new Date(toDate)

            for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                const dateStr = d.toISOString().split('T')[0]
                const dayData = { date: dateStr }
                
                targetMetrics.forEach(metric => {
                    const value = dataMap[dateStr] ? dataMap[dateStr][metric] || 0 : 0
                    dayData[metric] = Math.round(value * 100) / 100 // Round to 2 decimal places
                })
                
                chartData.push(dayData)
            }

            chartData.sort((a, b) => new Date(a.date) - new Date(b.date))

            const result = {
                filter: filter,
                metrics: targetMetrics,
                fromDate: fromDate,
                toDate: toDate,
                chartData: chartData
            }

            logging.logInfo(`Chart data generated with ${chartData.length} data points for ${targetMetrics.length} metrics`)
            return result

        } catch (error) {
            logging.logError(`Unable to generate nutrition chart for patient ${patientId}`, error)
            return null
        }
    }

    async getNutritionPercentageChart(patientId, filter) {
        try {
            logging.logInfo(`Generating nutrition percentage chart for patient: ${patientId}, filter: ${filter}`)

            const macronutrients = [NutritionMetric.FAT, NutritionMetric.CARBS, NutritionMetric.PROTEIN]
            const { fromDate, toDate } = this._calculateDateRange(filter)

            const dailySummary = await this.getNutritionSummary(patientId, filter, macronutrients, 'asc')

            if (!dailySummary || dailySummary.length === 0) {
                logging.logInfo(`No nutrition data found for percentage chart`)
                return {
                    filter: filter,
                    fromDate: fromDate,
                    toDate: toDate,
                    chartData: []
                }
            }

            const chartData = dailySummary.map(day => {
                const fat = day.fat || 0
                const carbs = day.carbs || 0
                const protein = day.protein || 0
                
                const total = fat + carbs + protein
            
                const fatPercentage = total > 0 ? Math.round((fat / total) * 100 * 100) / 100 : 0
                const carbsPercentage = total > 0 ? Math.round((carbs / total) * 100 * 100) / 100 : 0
                const proteinPercentage = total > 0 ? Math.round((protein / total) * 100 * 100) / 100 : 0
                
                return {
                    date: day.date,
                    fat_percentage: fatPercentage,
                    carbs_percentage: carbsPercentage,
                    protein_percentage: proteinPercentage
                }
            })

            const dataMap = {}
            chartData.forEach(day => {
                dataMap[day.date] = day
            })

            const completeChartData = []
            const startDate = new Date(fromDate)
            const endDate = new Date(toDate)

            for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                const dateStr = d.toISOString().split('T')[0]
                
                if (dataMap[dateStr]) {
                    completeChartData.push(dataMap[dateStr])
                } else {
                    completeChartData.push({
                        date: dateStr,
                        fat_percentage: 0,
                        carbs_percentage: 0,
                        protein_percentage: 0
                    })
                }
            }

            completeChartData.sort((a, b) => new Date(a.date) - new Date(b.date))

            const result = {
                filter: filter,
                fromDate: fromDate,
                toDate: toDate,
                chartData: completeChartData
            }

            logging.logInfo(`Percentage chart data generated with ${completeChartData.length} data points`)
            return result

        } catch (error) {
            logging.logError(`Unable to generate nutrition percentage chart for patient ${patientId}`, error)
            return null
        }
    }

}

module.exports = new NutritionService()

