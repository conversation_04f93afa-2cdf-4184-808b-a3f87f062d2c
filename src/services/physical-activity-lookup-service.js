const logging = require('../common/logging')

class PhysicalActivityLookupService {
  constructor() {
    // Physical activity data from Excel file with corrected activity names
    this.physicalActivities = [
      // Aerobics
      {
        'Activity Types': 'Aerobics',
        Activity: 'Walking',
        'MET (Mild)': 2.0,
        'MET (Moderate)': 3.8,
        'MET (Severe)': 6.5,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Jogging',
        'MET (Mild)': 3.3,
        'MET (Moderate)': 4.8,
        'MET (Severe)': 7.5,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Running',
        'MET (Mild)': 8.5,
        'MET (Moderate)': 10.5,
        'MET (Severe)': 14.8,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Cycling',
        'MET (Mild)': 4.3,
        'MET (Moderate)': 7.0,
        'MET (Severe)': 9.0,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Swimming',
        'MET (Mild)': 5.8,
        'MET (Moderate)': 8.0,
        'MET (Severe)': 9.8,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Dancing',
        'MET (Mild)': 4.8,
        'MET (Moderate)': 6.0,
        'MET (Severe)': 8.0,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Zumba',
        'MET (Mild)': null,
        'MET (Moderate)': 5.5,
        'MET (Severe)': 6.5,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Skipping',
        'MET (Mild)': null,
        'MET (Moderate)': 8.3,
        'MET (Severe)': 11.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Skipping rope',
        'MET (Mild)': null,
        'MET (Moderate)': 8.3,
        'MET (Severe)': 11.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Kickboxing',
        'MET (Mild)': null,
        'MET (Moderate)': null,
        'MET (Severe)': 7.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Kick boxing',
        'MET (Mild)': null,
        'MET (Moderate)': null,
        'MET (Severe)': 7.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Rowing',
        'MET (Mild)': null,
        'MET (Moderate)': 5.0,
        'MET (Severe)': 7.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Hiking',
        'MET (Mild)': 3.8,
        'MET (Moderate)': 5.3,
        'MET (Severe)': 7.8,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Stair Climbing',
        'MET (Mild)': 4.5,
        'MET (Moderate)': 6.8,
        'MET (Severe)': 7.5,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Sports - Cricket',
        'MET (Mild)': null,
        'MET (Moderate)': 4.8,
        'MET (Severe)': null,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Sports - Basketball',
        'MET (Mild)': 5.0,
        'MET (Moderate)': 6.0,
        'MET (Severe)': 9.3,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Sports - Soccer',
        'MET (Mild)': 5.0,
        'MET (Moderate)': 7.0,
        'MET (Severe)': 9.5,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Sports - Badminton',
        'MET (Mild)': null,
        'MET (Moderate)': 5.5,
        'MET (Severe)': 7.0,
      },
      {
        'Activity Types': 'Aerobics',
        Activity: 'Sports - Tennis',
        'MET (Mild)': null,
        'MET (Moderate)': 5.0,
        'MET (Severe)': 6.8,
      },
      // Strength
      {
        'Activity Types': 'Strength',
        Activity: 'Body Weight Exercises',
        'MET (Mild)': null,
        'MET (Moderate)': 3,
        'MET (Severe)': 6.5,
      },
      {
        'Activity Types': 'Strength',
        Activity: 'Free Weight Exercises',
        'MET (Mild)': null,
        'MET (Moderate)': 3.5,
        'MET (Severe)': 6.0,
      },
      {
        'Activity Types': 'Strength',
        Activity: 'Machine Based Exercise',
        'MET (Mild)': null,
        'MET (Moderate)': null,
        'MET (Severe)': 6.0,
      },
      {
        'Activity Types': 'Strength',
        Activity: 'Resistance Band Exercise',
        'MET (Mild)': 3.0,
        'MET (Moderate)': 3.5,
        'MET (Severe)': 5.8,
      },
      {
        'Activity Types': 'Strength',
        Activity: 'Functional Strenght Exercise',
        'MET (Mild)': 2.0,
        'MET (Moderate)': null,
        'MET (Severe)': 4.5,
      },

      // Flexibility
      {
        'Activity Types': 'Flexibility',
        Activity: 'Static Stretching',
        'MET (Mild)': 2.3,
        'MET (Moderate)': null,
        'MET (Severe)': null,
      },
      {
        'Activity Types': 'Flexibility',
        Activity: 'Dynamic Stretching',
        'MET (Mild)': 2.8,
        'MET (Moderate)': 3.8,
        'MET (Severe)': null,
      },
      {
        'Activity Types': 'Flexibility',
        Activity: 'Yoga poses',
        'MET (Mild)': 2.3,
        'MET (Moderate)': 4.0,
        'MET (Severe)': 8.0,
      },
      {
        'Activity Types': 'Flexibility',
        Activity: 'Pilates based flexibility',
        'MET (Mild)': 1.8,
        'MET (Moderate)': 2.8,
        'MET (Severe)': null,
      },
      // Balance
      {
        'Activity Types': 'Balance',
        Activity: 'Basic Balance Exercise',
        'MET (Mild)': 2.3,
        'MET (Moderate)': null,
        'MET (Severe)': null,
      },
      {
        'Activity Types': 'Balance',
        Activity: 'Balance Enhancing Yoga Poses',
        'MET (Mild)': 2.0,
        'MET (Moderate)': 2.5,
        'MET (Severe)': 4.0,
      },
      {
        'Activity Types': 'Balance',
        Activity: 'Advanced/Functional Balance Exercise',
        'MET (Mild)': 2.0,
        'MET (Moderate)': 2.5,
        'MET (Severe)': 4.0,
      },
      {
        'Activity Types': 'Balance',
        Activity: 'Sports Specific or dynamic Balance drills',
        'MET (Mild)': 3.0,
        'MET (Moderate)': 3.5,
        'MET (Severe)': 4.0,
      },
    ]
  }

  async getActivities() {
    return this.physicalActivities
  }

  async getMetValue(activityName, intensity, activityType) {
    try {
      const activities = await this.getActivities()

      const activity = activities.find(
        (a) =>
          a['Activity Types'] &&
          a['Activity Types'].toLowerCase() === activityType.toLowerCase() &&
          a['Activity'] &&
          a['Activity'].toLowerCase() === activityName.toLowerCase(),
      )

      if (!activity) {
        logging.logInfo(`Activity not found: ${activityName} (${activityType})`)
        return null
      }

      let metValue = null
      const requestedIntensity = intensity.toLowerCase()

      switch (requestedIntensity) {
        case 'mild':
          metValue = activity['MET (Mild)']
          break
        case 'moderate':
          metValue = activity['MET (Moderate)']
          // Fallback to mild if moderate not available
          if (!metValue) {
            metValue = activity['MET (Mild)']
            if (metValue) {
              logging.logInfo(
                `ARCAAI-MS-EMR ::  Moderate intensity not available for ${activityName}, using Mild intensity`,
              )
            }
          }
          break
        case 'intense':
        case 'severe':
          metValue = activity['MET (Severe)']
          if (!metValue) {
            metValue = activity['MET (Moderate)']
            if (metValue) {
              logging.logInfo(
                `ARCAAI-MS-EMR ::  Severe intensity not available for ${activityName}, using Moderate intensity`,
              )
            } else {
              metValue = activity['MET (Mild)']
              if (metValue) {
                logging.logInfo(
                  `ARCAAI-MS-EMR ::  Severe/Moderate intensity not available for ${activityName}, using Mild intensity`,
                )
              }
            }
          }
          break
        default:
          logging.logInfo(`Invalid intensity: ${intensity}`)
          return null
      }

      const finalMetValue = parseFloat(metValue) || null
      if (finalMetValue) {
        logging.logInfo(
          `ARCAAI-MS-EMR ::  Found MET value ${finalMetValue} for ${activityName} (${activityType}) at ${requestedIntensity} intensity`,
        )
      }

      return finalMetValue
    } catch (error) {
      logging.logError(`Failed to get MET value for ${activityName}:`, error)
      return null
    }
  }

  // Get all activity types
  async getActivityTypes() {
    try {
      const activities = await this.getActivities()
      const types = [
        ...new Set(activities.map((a) => a['Activity Types']).filter(Boolean)),
      ]
      return types.sort()
    } catch (error) {
      logging.logError('Failed to get activity types:', error)
      return []
    }
  }

  // Get activities by type
  async getActivitiesByType(activityType) {
    try {
      const activities = await this.getActivities()
      return activities.filter(
        (a) =>
          a['Activity Types'] &&
          a['Activity Types'].toLowerCase() === activityType.toLowerCase(),
      )
    } catch (error) {
      logging.logError(
        `Failed to get activities by type ${activityType}:`,
        error,
      )
      return []
    }
  }

  // Search activities by name
  async searchActivities(searchTerm) {
    try {
      const activities = await this.getActivities()
      const term = searchTerm.toLowerCase()

      return activities.filter(
        (a) =>
          (a['Activity'] && a['Activity'].toLowerCase().includes(term)) ||
          (a['Activity Types'] &&
            a['Activity Types'].toLowerCase().includes(term)),
      )
    } catch (error) {
      logging.logError(
        `Failed to search activities with term ${searchTerm}:`,
        error,
      )
      return []
    }
  }

  // Get activities with all intensity MET values
  async getActivitiesWithIntensities() {
    try {
      const activities = await this.getActivities()

      return activities.map((activity) => ({
        activityType: activity['Activity Types'],
        activity: activity['Activity'],
        metValues: {
          mild: parseFloat(activity['MET (Mild)']) || 0,
          moderate: parseFloat(activity['MET (Moderate)']) || 0,
          intense: parseFloat(activity['MET (Severe)']) || 0,
        },
      }))
    } catch (error) {
      logging.logError('Failed to get activities with intensities:', error)
      return []
    }
  }

  // Clear cache (useful for testing or manual refresh)
  clearCache() {
    this.activityCache = null
    this.lastCacheUpdate = null
    logging.logInfo('Physical activity cache cleared')
  }
}

module.exports = new PhysicalActivityLookupService()
