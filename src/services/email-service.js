const nodemailer = require('nodemailer')
const jwt = require('jsonwebtoken')
const secretManager = require('./secret-manager')
const { logError, logInfo } = require('../common/logging')

class EmailService {
  constructor() {
    this.transporter = null
    this.isInitialized = false
    this.initializationPromise = null
  }

  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._doInitialize()
    return this.initializationPromise
  }

  async _doInitialize() {
    try {
      logInfo('Initializing Email Service with Key Vault secrets...')

      // Get secrets from Key Vault or fallback to environment variables
      const emailUser = await secretManager.getSecret('EMAIL_USER')
      const emailPassword = await secretManager.getSecret('EMAIL_PASSWORD')

      if (!emailUser || !emailPassword) {
        throw new Error(
          'Missing email credentials in Key Vault or environment variables',
        )
      }

      this.transporter = nodemailer.createTransport({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: emailUser,
          pass: emailPassword,
        },
        logger: true,
        debug: true,
      })

      // Verify the configuration
      await new Promise((resolve, reject) => {
        this.transporter.verify((error, success) => {
          if (error) {
            logError('SMTP configuration error:', error)
            reject(error)
          } else {
            logInfo('SMTP configuration is correct:', success)
            resolve(success)
          }
        })
      })

      this.isInitialized = true
      logInfo('Email Service initialized successfully')
    } catch (error) {
      logError('Failed to initialize Email Service:', error)
      this.isInitialized = false
      throw error
    }
  }

  async sendEmail(to, subject, text, html) {
    try {
      // Ensure email service is initialized
      if (!this.isInitialized) {
        await this.initialize()
      }

      const emailUser = await secretManager.getSecret('EMAIL_USER')

      await this.transporter.sendMail({
        from: emailUser,
        to,
        subject,
        text,
        html,
      })
    } catch (error) {
      logError('Error sending email:', error)
      throw new Error('Failed to send email')
    }
  }

  async sendWelcomeEmail(email, resetToken, temporaryPassword = null) {
    const baseAdminUrl = await secretManager.getSecret('BASE_ADMIN_URL')
    const activationLink = `${baseAdminUrl}/set-password?token=${resetToken}`

    const passwordToShow = temporaryPassword

    const subject = 'Welcome to EHR Platform'
    const text = `Dear User,\n\nWelcome to the EHR Platform! Please activate your account using the following link: ${activationLink}\n\nFor testing purposes, your temporary password is: ${passwordToShow}\n\nIf you have any questions, feel free to contact support.\n\nRegards,\nTeam EHR`
    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <h2 style="color: #4CAF50;">Welcome to EHR Platform</h2>
        <p>Dear User,</p>
        <p>Welcome to the EHR Platform! We're excited to have you on board.</p>
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007bff;">
          <p><strong>For Testing Purposes:</strong></p>
          <p>Your temporary password is: <code style="background-color: #e9ecef; padding: 2px 6px; border-radius: 3px; font-family: monospace;">${passwordToShow}</code></p>
        </div>
        <p>Please activate your account using the button below:</p>
        <div style="text-align: left; margin: 20px 0;">
          <a href="${activationLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;">Activate Account</a>
        </div>
        <p>If you have any questions, feel free to contact support.</p>
        <footer style="margin-top: 20px; font-size: 0.9em; color: #888;">
          <p>Regards,</p>
          <p>Team EHR</p>
        </footer>
      </div>
    `
    await this.sendEmail(email, subject, text, html)
  }

  async sendWelcomeEmailWithResetLink(
    email,
    resetToken,
    organizationName,
    password,
  ) {
    const baseAdminUrl = await secretManager.getSecret('BASE-ADMIN-URL')
    const resetLink = `${baseAdminUrl}/set-password?token=${resetToken}`
    const subject = `Invitation to Join ${organizationName} on EHR Platform`
    const text = `Dear ${organizationName},\n\nYou have been invited to join the EHR Platform.\n\nYour default password is: ${password}\n\nPlease reset your password using the following link: ${resetLink}\n\nIf you have any trouble accepting the invitation or believe you received this email by mistake, please contact support.\n\nRegards,\nTeam EHR`
    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333;">
        <h2 style="color: #4CAF50;">Invitation to Join ${organizationName}</h2>
        <p>Dear ${organizationName},</p>
        <p>You have been invited to join the EHR Platform.</p>
        <p>Your default password is: <strong>${password}</strong></p>
        <p>Please reset your password using the button below:</p>
        <div style="text-align: left; margin: 20px 0;">
          <a href="${resetLink}" style="background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;">Reset Password</a>
        </div>
        <p>If you have any trouble accepting the invitation or believe you received this email by mistake, please contact support.</p>
        <footer style="margin-top: 20px; font-size: 0.9em; color: #888;">
          <p>Regards,</p>
          <p>Team EHR</p>
        </footer>
      </div>
    `
    await this.sendEmail(email, subject, text, html)
  }

  async generateActivationToken(email) {
    const jwtSecret = await secretManager.getSecret('JWT_SECRET')
    if (!jwtSecret) {
      throw new Error(
        'JWT_SECRET not found in Key Vault or environment variables',
      )
    }
    return jwt.sign({ email }, jwtSecret, { expiresIn: '1d' })
  }

  async generatePasswordResetToken(email) {
    try {
      const jwtSecret = await secretManager.getSecret('JWT_SECRET')
      if (!jwtSecret) {
        throw new Error(
          'JWT_SECRET not found in Key Vault or environment variables',
        )
      }
      return jwt.sign({ email }, jwtSecret, { expiresIn: '1d' })
    } catch (error) {
      logError('Error generating password reset token:', error)
      throw new Error('Failed to generate password reset token')
    }
  }

  async sendWelcomeEmailWithB2CCredentials(
    email,
    name,
    temporaryPassword,
    b2cResetUrl,
    isAdmin = false,
  ) {
    const subject = isAdmin
      ? 'Welcome to EHR Platform - Organization Admin Account Created'
      : 'Welcome to EHR Platform - Account Created'

    const roleText = isAdmin ? 'organization administrator' : 'team member'

    const platformText = isAdmin ? 'Admin Portal' : 'EHR Platform'

    const text = `Dear ${name},\n\nWelcome to the EHR Platform! Your ${roleText} account has been successfully created.\n\nYour login credentials:\nEmail: ${email}\nTemporary Password: ${temporaryPassword}\n\nIMPORTANT: For security reasons, you must change your password on first login.\n\nTo access the ${platformText} and set your permanent password, please visit: ${b2cResetUrl}\n\nWhat happens next:\n1. Visit the login portal using the link above\n2. Sign in with your email and temporary password\n3. You'll be prompted to create a new secure password\n4. Once completed, you can access the ${platformText}\n\nIf you have any questions, feel free to contact support.\n\nRegards,\nTeam EHR`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333; max-width: 600px; margin: 0 auto;">
       <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">

          <h1 style="margin: 0; font-size: 24px;">${
            isAdmin
              ? '🏢 Welcome to EHR Admin Portal!'
              : '👋 Welcome to EHR Platform!'
          }</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${name}</strong>,</p>

          <p>Great news! Your <strong>${roleText}</strong> account has been successfully created on the EHR Platform.</p>
          ${
            isAdmin
              ? `
            <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #2e7d32;"><strong>🏢 Administrator Role</strong></p>
              <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
                You’ve been assigned as an administrator. You can manage your organization’s settings, users, and data.
              </p>
            </div>
            `
              : `
            <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #2e7d32;"><strong>👥 Team Member Role</strong></p>
              <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
                You’ve been added as a team member. You can now use the EHR platform to support patient care and collaborate with your organization.
              </p>
            </div>
            `
          }
          
          <div style="background-color: #fff; border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #4CAF50; margin-top: 0;">🔑 Your Login Credentials</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
                <td style="padding: 8px 0; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px;">${email}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Temporary Password:</td>
                <td style="padding: 8px 0; font-family: monospace; background-color: #f5f5f5; padding: 4px 8px; border-radius: 4px; font-weight: bold; color: #d32f2f;">${temporaryPassword}</td>
              </tr>
            </table>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>⚠️ IMPORTANT:</strong> For security reasons, you must change your password on first login.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${b2cResetUrl}" style="background-color: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: bold; display: inline-block; box-shadow: 0 3px 6px rgba(0,0,0,0.1);">Access ${platformText}</a>
          </div>

          <div style="background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <h4 style="color: #1976D2; margin-top: 0;">📋 What happens next?</h4>
            <ol style="margin: 10px 0; padding-left: 20px; color: #555;">
              <li>Click the "Access ${platformText}" button above</li>
              <li>You'll be taken to the secure Microsoft B2C login page</li>
              <li>Enter your email: <strong>${email}</strong></li>
              <li>Enter the temporary password shown above</li>
              <li>Microsoft will prompt you to create a new secure password</li>
              <li>After setting your password, you'll be redirected to the ${platformText}</li>
              <li>Use your email and new password for all future logins</li>
            </ol>
          </div>

          <div style="background-color: #e8f5e8; border: 1px solid #4CAF50; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #2e7d32;"><strong>🔐 Secure OAuth Authentication</strong></p>
            <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">This link uses enterprise-grade OAuth 2.0 with PKCE security for maximum protection.</p>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>💡 Important Notes:</strong></p>
            <ul style="margin: 10px 0; padding-left: 20px; color: #856404;">
              <li>The temporary password is only valid for first-time login</li>
              <li>You must create a new password during your first login</li>
              <li>All password management is handled securely by Microsoft</li>
              <li>This link uses the same secure OAuth flow as your regular login</li>
              <li>If you encounter any issues, try refreshing the page</li>
            </ul>
          </div>

          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0; font-size: 14px; color: #666;">
              <strong>Having trouble with the button?</strong><br>
              Copy and paste this link into your browser:
            </p>
            <p style="word-break: break-all; color: #007bff; font-family: monospace; font-size: 12px; background-color: #fff; padding: 8px; border-radius: 4px; margin: 10px 0 0 0;">${b2cResetUrl}</p>
          </div>

          <p style="margin-top: 30px;">If you have any questions or need assistance, feel free to contact our support team.</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team EHR</p>
            <p style="font-size: 12px; margin: 0;">This is an automated message. Please do not reply to this email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }

  async sendQuoteRequestNotification(quoteData) {
    const { name, email, phoneNumber, organizationName, designation, requirements } = quoteData

    // Email to admin/sales team
    const adminEmail = process.env.SALES_EMAIL || process.env.EMAIL_USER
    const subject = `New Quote Request – ${organizationName}`

    const text = `Hello Team,

A new Quote request has been received. Please review the details below and follow up with the requester.

Details
Name: ${name}
Email Id: ${email}
Phone number: ${phoneNumber}
Organization name: ${organizationName}
Designation: ${designation}
Requirements: ${requirements}

Thank you,
Team Arcaai`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <p>Hello Team,</p>

        <p>A new Quote request has been received. Please review the details below and follow up with the requester.</p>

        <h3 style="color: #333; margin-top: 20px;">Details</h3>

        <table style="border-collapse: collapse; margin: 10px 0;">
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold;">Name:</td>
            <td style="padding: 5px 0;">${name}</td>
          </tr>
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold;">Email Id:</td>
            <td style="padding: 5px 0;">${email}</td>
          </tr>
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold;">Phone number:</td>
            <td style="padding: 5px 0;">${phoneNumber}</td>
          </tr>
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold;">Organization name:</td>
            <td style="padding: 5px 0;">${organizationName}</td>
          </tr>
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold;">Designation:</td>
            <td style="padding: 5px 0;">${designation}</td>
          </tr>
          <tr>
            <td style="padding: 5px 10px 5px 0; font-weight: bold; vertical-align: top;">Requirements:</td>
            <td style="padding: 5px 0; white-space: pre-line;">${requirements}</td>
          </tr>
        </table>

        <p style="margin-top: 30px;">Thank you,<br>Team Arcaai</p>
      </div>
    `

    await this.sendEmail(adminEmail, subject, text, html)
  }

  async sendQuoteRequestConfirmation(email, name) {
    const subject = 'Thank you for your interest in our Platform'

    const text = `Dear ${name},\n\nThank you for your interest in our Platform!\n\nWe have received your subscription quote request and our team will review it shortly. One of our representatives will get in touch with you within 1-2 business days to discuss your requirements and provide you with a customized quote.\n\nIn the meantime, if you have any urgent questions, please feel free to reach out to us.\n\nBest regards,\nTeam EHR`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">🎉 Thank You for Your Interest!</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${name}</strong>,</p>

          <p>We're excited about the opportunity to work with you.</p>

          <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #2e7d32;"><strong>✅ Request Received</strong></p>
            <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
              We have received your subscription quote request and our team is reviewing it.
            </p>
          </div>

          <div style="background-color: #fff; border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #4CAF50; margin-top: 0;">📋 What Happens Next?</h3>
            <ol style="margin: 10px 0; padding-left: 20px; color: #555;">
              <li style="margin-bottom: 10px;">Our team will review your requirements</li>
              <li style="margin-bottom: 10px;">We'll prepare a customized quote based on your needs</li>
              <li style="margin-bottom: 10px;">A representative will contact you within <strong>1-2 business days</strong></li>
              <li>We'll schedule a demo if you're interested</li>
            </ol>
          </div>

          <div style="background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #1976D2;"><strong>💡 Questions?</strong></p>
            <p style="margin: 10px 0 0 0; color: #1976D2; font-size: 14px;">
              If you have any urgent questions in the meantime, please don't hesitate to reach out to us.
            </p>
          </div>

          <p style="margin-top: 30px;">We look forward to discussing how the Platform can help transform your healthcare operations!</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team Arcaai</p>
            <p style="font-size: 12px; margin: 0;">This is an automated confirmation email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }

  /**
   * Send plan change confirmation email
   * @param {string} email - Recipient email
   * @param {string} contactName - Contact person name
   * @param {string} previousPlanName - Previous subscription plan name
   * @param {string} newPlanName - New subscription plan name
   * @param {string} billingType - Billing type (monthly/yearly)
   * @param {number} totalAmount - Base amount (tax will be added)
   * @param {Date} startDate - Subscription start date
   * @param {Date} endDate - Subscription end date
   */
  async sendPlanChangeConfirmationEmail(email, contactName, previousPlanName, newPlanName, billingType, totalAmount, startDate, endDate) {
    const subject = 'Plan Change Confirmation - Arca AI EHR'

    // Calculate amount paid with 18% GST
    const taxRate = 0.18
    const amountPaid = totalAmount + (totalAmount * taxRate)

    const text = `Dear ${contactName || 'Valued Customer'},

Your subscription plan has been successfully updated.

Previous Plan: ${previousPlanName}
New Plan: ${newPlanName}
Billing Type: ${billingType}
Amount Paid: ₹${amountPaid.toLocaleString()}
Valid From: ${startDate.toLocaleDateString('en-IN')}
Valid Until: ${endDate.toLocaleDateString('en-IN')}

You can now access all the features included in your new plan. Login to your account to explore the enhanced capabilities.

Thank you for choosing Arca AI EHR!

Best regards,
Team Arcaai`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">🎉 Plan Change Successful!</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${contactName || 'Valued Customer'}</strong>,</p>

          <p>Your subscription plan has been successfully updated. Here are the details of your plan change:</p>

          <div style="background-color: #e8f5e9; border-left: 4px solid #4CAF50; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #2e7d32;"><strong>✅ Plan Updated Successfully</strong></p>
            <p style="margin: 10px 0 0 0; color: #2e7d32; font-size: 14px;">
              Your new subscription is now active and ready to use.
            </p>
          </div>

          <div style="background-color: #fff; border: 2px solid #4CAF50; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #4CAF50; margin-top: 0;">📋 Plan Change Summary</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 10px 0; color: #666; font-weight: bold; width: 40%;">Previous Plan:</td>
                <td style="padding: 10px 0; color: #333;">${previousPlanName}</td>
              </tr>
              <tr style="background-color: #f9f9f9;">
                <td style="padding: 10px 0; color: #666; font-weight: bold;">New Plan:</td>
                <td style="padding: 10px 0; color: #4CAF50; font-weight: bold;">${newPlanName}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; color: #666; font-weight: bold;">Billing Type:</td>
                <td style="padding: 10px 0; color: #333; text-transform: capitalize;">${billingType}</td>
              </tr>
              <tr style="background-color: #f9f9f9;">
                <td style="padding: 10px 0; color: #666; font-weight: bold;">Amount Paid:</td>
                <td style="padding: 10px 0; color: #333;">₹${amountPaid.toLocaleString()}</td>
              </tr>
              <tr>
                <td style="padding: 10px 0; color: #666; font-weight: bold;">Valid From:</td>
                <td style="padding: 10px 0; color: #333;">${startDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' })}</td>
              </tr>
              <tr style="background-color: #f9f9f9;">
                <td style="padding: 10px 0; color: #666; font-weight: bold;">Valid Until:</td>
                <td style="padding: 10px 0; color: #333;">${endDate.toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' })}</td>
              </tr>
            </table>
          </div>

          <div style="background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #1976D2;"><strong>💡 What's Next?</strong></p>
            <p style="margin: 10px 0 0 0; color: #1976D2; font-size: 14px;">
              You can now access all the features included in your new plan. Login to your account to explore the enhanced capabilities.
            </p>
          </div>

          <p style="margin-top: 30px;">Thank you for choosing Arca AI EHR. We're committed to helping you deliver better healthcare!</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team Arcaai</p>
            <p style="font-size: 12px; margin: 0;">This is an automated confirmation email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }

  async sendSubscriptionRenewalReminder(subscription) {
    const email = subscription.contactEmail || subscription.billingDetails?.email
    const name =
      subscription.billingDetails?.contactName ||
      subscription.contactEmail ||
      'Valued Customer'
    const planName = subscription.planName || 'Your Plan'
    const endDate = new Date(subscription.endDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
    const daysRemaining = Math.ceil(
      (new Date(subscription.endDate) - new Date()) / (1000 * 60 * 60 * 24),
    )

    const subject = `⏰ Subscription Renewal Reminder - ${planName}`

    const text = `Dear ${name},\n\nThis is a friendly reminder that your subscription to ${planName} will expire in ${daysRemaining} days on ${endDate}.\n\nTo ensure uninterrupted access to your EHR Platform features, please renew your subscription before the expiry date.\n\nSubscription Details:\n- Plan: ${planName}\n- Expiry Date: ${endDate}\n- Days Remaining: ${daysRemaining}\n\nTo renew your subscription, please log in to your account or contact our support team.\n\nThank you for choosing Arca AI EHR!\n\nBest regards,\nTeam Arcaai`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #FF9800; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">⏰ Subscription Renewal Reminder</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${name}</strong>,</p>

          <p>This is a friendly reminder that your subscription will expire soon.</p>

          <div style="background-color: #fff; border: 2px solid #FF9800; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #FF9800; margin-top: 0;">📋 Subscription Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Plan:</td>
                <td style="padding: 8px 0; color: #333;"><strong>${planName}</strong></td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Expiry Date:</td>
                <td style="padding: 8px 0; color: #d32f2f; font-weight: bold;">${endDate}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Days Remaining:</td>
                <td style="padding: 8px 0; color: #FF9800; font-weight: bold; font-size: 18px;">${daysRemaining} days</td>
              </tr>
            </table>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>⚠️ Action Required:</strong> Please renew your subscription before ${endDate} to ensure uninterrupted access to your EHR Platform features.</p>
          </div>

          <div style="background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 15px; margin: 20px 0;">
            <h4 style="color: #1976D2; margin-top: 0;">📌 Why Renew?</h4>
            <ul style="margin: 10px 0; padding-left: 20px; color: #555;">
              <li>Continue using all premium features without interruption</li>
              <li>Maintain access to your patient records and data</li>
              <li>Keep your team's workflow running smoothly</li>
              <li>Receive ongoing support and updates</li>
            </ul>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="margin-bottom: 15px; color: #555;">To renew your subscription:</p>
            <p style="background-color: #e8f5e9; padding: 15px; border-radius: 6px; margin: 10px 0; color: #2e7d32;">
              <strong>📞 Contact Support:</strong> Reach out to our team for assistance with renewal<br>
              <strong>💻 Login to Dashboard:</strong> Access your account to manage your subscription
            </p>
          </div>

          <p style="margin-top: 30px;">Thank you for choosing Arca AI EHR. We look forward to continuing to serve you!</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team Arcaai</p>
            <p style="font-size: 12px; margin: 0;">This is an automated reminder email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }

  async sendSubscriptionExpiredNotification(subscription) {
    const email = subscription.contactEmail || subscription.billingDetails?.email
    const name =
      subscription.billingDetails?.contactName ||
      subscription.contactEmail ||
      'Valued Customer'
    const planName = subscription.planName || 'Your Plan'
    const endDate = new Date(subscription.endDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })

    const subject = `Subscription Expired - ${planName}`

    const text = `Dear ${name},\n\nYour subscription to ${planName} has expired as of ${endDate}.\n\nYour access to the EHR Platform features has been suspended. To restore your access and continue using the platform, please renew your subscription.\n\nSubscription Details:\n- Plan: ${planName}\n- Expired On: ${endDate}\n- Status: Expired\n\nTo renew your subscription and restore access, please contact our support team or log in to your account.\n\nWe hope to see you back soon!\n\nBest regards,\nTeam Arcaai`

    const html = `
      <div style="font-family: Arial, sans-serif; line-height: 1.5; color: #333; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #d32f2f; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">Subscription Expired</h1>
        </div>

        <div style="background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px;">
          <p style="font-size: 16px; margin-bottom: 20px;">Dear <strong>${name}</strong>,</p>

          <p>We're writing to inform you that your subscription has expired.</p>

          <div style="background-color: #fff; border: 2px solid #d32f2f; border-radius: 8px; padding: 20px; margin: 20px 0;">
            <h3 style="color: #d32f2f; margin-top: 0;">📋 Subscription Details</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Plan:</td>
                <td style="padding: 8px 0; color: #333;"><strong>${planName}</strong></td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Expired On:</td>
                <td style="padding: 8px 0; color: #d32f2f; font-weight: bold;">${endDate}</td>
              </tr>
              <tr>
                <td style="padding: 8px 0; font-weight: bold; color: #555;">Status:</td>
                <td style="padding: 8px 0;">
                  <span style="background-color: #ffebee; color: #d32f2f; padding: 4px 12px; border-radius: 12px; font-weight: bold; font-size: 12px;">EXPIRED</span>
                </td>
              </tr>
            </table>
          </div>

          <div style="background-color: #ffebee; border: 1px solid #ef5350; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #c62828;"><strong>⚠️ Access Suspended:</strong> Your access to EHR Platform features has been suspended due to subscription expiration.</p>
          </div>          

          <div style="text-align: center; margin: 30px 0;">
            <p style="margin-bottom: 15px; color: #555; font-weight: bold;">Ready to Renew?</p>
            <p style="background-color: #e8f5e9; padding: 15px; border-radius: 6px; margin: 10px 0; color: #2e7d32;">
              <strong>📞 Contact Support:</strong> Our team is ready to help you renew<br>
              <strong>💻 Login to Dashboard:</strong> Manage your subscription online
            </p>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; color: #856404;"><strong>💡 Note:</strong> Your data is safely stored. Once you renew, you'll regain immediate access to all your information.</p>
          </div>

          <p style="margin-top: 30px;">We hope to welcome you back soon. Thank you for your past support!</p>

          <footer style="margin-top: 40px; padding-top: 20px; border-top: 2px solid #eee; text-align: center; color: #888;">
            <p style="margin: 0; font-weight: bold;">Best regards,</p>
            <p style="margin: 5px 0 15px 0; font-weight: bold;">Team Arcaai</p>
            <p style="font-size: 12px; margin: 0;">This is an automated notification email.</p>
          </footer>
        </div>
      </div>
    `

    await this.sendEmail(email, subject, text, html)
  }
}

module.exports = new EmailService()
