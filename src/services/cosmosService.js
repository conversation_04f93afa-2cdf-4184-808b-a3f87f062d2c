const { CosmosClient } = require('@azure/cosmos')
const secretManager = require('./secret-manager')
const { logError, logInfo } = require('../common/logging')

// Initialize Cosmos DB client with secrets from Key Vault
let client = null
let databaseId = null
let isInitialized = false
let initializationPromise = null

async function initializeCosmosClient() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logInfo(
      'Initializing Cosmos DB client (cosmosService) with Key Vault secrets...',
    )

    // Get secrets from Key Vault or fallback to environment variables
    const connectionString = await secretManager.getSecret(
      'COSMOS_DB_CONNECTIONSTRING',
    )
    databaseId = await secretManager.getSecret(
      'COSMOS_DB_DATABASE',
      'ArcaAudioLayer',
    )

    if (!connectionString) {
      throw new Error(
        'COSMOS_DB_CONNECTIONSTRING not found in Key Vault or environment variables',
      )
    }

    client = new CosmosClient(connectionString)
    isInitialized = true
    logInfo('Cosmos DB client (cosmosService) initialized successfully')
  } catch (error) {
    logError('Failed to initialize Cosmos DB client (cosmosService):', error)
    isInitialized = false
    throw error
  }
}

const getOrCreateContainer = async (containerId) => {
  // Ensure client is initialized
  if (!isInitialized) {
    await initializeCosmosClient()
  }

  await client.databases.createIfNotExists({ id: databaseId })

  await client.database(databaseId).containers.createIfNotExists({
    id: containerId,
    partitionKey: '/id',
    throughput: 400,
  })

  return client.database(databaseId).container(containerId)
}

exports.saveLabReportMetadata = async (
  doc,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.items.create(doc)
  return doc
}

exports.getLabReportMetadata = async (
  fileId,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  const { resource } = await container.item(fileId, fileId).read()
  return resource
}

exports.updateLabReportMetadata = async (
  doc,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.item(doc.id, doc.id).replace(doc)
  return doc
}

exports.deleteLabReportMetadata = async (
  fileId,
  containerName = 'lab_reports-meta-data',
) => {
  const container = await getOrCreateContainer(containerName)
  await container.item(fileId, fileId).delete()
  return { success: true, deletedId: fileId }
}
