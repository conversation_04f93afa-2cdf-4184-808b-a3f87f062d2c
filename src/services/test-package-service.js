const testPackageRepository = require('../repositories/test-package-repository')
const logging = require('../common/logging')
const TestPackageModel = require('../models/test-package-model')

class TestPackageService {
  async getTestsForPackage(packageId,organizationId) {
    try {
      const result = await testPackageRepository.getTestsByPackageId(packageId)
      if (result && result.length > 0) {
        const tests = result[0].tests || []

        const enrichedTests = await Promise.all(
          tests.map(async (test) => {
            try {
              const pricingInfo =
                await testPackageRepository.getTestPricingInfo(test.testId,organizationId)

              return {
                testName: test.testName,
                testId: test.testId,
                price: pricingInfo?.price || 0,
                organizationCost: pricingInfo?.organizationCost || 0,
              }
            } catch (error) {
              logging.logError(
                `Failed to fetch pricing for test ${test.testId}`,
                error,
              )
              return {
                testName: test.testName,
                testId: test.testId,
                price: 0,
                organizationCost: 0,
              }
            }
          }),
        )

        return enrichedTests
      }
      return []
    } catch (error) {
      logging.logError(`Failed to fetch tests for package ${packageId}`, error)
      return []
    }
  }

  async addTestsToPackage(packageId, tests) {
    try {
      const packageDoc = await testPackageRepository.getTestPackageById(
        packageId,
      )
      if (packageDoc && packageDoc.length > 0) {
        const updatedPackage = packageDoc[0]
        updatedPackage.tests = updatedPackage.tests || []
        for (const test of tests) {
          const alreadyExists = updatedPackage.tests.some(
            (t) => t.testId === test.testId,
          )
          if (!alreadyExists) {
            updatedPackage.tests.push(test)
          }
        }

        await testPackageRepository.upsertTestPackage(updatedPackage)
        return true
      }

      return false
    } catch (error) {
      logging.logError('Failed to add tests to package', error)
      return false
    }
  }

  async getUserSpecificPackages(userId) {
    try {
      const result = await testPackageRepository.getUserSpecificPackages(userId)
      return result
    } catch (error) {
      logging.logError(`Failed to fetch packages for user ${userId}`, error)
      return []
    }
  }

  async createTestPackage(testPackageData, userId) {
    try {
      const existingTestPackages =
        await testPackageRepository.getTestPackageByName(testPackageData.name)

      if (existingTestPackages.length > 0) {
        return {
          error: true,
          message:
            'Test package with the same name already exists for this user',
        }
      }

      const newTestPackage = new TestPackageModel({
        ...testPackageData,
        userId,
      })
      const result = await testPackageRepository.createTestPackage(
        newTestPackage,
      )
      return result
    } catch (error) {
      logging.logError('Failed to create test package', error)
      throw error
    }
  }

  async removeTestFromPackage(packageId, testId) {
    try {
      const packageDoc = await testPackageRepository.getTestPackageById(
        packageId,
      )

      if (packageDoc && packageDoc.length > 0) {
        const updatedPackage = packageDoc[0]
        updatedPackage.tests = updatedPackage.tests || []

        updatedPackage.tests = updatedPackage.tests
          .flat()
          .filter((t) => t.testId !== String(testId))

        await testPackageRepository.upsertTestPackage(updatedPackage)
        return true
      }

      return false
    } catch (error) {
      logging.logError('Failed to remove test from package', error)
      return false
    }
  }

  async getTestPackagesByType(type) {
    try {
      const result = await testPackageRepository.getTestPackagesByType(type)
      return result
    } catch (error) {
      logging.logError(`Failed to fetch test packages of type ${type}`, error)
      return []
    }
  }

  async updateTestPackage(packageId, updates) {
    try {
      const packageDoc = await testPackageRepository.getTestPackageById(
        packageId,
      )

      if (packageDoc && packageDoc.length > 0) {
        const updatedPackage = { ...packageDoc[0], ...updates }
        await testPackageRepository.upsertTestPackage(updatedPackage)
        return updatedPackage
      }

      throw new Error(`Test package with ID ${packageId} not found`)
    } catch (error) {
      logging.logError(`Failed to update test package ${packageId}`, error)
      throw error
    }
  }
}

module.exports = new TestPackageService()
