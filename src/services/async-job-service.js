const { v4: uuidv4 } = require('uuid')
const logging = require('../common/logging')

class AsyncJobService {
  constructor() {
    // In-memory job tracking (in production, use Redis or database)
    this.jobs = new Map()
  }

  // Start an async job and return job ID immediately
  startJob(jobType, jobData, processingFunction) {
    const jobId = uuidv4()
    const job = {
      id: jobId,
      type: jobType,
      status: 'STARTED',
      progress: 0,
      totalItems: 0,
      processedItems: 0,
      startTime: new Date().toISOString(),
      endTime: null,
      result: null,
      error: null,
      data: jobData
    }

    this.jobs.set(jobId, job)

    // Start processing asynchronously
    this.processJobAsync(jobId, processingFunction)

    return jobId
  }

  // Process job asynchronously
  async processJobAsync(jobId, processingFunction) {
    const job = this.jobs.get(jobId)
    if (!job) return

    try {
      job.status = 'PROCESSING'
      this.jobs.set(jobId, job)

      // Create progress callback
      const updateProgress = (processedItems, totalItems, message = '') => {
        job.processedItems = processedItems
        job.totalItems = totalItems
        job.progress = totalItems > 0 ? Math.round((processedItems / totalItems) * 100) : 0
        job.message = message
        this.jobs.set(jobId, job)
        
        console.log(`Job ${jobId}: ${job.progress}% (${processedItems}/${totalItems}) - ${message}`)
      }

      // Execute the processing function with progress callback
      const result = await processingFunction(job.data, updateProgress)

      // Job completed successfully
      job.status = 'COMPLETED'
      job.result = result
      job.endTime = new Date().toISOString()
      job.progress = 100
      this.jobs.set(jobId, job)

      logging.logInfo(`Job ${jobId} completed successfully`)

    } catch (error) {
      // Job failed
      job.status = 'FAILED'
      job.error = error.message
      job.endTime = new Date().toISOString()
      this.jobs.set(jobId, job)

      logging.logError(`Job ${jobId} failed:`, error)
    }
  }

  // Get job status
  getJobStatus(jobId) {
    const job = this.jobs.get(jobId)
    if (!job) {
      return { error: 'Job not found' }
    }

    return {
      id: job.id,
      type: job.type,
      status: job.status,
      progress: job.progress,
      totalItems: job.totalItems,
      processedItems: job.processedItems,
      startTime: job.startTime,
      endTime: job.endTime,
      result: job.result,
      error: job.error,
      message: job.message || ''
    }
  }

  // Clean up old jobs (call periodically)
  cleanupOldJobs(maxAgeHours = 24) {
    const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000)
    
    for (const [jobId, job] of this.jobs.entries()) {
      const jobTime = new Date(job.startTime)
      if (jobTime < cutoffTime) {
        this.jobs.delete(jobId)
        console.log(`Cleaned up old job: ${jobId}`)
      }
    }
  }

  // Get all jobs (for debugging)
  getAllJobs() {
    return Array.from(this.jobs.values())
  }
}

module.exports = new AsyncJobService()
