const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { v4: uuidv4 } = require('uuid')

const containerId = 'consultation-record-history'

class ConsultationRecordHistoryService {
  /**
   * Create a consultation record history entry
   * @param {Object} recordData - The record data containing transcript, userId, patientId, etc.
   * @returns {Object} Created record
   */
  async createConsultationRecordHistory(recordData) {
    try {
      const record = {
        id: uuidv4(),
        transcript: recordData.transcript,
        userId: recordData.userId,
        patientId: recordData.patientId,
        processedAt: recordData.processedAt || new Date().toISOString(),
        status: recordData.status || 'pending',
        type: 'consultation-summary',
      }

      const result = await cosmosDbContext.createItem(record, containerId)
      logging.logInfo(`Created consultation record history: ${result.id}`)
      return result
    } catch (error) {
      logging.logError('Unable to create consultation record history', error)
      throw error
    }
  }

  /**
   * Get the latest consultation record for a specific user and patient
   * @param {string} userId - The doctor/user ID
   * @param {string} patientId - The patient ID
   * @returns {Object|null} Latest record or null if not found
   */
  async getLatestConsultationRecord(userId, patientId) {
    try {
      const query = `
        SELECT * FROM c 
        WHERE c.userId = '${userId}' 
        AND c.patientId = '${patientId}' 
        AND c.type = 'consultation-summary'
        ORDER BY c.created_on DESC
      `

      const result = await cosmosDbContext.queryItems(query, containerId)

      if (result && result.length > 0) {
        logging.logInfo(`Found latest consultation record: ${result[0].id}`)
        return result[0]
      }

      logging.logInfo(
        `No consultation record found for userId: ${userId}, patientId: ${patientId}`,
      )
      return null
    } catch (error) {
      logging.logError('Unable to get latest consultation record', error)
      throw error
    }
  }

  /**
   * Update consultation record history
   * @param {string} recordId - The record ID to update
   * @param {Object} updateData - Data to update
   * @returns {Object} Updated record
   */
  async updateConsultationRecordHistory(recordId, updateData) {
    try {
      const result = await cosmosDbContext.patchItem(
        recordId,
        updateData,
        containerId,
      )
      logging.logInfo(`Updated consultation record history: ${recordId}`)
      return result
    } catch (error) {
      logging.logError('Unable to update consultation record history', error)
      throw error
    }
  }

  /**
   * Delete old consultation records (for cleanup)
   * @param {Date} cutoffDate - Delete records older than this date
   * @returns {number} Number of deleted records
   */
  async deleteOldConsultationRecords(cutoffDate) {
    try {
      const cutoffISOString = cutoffDate.toISOString()
      const query = `
        SELECT c.id FROM c 
        WHERE c.created_on < '${cutoffISOString}' 
        AND c.type = 'consultation-summary'
      `

      const recordsToDelete = await cosmosDbContext.queryItems(
        query,
        containerId,
      )

      if (!recordsToDelete || recordsToDelete.length === 0) {
        logging.logInfo('No old consultation records to delete')
        return 0
      }

      let deletedCount = 0
      for (const record of recordsToDelete) {
        try {
          await cosmosDbContext.deleteItem(record.id, record.id, containerId)
          deletedCount++
        } catch (deleteError) {
          logging.logError(
            `Failed to delete consultation record ${record.id}:`,
            deleteError,
          )
        }
      }

      logging.logInfo(`Deleted ${deletedCount} old consultation records`)
      return deletedCount
    } catch (error) {
      logging.logError('Unable to delete old consultation records', error)
      throw error
    }
  }
}

module.exports = new ConsultationRecordHistoryService()
