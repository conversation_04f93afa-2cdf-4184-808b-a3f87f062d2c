const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const { v4: uuidv4 } = require('uuid')

const containerId = 'lifestyle-record-history'

class LifestyleRecordHistoryService {
  /**
   * Create a lifestyle record history entry
   * @param {Object} recordData - The record data containing transcript, source, userId, patientId, etc.
   * @returns {Object} Created record
   */
  async createLifestyleRecordHistory(recordData) {
    try {
      const record = {
        id: uuidv4(),
        transcript: recordData.transcript,
        source: recordData.source,
        userId: recordData.userId,
        patientId: recordData.patientId,
        processedAt: recordData.processedAt || new Date().toISOString(),
        status: recordData.status || 'pending',
        type: 'lifestyle-ambient-listening',
      }

      const result = await cosmosDbContext.createItem(record, containerId)
      logging.logInfo(`Created lifestyle record history: ${result.id}`)
      return result
    } catch (error) {
      logging.logError('Unable to create lifestyle record history', error)
      throw error
    }
  }

  /**
   * Get the latest lifestyle record for a specific source, patient and user
   * @param {string} source - The source name
   * @param {string} patientId - The patient ID
   * @param {string} userId - The doctor/user ID
   * @returns {Object|null} Latest record or null if not found
   */
  async getLatestLifestyleRecord(source, patientId, userId) {
    try {
      const query = `
        SELECT * FROM c 
        WHERE c.source = '${source}' 
        AND c.patientId = '${patientId}' 
        AND c.userId = '${userId}'
        AND c.type = 'lifestyle-ambient-listening'
        ORDER BY c.created_on DESC
      `

      const result = await cosmosDbContext.queryItems(query, containerId)

      if (result && result.length > 0) {
        logging.logInfo(`Found latest lifestyle record: ${result[0].id}`)
        return result[0]
      }

      logging.logInfo(
        `No lifestyle record found for source: ${source}, patientId: ${patientId}, userId: ${userId}`,
      )
      return null
    } catch (error) {
      logging.logError('Unable to get latest lifestyle record', error)
      throw error
    }
  }

  /**
   * Update lifestyle record history
   * @param {string} recordId - The record ID to update
   * @param {Object} updateData - Data to update
   * @returns {Object} Updated record
   */
  async updateLifestyleRecordHistory(recordId, updateData) {
    try {
      const result = await cosmosDbContext.patchItem(
        recordId,
        updateData,
        containerId,
      )
      logging.logInfo(`Updated lifestyle record history: ${recordId}`)
      return result
    } catch (error) {
      logging.logError('Unable to update lifestyle record history', error)
      throw error
    }
  }

  /**
   * Delete old lifestyle records (for cleanup)
   * @param {Date} cutoffDate - Delete records older than this date
   * @returns {number} Number of deleted records
   */
  async deleteOldLifestyleRecords(cutoffDate) {
    try {
      const cutoffISOString = cutoffDate.toISOString()
      const query = `
        SELECT c.id FROM c 
        WHERE c.created_on < '${cutoffISOString}' 
        AND c.type = 'lifestyle-ambient-listening'
      `

      const recordsToDelete = await cosmosDbContext.queryItems(
        query,
        containerId,
      )

      if (!recordsToDelete || recordsToDelete.length === 0) {
        logging.logInfo('No old lifestyle records to delete')
        return 0
      }

      let deletedCount = 0
      for (const record of recordsToDelete) {
        try {
          await cosmosDbContext.deleteItem(record.id, record.id, containerId)
          deletedCount++
        } catch (deleteError) {
          logging.logError(
            `Failed to delete lifestyle record ${record.id}:`,
            deleteError,
          )
        }
      }

      logging.logInfo(`Deleted ${deletedCount} old lifestyle records`)
      return deletedCount
    } catch (error) {
      logging.logError('Unable to delete old lifestyle records', error)
      throw error
    }
  }

  /**
   * Get lifestyle records by source name
   * @param {string} source - The source name
   * @param {number} pageSize - Page size
   * @param {string} continuationToken - Continuation token for pagination
   * @returns {Object} Query result with items and pagination info
   */
  async getLifestyleRecordsBySource(
    source,
    pageSize = 10,
    continuationToken = null,
  ) {
    try {
      const query = `
        SELECT * FROM c 
        WHERE c.source = '${source}' 
        AND c.type = 'lifestyle-ambient-listening'
        ORDER BY c.created_on DESC
      `

      const result = await cosmosDbContext.getAllItemQuery(
        containerId,
        query,
        pageSize,
        continuationToken,
      )
      return result
    } catch (error) {
      logging.logError('Unable to get lifestyle records by source', error)
      throw error
    }
  }
}

module.exports = new LifestyleRecordHistoryService()
