const EncryptionUtils = require('../utils/encryption-utils');

const PATIENT_SENSITIVE_FIELDS = [
  'contact.phone',
  'contact.email',
  'aadhar',         
  'abha',           
  'proof.aadharNumber',
  'proof.abhaNumber',
  'address.phone',
  'address.email',
  'insurance.id'
];

const FIELD_PERMISSION_MAPPING = {
  'aadhar': 'emr.patientinfo.view.aadhar',              
  'abha': 'emr.patientinfo.view.abha',                 
  'proof.aadharNumber': 'emr.patientinfo.view.aadhar',
  'proof.abhaNumber': 'emr.patientinfo.view.abha',
  'contact.phone': 'emr.patientinfo.view.sensitive',
  'contact.email': 'emr.patientinfo.view.sensitive',
  'address.phone': 'emr.patientinfo.view.sensitive',
  'address.email': 'emr.patientinfo.view.sensitive',
  'insurance.id': 'emr.patientinfo.view.sensitive'
};


class PatientEncryptionService {
  // New secure methods using random IV encryption
  static encryptPatientDataSecure(patientData) {
    return EncryptionUtils.encryptSensitiveFieldsSecure(patientData, PATIENT_SENSITIVE_FIELDS);
  }

  static decryptPatientDataSecure(patientData, userPermissions = []) {
    return EncryptionUtils.decryptSensitiveFieldsSecure(
      patientData, 
      PATIENT_SENSITIVE_FIELDS, 
      userPermissions, 
      FIELD_PERMISSION_MAPPING
    );
  }

  // Legacy methods for backward compatibility (using fixed IV)
  static encryptPatientData(patientData) {
    return EncryptionUtils.encryptSensitiveFields(patientData, PATIENT_SENSITIVE_FIELDS);
  }

  static decryptPatientData(patientData, userPermissions = []) {
    return EncryptionUtils.decryptSensitiveFields(
      patientData, 
      PATIENT_SENSITIVE_FIELDS, 
      userPermissions, 
      FIELD_PERMISSION_MAPPING
    );
  }

  static getSensitiveFields() {
    return [...PATIENT_SENSITIVE_FIELDS];
  }

  static getFieldPermissionMapping() {
    return {...FIELD_PERMISSION_MAPPING};
  }

  static hasPermissionForField(fieldPath, userPermissions = []) {
    const requiredPermission = FIELD_PERMISSION_MAPPING[fieldPath];
    if (!requiredPermission) {
      return true;
    }
    
    return userPermissions.includes(requiredPermission) || 
           userPermissions.includes('emr.patientinfo.view.sensitive');
  }

  static addSensitiveField(fieldPath, requiredPermission = null) {
    if (!PATIENT_SENSITIVE_FIELDS.includes(fieldPath)) {
      PATIENT_SENSITIVE_FIELDS.push(fieldPath);
    }
    
    if (requiredPermission) {
      FIELD_PERMISSION_MAPPING[fieldPath] = requiredPermission;
    }
  }

  static removeSensitiveField(fieldPath) {
    const index = PATIENT_SENSITIVE_FIELDS.indexOf(fieldPath);
    if (index > -1) {
      PATIENT_SENSITIVE_FIELDS.splice(index, 1);
    }
    
    delete FIELD_PERMISSION_MAPPING[fieldPath];
  }
}

module.exports = PatientEncryptionService;
