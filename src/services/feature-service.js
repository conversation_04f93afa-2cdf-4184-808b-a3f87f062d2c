const featureRepository = require('../repositories/feature-repository')
const logging = require('../common/logging')

class FeatureService {
  async createFeature(featureData, userId) {
    try {
      // Validate required fields
      if (!featureData.featureName) {
        throw new Error('Feature name is required')
      }

      const feature = await featureRepository.createFeature({
        ...featureData,
        created_by: userId,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Feature created successfully',
        data: feature,
      }
    } catch (error) {
      logging.logError('Error in createFeature:', error)
      return {
        success: false,
        message: error.message || 'Failed to create feature',
      }
    }
  }

  async updateFeature(featureId, featureData, userId) {
    try {
      // Check if feature exists
      const existingFeature = await featureRepository.getFeatureById(featureId)
      if (!existingFeature) {
        throw new Error('Feature not found')
      }

      const feature = await featureRepository.updateFeature(featureId, {
        ...featureData,
        updated_by: userId,
      })

      return {
        success: true,
        message: 'Feature updated successfully',
        data: feature,
      }
    } catch (error) {
      logging.logError('Error in updateFeature:', error)
      return {
        success: false,
        message: error.message || 'Failed to update feature',
      }
    }
  }

  async deleteFeature(featureId) {
    try {
      // Check if feature exists
      const existingFeature = await featureRepository.getFeatureById(featureId)
      if (!existingFeature) {
        throw new Error('Feature not found')
      }

      await featureRepository.deleteFeature(featureId)

      return {
        success: true,
        message: 'Feature deleted successfully',
      }
    } catch (error) {
      logging.logError('Error in deleteFeature:', error)
      return {
        success: false,
        message: error.message || 'Failed to delete feature',
      }
    }
  }

  async deactivateFeature(featureId, userId) {
    try {
      const feature = await featureRepository.deactivateFeature(featureId, userId)

      return {
        success: true,
        message: 'Feature deactivated successfully',
        data: feature,
      }
    } catch (error) {
      logging.logError('Error in deactivateFeature:', error)
      return {
        success: false,
        message: error.message || 'Failed to deactivate feature',
      }
    }
  }

  async getFeature(featureId) {
    try {
      const feature = await featureRepository.getFeatureById(featureId)

      if (!feature) {
        return {
          success: false,
          message: 'Feature not found',
        }
      }

      return {
        success: true,
        data: feature,
      }
    } catch (error) {
      logging.logError('Error in getFeature:', error)
      return {
        success: false,
        message: error.message || 'Failed to get feature',
      }
    }
  }

  async getAllFeatures(includeInactive = false) {
    try {
      const features = await featureRepository.getAllFeatures(includeInactive)

      return {
        success: true,
        data: features,
        count: features.length,
      }
    } catch (error) {
      logging.logError('Error in getAllFeatures:', error)
      return {
        success: false,
        message: error.message || 'Failed to get features',
      }
    }
  }

  async searchFeatures(searchParams) {
    try {
      const features = await featureRepository.searchFeatures(searchParams)

      return {
        success: true,
        data: features,
        count: features.length,
      }
    } catch (error) {
      logging.logError('Error in searchFeatures:', error)
      return {
        success: false,
        message: error.message || 'Failed to search features',
      }
    }
  }

  async getFeaturesByPermissionKey(permissionKey) {
    try {
      if (!permissionKey) {
        throw new Error('Permission key is required')
      }

      const features = await featureRepository.getFeaturesByPermissionKey(permissionKey)

      return {
        success: true,
        data: features,
        count: features.length,
      }
    } catch (error) {
      logging.logError('Error in getFeaturesByPermissionKey:', error)
      return {
        success: false,
        message: error.message || 'Failed to get features by permission key',
      }
    }
  }

  async getAllPermissionKeys() {
    try {
      const permissionKeys = await featureRepository.getAllPermissionKeys()

      return {
        success: true,
        data: permissionKeys,
        count: permissionKeys.length,
      }
    } catch (error) {
      logging.logError('Error in getAllPermissionKeys:', error)
      return {
        success: false,
        message: error.message || 'Failed to get permission keys',
      }
    }
  }
}

module.exports = new FeatureService()
