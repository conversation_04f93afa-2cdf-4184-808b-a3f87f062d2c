const crypto = require('crypto')
const { v4: uuidv4 } = require('uuid')
const secretManager = require('./secret-manager')
const logging = require('../common/logging')

/**
 * Payment Tokenization Service
 * Handles secure tokenization of sensitive payment data
 * Ensures sensitive payment details are never stored in plain text
 */
class PaymentTokenizationService {
  constructor() {
    this.tokenizationKey = null
    this.isInitialized = false
    this.initializationPromise = null
    
    // Define sensitive payment fields that should be tokenized
    this.sensitiveFields = [
      'razorpayPaymentId',
      'razorpaySignature', 
      'cardNumber',
      'cardHolderName',
      'cvv',
      'bankAccount',
      'ifscCode',
      'upiId'
    ]
  }

  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._doInitialize()
    return this.initializationPromise
  }

  async _doInitialize() {
    try {
      logging.logInfo('Initializing Payment Tokenization Service...')
      
      // Get tokenization key from Key Vault or generate one
      this.tokenizationKey = await secretManager.getSecret('PAYMENT_TOKENIZATION_KEY')
      
      if (!this.tokenizationKey) {
        // Generate a new tokenization key if not found
        this.tokenizationKey = crypto.randomBytes(32).toString('hex')
        logging.logWarning('Payment tokenization key not found in Key Vault, using generated key')
      }

      this.isInitialized = true
      logging.logInfo('Payment Tokenization Service initialized successfully')
    } catch (error) {
      logging.logError('Failed to initialize Payment Tokenization Service:', error)
      this.isInitialized = false
      throw error
    }
  }

  /**
   * Generate a secure payment token
   * @param {string} sensitiveData - The sensitive data to tokenize
   * @returns {string} - Secure token
   */
  async generateToken(sensitiveData) {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      // Generate a unique token ID
      const tokenId = uuidv4()
      
      // Create HMAC for token integrity
      const hmac = crypto.createHmac('sha256', this.tokenizationKey)
      hmac.update(sensitiveData + tokenId)
      const signature = hmac.digest('hex')
      
      // Create token with format: tokenId:signature:timestamp
      const timestamp = Date.now()
      const token = `${tokenId}:${signature}:${timestamp}`
      
      return Buffer.from(token).toString('base64')
    } catch (error) {
      logging.logError('Error generating payment token:', error)
      throw new Error('Failed to generate payment token')
    }
  }

  /**
   * Validate a payment token
   * @param {string} token - The token to validate
   * @param {string} originalData - The original data to validate against
   * @returns {boolean} - True if token is valid
   */
  async validateToken(token, originalData) {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      const decodedToken = Buffer.from(token, 'base64').toString('utf8')
      const [tokenId, signature, timestamp] = decodedToken.split(':')
      
      if (!tokenId || !signature || !timestamp) {
        return false
      }

      // Check token age (tokens expire after 24 hours)
      const tokenAge = Date.now() - parseInt(timestamp)
      if (tokenAge > 24 * 60 * 60 * 1000) {
        logging.logWarning('Payment token expired')
        return false
      }

      // Verify signature
      const hmac = crypto.createHmac('sha256', this.tokenizationKey)
      hmac.update(originalData + tokenId)
      const expectedSignature = hmac.digest('hex')
      
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      )
    } catch (error) {
      logging.logError('Error validating payment token:', error)
      return false
    }
  }

  /**
   * Tokenize sensitive payment data
   * @param {Object} paymentData - Payment data object
   * @returns {Object} - Payment data with sensitive fields tokenized
   */
  async tokenizePaymentData(paymentData) {
    if (!paymentData || typeof paymentData !== 'object') {
      return paymentData
    }

    const tokenizedData = { ...paymentData }
    const tokenMap = {}

    for (const field of this.sensitiveFields) {
      if (tokenizedData[field] && typeof tokenizedData[field] === 'string') {
        const token = await this.generateToken(tokenizedData[field])
        tokenMap[field] = tokenizedData[field] // Store original for validation
        tokenizedData[field] = token
        tokenizedData[`${field}_tokenized`] = true
      }
    }

    // Store token mapping for validation (this should be stored securely)
    tokenizedData._tokenMap = tokenMap
    
    return tokenizedData
  }

  /**
   * Create a payment reference token for external systems
   * @param {Object} paymentReference - Payment reference data
   * @returns {string} - Secure payment reference token
   */
  async createPaymentReferenceToken(paymentReference) {
    const referenceData = {
      paymentId: paymentReference.paymentId,
      organizationId: paymentReference.organizationId,
      amount: paymentReference.amount,
      timestamp: Date.now()
    }

    const referenceString = JSON.stringify(referenceData)
    return await this.generateToken(referenceString)
  }

  /**
   * Validate payment reference token
   * @param {string} token - Payment reference token
   * @param {Object} expectedReference - Expected reference data
   * @returns {boolean} - True if valid
   */
  async validatePaymentReferenceToken(token, expectedReference) {
    const referenceString = JSON.stringify({
      paymentId: expectedReference.paymentId,
      organizationId: expectedReference.organizationId,
      amount: expectedReference.amount,
      timestamp: expectedReference.timestamp
    })

    return await this.validateToken(token, referenceString)
  }

  /**
   * Generate secure payment session token
   * @param {Object} sessionData - Payment session data
   * @returns {string} - Session token
   */
  async generatePaymentSessionToken(sessionData) {
    const sessionInfo = {
      sessionId: uuidv4(),
      paymentId: sessionData.paymentId,
      userId: sessionData.userId,
      organizationId: sessionData.organizationId,
      expiresAt: Date.now() + (30 * 60 * 1000) // 30 minutes
    }

    return await this.generateToken(JSON.stringify(sessionInfo))
  }
}

module.exports = new PaymentTokenizationService()
