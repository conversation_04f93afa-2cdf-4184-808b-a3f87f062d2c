const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const bcrypt = require('bcrypt')
const containerId = 'Users'
const { getUsersByOrganizationQuery, getUserByEmailIncludingInactiveQuery } = require('../queries/user-query')
const userRepository = require('../repositories/admin/user-repository') // Import the missing userRepository

class UserService {
  async getUserById(id) {
    try {
      var data = await cosmosDbContext.readItem(id, id, containerId)
      logging.logInfo(`Get user by ID: ${id}`)
      if (!data) {
        logging.logInfo(
          `Direct ID lookup failed, trying B2C user ID search for: ${id}`,
        )
        data = await this.getUserByB2CUserId(id)
      }
      return data
    } catch (error) {
      logging.logError(`Unable to get user by id ${id}`, error)
      logging.logInfo(
        `Direct ID lookup failed, trying B2C user ID search for: ${id}`,
      )
      return null
    }
  }

  async addUser(user) {
    try {
      // Ensure `isActive` is explicitly set
      user.isActive = user.isActive !== undefined ? user.isActive : false
      var res = await cosmosDbContext.createItem(user, containerId)
      return res
    } catch (error) {
      logging.logError(`Uable to create user`, error)
      return null
    }
  }

  async updateUser(user) {
    try {
      var res = await cosmosDbContext.updateItem(user, containerId)
      return res
    } catch (error) {
      logging.logError(`Unable to update user`, error)
      return null
    }
  }

  async deleteUser(userId) {
    try {
      logging.logInfo(
        `Attempting to delete user from local database: ${userId}`,
      )
      var res = await cosmosDbContext.deleteItem(userId, userId, containerId)
      logging.logInfo(
        `User deleted from local database successfully: ${userId}`,
      )
      return { success: true, deletedResource: res }
    } catch (error) {
      logging.logError(
        `Unable to delete user ${userId} from local database:`,
        error,
      )
      logging.logError(`Delete error details:`, {
        userId: userId,
        containerId: containerId,
        errorMessage: error.message,
        errorCode: error.code,
        statusCode: error.statusCode,
      })
      throw error
    }
  }

  async getUserByQuery(queryString) {
    try {
      var data = await cosmosDbContext.queryItems(queryString, containerId)
      return data
    } catch (error) {
      logging.logError(`Unable to get user by query ${queryString}`, error)
      return null
    }
  }

  async getAllUser(pageSize, continuetoken) {
    try {
      var data = await cosmosDbContext.getAllItems(
        containerId,
        pageSize,
        continuetoken,
      )
      return data
    } catch (error) {
      logging.logError(`Unable to get all users`)
      return null
    }
  }

  async getUsersByQuery(query, pageSize, continuetoken) {
    try {
      var data = await cosmosDbContext.getAllItemQuery(
        containerId,
        query,
        pageSize,
        continuetoken,
      )
      return data
    } catch (error) {
      logging.logError(`Unable to get users by query ${query}`)
      return null
    }
  }

  async updateUserByEmail(email, userData) {
    try {
      const users = await this.getUserByEmail(email)

      if (users && users.length > 0) {
        const user = users[0]
        Object.assign(user, userData)
        const res = await this.updateUser(user)
        return res
      }
      return null
    } catch (error) {
      logging.logError(`Unable to update user by email ${email}`, error)
      return null
    }
  }

  async hashPassword(password) {
    return bcrypt.hash(password, 10)
  }

  async getUserByEmail(email) {
    const query = `SELECT * FROM c WHERE c.email = '${email}' AND c.isActive = true`
    return await this.getUserByQuery(query)
  }

  async getUserByEmailIncludingInactive(email) {
    const query = getUserByEmailIncludingInactiveQuery(email)
    return await this.getUserByQuery(query)
  }

  async getUserByB2CUserId(b2cUserId) {
    const query = `SELECT * FROM c WHERE c.b2cUserId = '${b2cUserId}'`
    const users = await this.getUserByQuery(query)
    return users && users.length > 0 ? users[0] : null
  }

  async comparePassword(plainPassword, hashedPassword) {
    if (!plainPassword || !hashedPassword) {
      throw new Error('Both plainPassword and hashedPassword are required')
    }
    return bcrypt.compare(plainPassword, hashedPassword)
  }

  async validateResetToken(token) {
    const query = `SELECT * FROM c WHERE c.resetToken = '${token}' AND c.resetTokenExpiry > GETDATE()`
    const users = await this.getUserByQuery(query)

    if (users && users.length > 0) {
      return users[0]
    }

    return null
  }

  async updatePassword(userId, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, 10)
    const user = await this.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    user.password = hashedPassword
    user.resetToken = null // Clear the reset token
    user.resetTokenExpiry = null

    return this.updateUser(user)
  }

  async validateActivationToken(resetToken) {
    const currentDate = new Date().toISOString() // Get current date in ISO format
    const query = `SELECT * FROM c WHERE c.resetToken = '${resetToken}' AND c.resetTokenExpiry > '${currentDate}'`
    const users = await this.getUserByQuery(query)

    if (users && users.length > 0) {
      return users[0]
    }

    return null
  }

  async updatePasswordAndActivateUser(userId, newPassword) {
    const hashedPassword = await bcrypt.hash(newPassword, 10)
    const user = await this.getUserById(userId)

    if (!user) {
      throw new Error('User not found')
    }

    user.password = hashedPassword
    user.resetToken = null
    user.resetTokenExpiry = null
    user.isActive = true

    return this.updateUser(user)
  }

  async getUsersByOrganization(
    organizationId,
    search,
    role,
    isActive,
    sortBy,
    sortOrder,
    pageSize,
    page,
    continueToken,
  ) {
    const { getUsersCountQuery } = require('../queries/user-query')

    if (continueToken) {
      const query = getUsersByOrganizationQuery(
        organizationId,
        search,
        role,
        isActive,
        sortBy,
        sortOrder,
      )

      const countQuery = getUsersCountQuery(organizationId, search, role, isActive)

      const [items, totalCount] = await Promise.all([
        userRepository.getUsersByQuery(query, pageSize, continueToken),
        userRepository.getUsersCount(countQuery),
      ])

      return {
        items: items || [],
        totalCount: totalCount || 0,
      }
    }

    const query = getUsersByOrganizationQuery(
      organizationId,
      search,
      role,
      isActive,
      sortBy,
      sortOrder,
      pageSize,
      page,
    )

    const countQuery = getUsersCountQuery(organizationId, search, role, isActive)

    const [items, totalCount] = await Promise.all([
      userRepository.getUsersByQueryDirect(query),
      userRepository.getUsersCount(countQuery),
    ])

    return {
      items: items || [],
      totalCount: totalCount || 0,
    }
  }
}

module.exports = new UserService()
