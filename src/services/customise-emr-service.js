const { logError, logInfo } = require("../common/logging");
const cosmosDbContext = require("../cosmosDbContext/comosdb-context");
const customiseEmrContainerId = "CustomiseEmrs";

class CustomiseEmrService {

    async getCustomiseEmrByQuery(queryString) {
        try {
            logInfo(`GET customise emr with query:: ${queryString}`)
            var data = await cosmosDbContext.queryItems(queryString, customiseEmrContainerId);
            return data;
        } catch (error) {
            logError(`Unable to get customise emr`, error)
            return null;
        }
    }

    async getCustomiseEmrBySourceName(sourceName){
        try {
            logInfo(`GET customise emr with sourceName:: ${sourceName}`)
            var data = await cosmosDbContext.queryItems(`SELECT * FROM c WHERE c.source_name = "${sourceName}"`, customiseEmrContainerId);
            return data;
        } catch (error) {
            logError(`Unable to get customise emr`, error)
            return null;
        }
    }

    async createCustomiseEmr(customiseEmr) {
        try {
            logInfo(`Create customise emr :: ${JSON.stringify(customiseEmr)}`)
            var result = await cosmosDbContext.createItem(customiseEmr, customiseEmrContainerId)
            return result;
        } catch (error) {
            logError(`Unable to create customise emr`, error)
            return null;
        }
    }

   async patchCustomiseEmr(customiseEmrId, customiseEmrData) {
        try {
            logInfo(`Patch customise emr :: ${customiseEmrId} :: ${JSON.stringify(customiseEmrData)}`)
            var result = await cosmosDbContext.patchItem(customiseEmrId, customiseEmrData, customiseEmrContainerId)
            return result;
        } catch (error) {
            logError(`Unable to patch customise emr`, error)
            return null;
        }
    }


}

module.exports = new CustomiseEmrService();