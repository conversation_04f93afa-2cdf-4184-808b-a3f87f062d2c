const msal = require('@azure/msal-node')
const { Client } = require('@microsoft/microsoft-graph-client')
const { logError, logInfo } = require('../common/logging')
const helper = require('../common/helper')
const secretManager = require('./secret-manager')

let cca = null
let isInitialized = false
let initializationPromise = null

async function initializeMSAL() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logInfo('Initializing MSAL with Key Vault secrets...')

    const clientId = await secretManager.getSecret('CLIENT_ID')
    const tenantId = await secretManager.getSecret('TENANT_ID')
    const clientSecret = await secretManager.getSecret('CLIENT_SECRET')

    if (!clientId || !tenantId || !clientSecret) {
      throw new Error(
        'Missing required Azure AD credentials in Key Vault or environment variables',
      )
    }

    const config = {
      auth: {
        clientId,
        authority: `https://login.microsoftonline.com/${tenantId}`,
        clientSecret,
      },
    }

    cca = new msal.ConfidentialClientApplication(config)
    isInitialized = true
    logInfo('MSAL initialized successfully')
  } catch (error) {
    logError('Failed to initialize MSAL:', error)
    isInitialized = false
    throw error
  }
}
class GraphService {
  async getToken() {
    try {
      if (!isInitialized) {
        await initializeMSAL()
      }

      const tokenResponse = await cca.acquireTokenByClientCredential({
        scopes: ['https://graph.microsoft.com/.default'],
      })
      return tokenResponse
    } catch (error) {
      logError(`Unable to get token :: `, error)
      return null
    }
  }

  async createUser(accessToken, user) {
    try {
      logInfo(`Creating user :: ${JSON.stringify(user)}`)
      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken) // Pass the token to the auth provider
        },
      })

      var data = await client.api('/users').post(user)
      return data
    } catch (error) {
      if (error.statusCode === 409) {
        logError(
          `Unable to create user: User with userPrincipalName ${user.userPrincipalName} already exists.`,
          error,
        )
      } else {
        logError(`Unable to create user`, error)
      }
      throw error // Re-throw the error for further handling
    }
  }

  async getUserByPrincipalName(accessToken, userPrincipalName) {
    try {
      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      const user = await client.api(`/users/${userPrincipalName}`).get()
      return user
    } catch (error) {
      if (error.statusCode === 404) {
        console.log(
          `User with principal name ${userPrincipalName} does not exist.`,
        )
        return null // User does not exist
      }
      console.error(
        `Error fetching user with principal name ${userPrincipalName}:`,
        error,
      )
      throw error // Re-throw other errors
    }
  }

  async updateUser(accessToken, userId, updateData) {
    try {
      logInfo(
        `Updating B2C user: ${userId} with data: ${JSON.stringify(updateData)}`,
      )

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      const b2cUpdateData = {}
      if (updateData.name) {
        b2cUpdateData.displayName = updateData.name
      }

      if (updateData.accountEnabled !== undefined) {
        b2cUpdateData.accountEnabled = updateData.accountEnabled
      }

      if (updateData.passwordProfile) {
        b2cUpdateData.passwordProfile = updateData.passwordProfile
      }

      if (Object.keys(b2cUpdateData).length === 0) {
        logInfo('No B2C fields to update')
        return { success: true, message: 'No B2C fields to update' }
      }

      const result = await client.api(`/users/${userId}`).patch(b2cUpdateData)
      logInfo(`B2C user updated successfully: ${userId}`)
      return result
    } catch (error) {
      logError(`Error updating B2C user ${userId}:`, error)
      throw error
    }
  }

  async deleteUser(accessToken, userId) {
    try {
      logInfo(`Deleting B2C user: ${userId}`)

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      await client.api(`/users/${userId}`).delete()
      logInfo(`B2C user deleted successfully: ${userId}`)
      return { success: true, message: 'User deleted from B2C successfully' }
    } catch (error) {
      logError(`Error deleting B2C user ${userId}:`, error)
      throw error
    }
  }

  async permanentlyDeleteUser(accessToken, userId) {
    try {
      logInfo(`Permanently deleting B2C user (bypassing 30-day soft delete): ${userId}`)

      const client = Client.init({
        authProvider: (done) => {
          done(null, accessToken)
        },
      })

      // Step 1: Soft delete the user
      await client.api(`/users/${userId}`).delete()
      logInfo(`Step 1: B2C user soft deleted: ${userId}`)

      // Step 2: Permanently delete from deleted items (removes 30-day recovery period)
      await client.api(`/directory/deletedItems/${userId}`).delete()
      logInfo(`Step 2: B2C user permanently deleted from recycle bin: ${userId}`)

      return { success: true, message: 'User permanently deleted from B2C (bypassed 30-day period)' }
    } catch (error) {
      logError(`Error permanently deleting B2C user ${userId}:`, error)
      throw error
    }
  }
}

module.exports = new GraphService()
