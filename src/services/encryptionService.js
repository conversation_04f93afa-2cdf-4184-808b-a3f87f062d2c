const crypto = require('crypto')

exports.encryptBuffer = async (buffer) => {
  if (!buffer || !Buffer.isBuffer(buffer)) {
    throw new Error('Invalid buffer: expected a Buffer object')
  }

  const key = crypto.randomBytes(32)
  const iv = crypto.randomBytes(16)
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
  const encrypted = Buffer.concat([cipher.update(buffer), cipher.final()])
  return {
    encryptedData: encrypted,
    encryptionKey: key.toString('hex'),
    iv: iv.toString('hex'),
  }
}

exports.decryptBuffer = async (encryptedBuffer, keyHex, ivHex) => {
  const key = Buffer.from(keyHex, 'hex')
  const iv = Buffer.from(ivHex, 'hex')
  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
  return Buffer.concat([decipher.update(encryptedBuffer), decipher.final()])
}
