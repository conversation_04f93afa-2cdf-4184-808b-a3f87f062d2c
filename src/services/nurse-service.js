const cosmosDbContext = require('../cosmosDbContext/comosdb-context');
const logging = require('../common/logging');
const containerId = "Nurses"

class NurseService {
    /**
 * Create a nurse
 * @param {*} nurse 
 * @returns any
 */
    async createNurse(nurse) {
        try {
            var result = await cosmosDbContext.createItem(nurse, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to create nurse", error);
            return null;
        }
    }

    /**
     * Get nurse by ID
     * @param {*} id string
     * @returns any
     */
    async getNurse(id) {
        try {
            var result = await cosmosDbContext.readItem(id, id, containerId);
            return result
        } catch (error) {
            logging.logError("unable to get nurse", error);
            return null;
        }
    }

    /**
     * Get nurses by query string
     * @param {query} query string 
     * @returns any
     */
    async queryNurses(query, pageSize, continueToken) {
        try {
            var result = await cosmosDbContext.getAllItemQuery(containerId, query, pageSize, continueToken);
            return result;
        } catch (error) {
            logging.logError("Unable to get nurses", error);
            return null;
        }
    }

    /**
     * Get all nurses with paging, default is 10 records per query
     * @param {*} pageSize the number of record will be returned, default is 10
     * @param {*} continueToken to query next page
     * @returns 
     */
    async getAllNurses(pageSize, continueToken) {
        try {
            var result = await cosmosDbContext.getAllItems(containerId, pageSize, continueToken)
            return result;
        } catch (error) {
            logging.logError("Unable to get nurses", error);
            return null;
        }
    }
}

module.exports = new NurseService();