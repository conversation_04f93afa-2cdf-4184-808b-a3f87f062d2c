const medicalHistoryAddictionRepository = require('../repositories/medical-history-addiction-repository');
const logging = require('../common/logging');

class MedicalHistoryAddictionService {

    async createMedicalHistoryAddiction(medicalHistory) {
        try {
            return await medicalHistoryAddictionRepository.create(medicalHistory);
        } catch (error) {
            logging.logError("Service: Unable to create medical history addiction", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionByPatientId(patientId) {
        try {
            return await medicalHistoryAddictionRepository.getByPatientId(patientId);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by patient ID", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionById(id) {
        try {
            return await medicalHistoryAddictionRepository.getById(id);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by ID", error);
            throw error;
        }
    }

    async updateMedicalHistoryAddiction(id, updateData) {
        try {
            return await medicalHistoryAddictionRepository.update(id, updateData);
        } catch (error) {
            logging.logError("Service: Unable to update medical history addiction", error);
            throw error;
        }
    }

    async patchMedicalHistoryAddiction(id, patchPayload) {
        try {
            return await medicalHistoryAddictionRepository.patch(id, patchPayload);
        } catch (error) {
            logging.logError("Service: Unable to patch medical history addiction", error);
            throw error;
        }
    }

    async deleteMedicalHistoryAddiction(id) {
        try {
            return await medicalHistoryAddictionRepository.delete(id);
        } catch (error) {
            logging.logError("Service: Unable to delete medical history addiction", error);
            throw error;
        }
    }

    async checkExistingRecord(patientId) {
        try {
            return await medicalHistoryAddictionRepository.checkExistingRecord(patientId);
        } catch (error) {
            logging.logError("Service: Unable to check existing record", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionByDateRange(patientId, startDate, endDate) {
        try {
            return await medicalHistoryAddictionRepository.getByDateRange(patientId, startDate, endDate);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by date range", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionByDiagnosis(diseaseName) {
        try {
            return await medicalHistoryAddictionRepository.getByDiagnosis(diseaseName);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by diagnosis", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionBySubstanceHistory(substanceType, history) {
        try {
            return await medicalHistoryAddictionRepository.getBySubstanceHistory(substanceType, history);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by substance history", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionWithNicotineTest() {
        try {
            return await medicalHistoryAddictionRepository.getWithNicotineTest();
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction with nicotine test", error);
            throw error;
        }
    }

    async getMedicalHistoryAddictionByCreatedBy(createdBy) {
        try {
            return await medicalHistoryAddictionRepository.getByCreatedBy(createdBy);
        } catch (error) {
            logging.logError("Service: Unable to get medical history addiction by created by", error);
            throw error;
        }
    }
}

module.exports = new MedicalHistoryAddictionService();