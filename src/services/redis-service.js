const redis = require('redis')
const secretManager = require('./secret-manager')
const { logError, logInfo } = require('../common/logging')

// Initialize Redis connection with secrets from Key Vault
let cacheConnection = null
let isInitialized = false
let initializationPromise = null

async function initializeRedis() {
  if (initializationPromise) {
    return initializationPromise
  }

  initializationPromise = _doInitialize()
  return initializationPromise
}

async function _doInitialize() {
  try {
    logInfo('Initializing Redis with Key Vault secrets...')

    // Get secrets from Key Vault or fallback to environment variables
    const redisHostname = await secretManager.getSecret('REDISCACHEHOSTNAME')
    const redisKey = await secretManager.getSecret('REDISCACHEKEY')

    if (!redisHostname || !redisKey) {
      throw new Error(
        'Missing Redis credentials in Key Vault or environment variables',
      )
    }

    // Convert Redis client API to use promises, to make it usable with async/await syntax
    // Connect to the Azure Cache for Redis over the TLS port using the key.
    cacheConnection = redis.createClient({
      // rediss for TLS
      url: 'rediss://' + redisHostname + ':6380',
      password: redisKey,
    })

    await cacheConnection.connect()
    logInfo('Redis connected successfully')

    cacheConnection.on('error', function (error) {
      logError('Redis error:', error)
    })

    cacheConnection.on('reconnecting', () => logInfo('Redis reconnecting'))
    cacheConnection.on('ready', () => logInfo('Redis ready'))

    isInitialized = true
  } catch (error) {
    logError('Failed to initialize Redis:', error)
    isInitialized = false
    throw error
  }
}

// expire time default : 6 hours
const EXPIRED_TIME = 6 * 60 * 60
const KEY_PREFIX = 'ARCAAI-EMR'

class RedisCacheHandler {
  constructor() {}

  async set(key, value, expire = EXPIRED_TIME) {
    // Ensure Redis is initialized
    if (!isInitialized) {
      await initializeRedis()
    }

    let cacheValue = JSON.stringify(value)
    let cacheKey = `${KEY_PREFIX}:${key}`
    if (expire != null) {
      // Set expire
      await cacheConnection.set(cacheKey, cacheValue, 'EX', expire)
    } else {
      await cacheConnection.set(cacheKey, cacheValue)
    }
  }

  async get(key) {
    // Ensure Redis is initialized
    if (!isInitialized) {
      await initializeRedis()
    }

    let cacheKey = `${KEY_PREFIX}:${key}`
    let cacheValue = await cacheConnection.get(cacheKey)
    return JSON.parse(cacheValue)
  }

  async keys(pattern) {
    // Ensure Redis is initialized
    if (!isInitialized) {
      await initializeRedis()
    }

    let keys = []
    let prefix = `${KEY_PREFIX}:${pattern}:`
    let keyPattern = `${KEY_PREFIX}:${pattern}*`
    let redisKeys = await cacheConnection.keys(keyPattern)
    for (let index = 0; index < redisKeys.length; index++) {
      const key = redisKeys[index]
      keys.push(key.slice(prefix.length))
    }
    return keys
  }

  async delete(key) {
    // Ensure Redis is initialized
    if (!isInitialized) {
      await initializeRedis()
    }

    let cacheKey = `${KEY_PREFIX}:${key}`
    return await cacheConnection.del(cacheKey)
  }

  //Clean cache db
  async flush() {
    // Ensure Redis is initialized
    if (!isInitialized) {
      await initializeRedis()
    }

    await cacheConnection.flushAll()
  }
}

module.exports = new RedisCacheHandler()
