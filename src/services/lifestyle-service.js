const cosmosDbContext = require('../cosmosDbContext/comosdb-context');
const logging = require('../common/logging');
const containerId = "LifeStyles"

class LifeStyleService {

    async createLifeStyle(lifeStyle) {
        try {
            var result = await cosmosDbContext.createItem(lifeStyle, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to create lifeStyle", error);
            return null;
        }
    }

    async getLifeStyleByQuery(query, pageSize, continueToken) {
        try {
            var result = await cosmosDbContext.getAllItemQuery(containerId, query, pageSize, continueToken);
            return result;
        } catch (error) {
            logging.logError("unable to get lifeStyle", error);
            return null;
        }
    }

    async getLifeStyeBySourceName(sourceName) {
        try {
            var query = `SELECT * FROM c WHERE c.source = '${sourceName}'`;
            var result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to get lifeStyle", error);
            return null;            
        }
    }

    async patchLifeStyle(id, patchPayload) {
        try {
            var result = await cosmosDbContext.patchItem(id, patchPayload, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to patch lifeStyle", error);
            return null;
        }
    }

    async getLifeStyleByQuery(query) {
        try {
            var result = await cosmosDbContext.queryItems(query, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to get lifeStyle", error);
            return null;
        }
    }

    async deleteLifeStyle(id) {
        try {
            var result = await cosmosDbContext.deleteItem(id, id, containerId);
            return result;
        } catch (error) {
            logging.logError("unable to delete lifeStyle", error);
            return null;
        }
    }

}

module.exports = new LifeStyleService();