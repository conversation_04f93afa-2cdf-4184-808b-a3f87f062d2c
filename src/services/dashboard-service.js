const logging = require('../common/logging')
const dashboardRepository = require('../repositories/dashboard-repository')
const userService = require('./user-service')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const dashboardQuery = require('../queries/dashboard-query')

class DashboardService {
  _getTodayString() {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  async _getUserOrganization(userId) {
    try {
      const user = await userService.getUserById(userId)
      if (!user || !user.organizationId) {
        throw new Error('User organization not found')
      }
      return user.organizationId
    } catch (error) {
      const wrapped = new Error('Failed to get user organization', { cause: error })
      logging.logError(`Failed to get user organization for userId: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async getDashboardData(userId) {
    try {
      logging.logInfo(`Getting dashboard data for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const todayString = this._getTodayString()

      // Get all dashboard metrics in parallel
      const [totalPatients, todaysAppointments, patientQueue, averageWaitingTimeData] = await Promise.all([
        dashboardRepository.getTotalPatients(organizationId),
        dashboardRepository.getTodaysAppointments(organizationId, todayString),
        dashboardRepository.getPatientQueue(organizationId, todayString),
        this.getAverageWaitingTime(userId)
      ])

      return {
        totalPatients,
        todaysAppointments,
        patientQueue,
        averageWaitingTime: averageWaitingTimeData.averageWaitingTime,
        averageWaitingTimeDetails: averageWaitingTimeData,
        organizationId,
        date: todayString
      }
    } catch (error) {
      const wrapped = new Error('Failed to get dashboard data', { cause: error })
      logging.logError(`Failed to get dashboard data for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async getTotalPatients(userId) {
    try {
      logging.logInfo(`Getting total patients for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const result = await dashboardRepository.getTotalPatients(organizationId)
      
      return result
    } catch (error) {
      const wrapped = new Error('Failed to get total patients', { cause: error })
      logging.logError(`Failed to get total patients for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async getTodaysAppointments(userId) {
    try {
      logging.logInfo(`Getting today's appointments for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const todayString = this._getTodayString()
      const result = await dashboardRepository.getTodaysAppointments(organizationId, todayString)
      
      return result
    } catch (error) {
      const wrapped = new Error('Failed to get today\'s appointments', { cause: error })
      logging.logError(`Failed to get today's appointments for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async getPatientQueue(userId) {
    try {
      logging.logInfo(`Getting patient queue for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const todayString = this._getTodayString()
      const result = await dashboardRepository.getPatientQueue(organizationId, todayString)
      
      return result
    } catch (error) {
      const wrapped = new Error('Failed to get patient queue', { cause: error })
      logging.logError(`Failed to get patient queue for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  _calculateWaitingTime(patientArrivalTime, consultationStartTime) {
    try {
      if (!patientArrivalTime || !consultationStartTime) {
        return null
      }

      const arrivalDateTime = new Date(patientArrivalTime)
      const consultationDateTime = new Date(consultationStartTime)

      if (isNaN(arrivalDateTime.getTime()) || isNaN(consultationDateTime.getTime())) {
        return null
      }

      // Calculate difference in minutes
      const diffInMs = consultationDateTime.getTime() - arrivalDateTime.getTime()
      const diffInMinutes = Math.round(diffInMs / (1000 * 60))

      // Return null for negative waiting times (consultation started before arrival)
      return diffInMinutes >= 0 ? diffInMinutes : null
    } catch (error) {
      const wrapped = new Error('Error calculating waiting time', { cause: error })
      logging.logError('Error calculating waiting time:', wrapped)
      return null
    }
  }

  async getAverageWaitingTime(userId) {
    try {
      logging.logInfo(`Getting average waiting time for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const todayString = this._getTodayString()
      
      const waitingTimeData = await dashboardRepository.getAverageWaitingTimeData(organizationId, todayString)
      
      if (!waitingTimeData || waitingTimeData.length === 0) {
        return {
          averageWaitingTime: 0,
          totalCompletedConsultations: 0,
          validCalculations: 0,
          unit: 'minutes',
          date: todayString
        }
      }

      // Calculate waiting times for each completed consultation
      const waitingTimes = []
      
      for (const record of waitingTimeData) {
        const waitingTime = this._calculateWaitingTime(
          record.patientArrivalTime, 
          record.consultationStartTime
        )
        
        if (waitingTime !== null) {
          waitingTimes.push(waitingTime)
        }
      }

      if (waitingTimes.length === 0) {
        return {
          averageWaitingTime: 0,
          totalCompletedConsultations: waitingTimeData.length,
          validCalculations: 0,
          unit: 'minutes',
          date: todayString
        }
      }

      // Calculate average
      const totalWaitingTime = waitingTimes.reduce((sum, time) => sum + time, 0)
      const averageWaitingTime = Math.round(totalWaitingTime / waitingTimes.length)

      return {
        averageWaitingTime,
        totalCompletedConsultations: waitingTimeData.length,
        validCalculations: waitingTimes.length,
        unit: 'minutes',
        date: todayString
      }
    } catch (error) {
      const wrapped = new Error('Failed to get average waiting time', { cause: error })
      logging.logError(`Failed to get average waiting time for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async getUpcomingAppointments(userId, searchParams = {}) {
    try {
      logging.logInfo(`Getting upcoming appointments for user: ${userId}`)
      
      const organizationId = await this._getUserOrganization(userId)
      const now = new Date()
      

      const date = searchParams && typeof searchParams === 'object' && searchParams.date !== undefined ? searchParams.date : searchParams
      const doctorId = searchParams && typeof searchParams === 'object' ? searchParams.doctorId : null
      const patientName = searchParams && typeof searchParams === 'object' ? searchParams.patientName : null
      const patientId = searchParams && typeof searchParams === 'object' ? searchParams.patientId : null
          
          let dateFilter = null
          if (date) {
            const specificDate = new Date(date)
            if (!isNaN(specificDate.getTime())) {
              dateFilter = specificDate.toISOString().split('T')[0]
            }
          }

      const appointments = await dashboardRepository.getUpcomingAppointments(
        organizationId, 
        dateFilter, 
        doctorId
      )
      
      if (!appointments || appointments.length === 0) {
        return []
      }
      
      appointments.sort((a, b) => {
        if (a.date === b.date) {
          return (a.time || '').localeCompare(b.time || '')
        }
        return (a.date || '').localeCompare(b.date || '')
      })

      const patientIds = [...new Set(appointments.map(apt => apt.patientId))]
      const doctorIds = [...new Set(appointments.map(apt => apt.doctorId))]
      
      const [patients, doctors] = await Promise.all([
        this._getPatientsByIds(patientIds),
        this._getDoctorsByIds(doctorIds)
      ])

      const patientMap = new Map(patients.map(p => [p.id, p]))
      
      const doctorMap = new Map()
      for (const doctor of doctors) {
        if (!doctor) continue
        
        const possibleKeys = [
          doctor.id,                    
          doctor.general?.doctorID      
        ].filter(key => key) 
        
        for (const key of possibleKeys) {
          doctorMap.set(key, doctor)
        }
      }
    
      let formattedAppointments = appointments.map(appointment => {
        const patient = patientMap.get(appointment.patientId)
        const doctor = doctorMap.get(appointment.doctorId)
        
        return {
          queueId: appointment.queueId,
          appointmentId: appointment.appointmentId,
          patientId: appointment.patientId,
          patientName: patient?.name || 'N/A',
          doctorId: appointment.doctorId,
          doctorName: doctor?.name || doctor?.general?.fullName || 'N/A',
          date: appointment.date,
          time: appointment.time,
          status: appointment.status,
          type: appointment.type || 'N/A'
        }
      })
      
      if ((patientId && patientId.trim()) || (patientName && patientName.trim())) {
        formattedAppointments = formattedAppointments.filter(appointment => {
          let matches = false
          
          if (patientId && patientId.trim()) {
            matches = matches || appointment.patientId === patientId.trim()
          }
          
          if (patientName && patientName.trim()) {
            const searchTerm = patientName.toLowerCase().trim()
            matches = matches || appointment.patientName.toLowerCase().includes(searchTerm)
          }
          
          return matches
        })
      }
      
      return formattedAppointments
      
    } catch (error) {
      const wrapped = new Error('Failed to get upcoming appointments', { cause: error })
      logging.logError(`Failed to get upcoming appointments for user: ${userId}`, wrapped)
      throw wrapped
    }
  }

  async _getPatientsByIds(patientIds) {
    try {
      if (!patientIds || patientIds.length === 0) return []

      const querySpec = dashboardQuery.getPatientsByIdsQuery(patientIds)
      const patients = querySpec
        ? await cosmosDbContext.queryItems(querySpec, 'PatientProfiles')
        : []
      return Array.isArray(patients) ? patients : []
    } catch (error) {
      logging.logError('Failed to fetch patients by IDs', error)
      return []
    }
  }

  async _getDoctorsByIds(doctorIds) {
    try {
      if (!doctorIds || doctorIds.length === 0) return []
  const bulkQuery = dashboardQuery.getDoctorsByIdsBulkQuery(doctorIds)
  const doctors = bulkQuery ? await cosmosDbContext.queryItems(bulkQuery, 'Doctors') : []

      // Deduplicate doctors by id
      const unique = []
      const seen = new Set()
      for (const d of doctors || []) {
        if (!d || !d.id) continue
        if (!seen.has(d.id)) {
          seen.add(d.id)
          unique.push(d)
        }
      }

  // final unique doctors

      return unique || []
    } catch (error) {
  logging.logError('Failed to fetch doctors by IDs', error)
      return []
    }
  }
}

module.exports = new DashboardService()
