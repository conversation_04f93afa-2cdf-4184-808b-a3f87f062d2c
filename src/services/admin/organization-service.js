const { logger } = require('@azure/identity')
const organizationRepository = require('../../repositories/admin/organization-repository')
const userRepository = require('../../repositories/admin/user-repository')
const bcrypt = require('bcrypt')
const { DefaultRoles } = require('../../common/roles')
const OrganizationModel = require('../../models/organization-model')
const roleRepository = require('../../repositories/admin/role-repository')
const rolePermissionService = require('../role-permission-service')
const userService = require('../user-service')
const auditLogger = require('../../common/audit-logger')
const { logInfo, logError } = require('../../common/logging')
const { generateSecurePassword } = require('../../utils/password-utils')
const b2cService = require('../b2c-service')
const { APIPermissions } = require('../../common/permissions')
const graphService = require('../graph-service')
const NodeCache = require('node-cache')
const {
  organizationSuperAdminPermissionKeys,
  nonAdminSystemRolePermissionKeys,
} = require('../../utils/permission-utils')
const secretManager = require('../secret-manager')
const { AccountType } = require('../../common/constant')
const subscriptionRepository = require('../../repositories/subscription-repository')

class OrganizationService {
  async seedSuperAdmin(superAdmin) {
    logger.info('Checking if Super Admin already exists...')
    const existingSuperAdmin = await userRepository.getUserByRole(
      DefaultRoles.SUPER_ADMIN,
    )

    if (existingSuperAdmin && existingSuperAdmin.length > 0) {
      logger.info('Super Admin already exists. Skipping seeding.')
      return
    }

    logger.info('Seeding Super Admin user...')
    const hashedPassword = await bcrypt.hash(superAdmin.password, 10)
    await userRepository.createSuperAdmin({
      ...superAdmin,
      password: hashedPassword,
    })
    logger.info('Super Admin created successfully.')
  }

  async createOrganization(data) {
    const tenantName = await secretManager.getSecret('TENANT_NAME')
    const organizationData = new OrganizationModel(data)
    const organization = await organizationRepository.createOrganization(
      organizationData,
    )

    // Create admin user object for local database (without password fields)
    const adminUserObj = {
      userRole: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
      name: organizationData.contactPersonName,
      email: organizationData.contactEmail,
      isActive: true,
      userType: DefaultRoles.ORGANIZATION_SUPER_ADMIN,
      accountType: AccountType.NORMAL,
      organizationId: organization.id,
      isOrganizationMainAdmin: true,
      created_by: 'system',
      updated_by: 'system',
    }

    // Remove password-related fields as they're handled by B2C
    delete adminUserObj.password
    delete adminUserObj.resetToken
    delete adminUserObj.resetTokenExpiry

    try {
      const localUser = await userService.addUser(adminUserObj)

      const temporaryPassword = generateSecurePassword()

      const b2cUser = {
        accountEnabled: true, // User is active but must change password
        displayName: organizationData.contactPersonName,
        identities: [
          {
            signInType: 'emailAddress',
            issuer: `${tenantName}.onmicrosoft.com`,
            issuerAssignedId: organizationData.contactEmail,
          },
        ],
        passwordProfile: {
          forceChangePasswordNextSignIn: true, // Force password change on first login
          password: temporaryPassword,
        },
        passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
      }

      const b2cResult = await b2cService.createB2CUser(b2cUser)
      logInfo(
        `B2C admin user created successfully for organization: ${organizationData.name}`,
      )

      // Update local user with B2C user ID
      localUser.b2cUserId = b2cResult.id
      await userService.updateUser(localUser)

      // Send welcome email with temporary password and B2C OAuth login link
      await b2cService.sendWelcomeEmailWithB2CSetup(
        organizationData.contactEmail,
        organizationData.contactPersonName,
        temporaryPassword,
        true, // isAdmin = true for organization admin creation
      )

      await auditLogger.logAction(
        'Organization Created with Admin B2C Integration',
        'system',
        {
          organizationId: organization.id,
          adminUserId: localUser.id,
          b2cUserId: b2cResult.id,
        },
      )

      logInfo(
        `Organization creation completed successfully: ${organizationData.name}`,
      )

      // Create default roles and assign Organization Super Admin role to the user
      const defaultRoles = Object.keys(DefaultRoles)
        .filter((key) => key !== 'SUPER_ADMIN')
        .map((key) => ({
          id: `${organization.id}-${key}`,
          name: DefaultRoles[key],
          organizationId: organization.id,
          isDefault: true,
        }))
      // Convert permission keys to API format
      const convertPermissionsToAPIFormat = (permissionKeys) => {
        return permissionKeys.flatMap((permissionKey) => {
          const permission = APIPermissions.find((p) => p.key === permissionKey)

          if (!permission) {
            logError(`Permission not found: ${permissionKey}`)
            return []
          }

          if (permission.apis.length === 0 && permission.methods.length === 0) {
            return [{ permissionKey }]
          }

          return permission.apis.map((api) => ({
            api,
            methods: permission.methods,
            permissionKey,
          }))
        })
      }

      let organizationSuperAdminRoleId = null

      for (const role of defaultRoles) {
        await roleRepository.createRole(role)

        // Prepare permissions based on role type
        let rolePermissions = []
        if (role.name === DefaultRoles.ORGANIZATION_SUPER_ADMIN) {
          organizationSuperAdminRoleId = role.id
          rolePermissions = convertPermissionsToAPIFormat(
            organizationSuperAdminPermissionKeys,
          )
        } else {
          rolePermissions = convertPermissionsToAPIFormat(
            nonAdminSystemRolePermissionKeys,
          )
        }

        const rolePermissionRecord = {
          id: role.id,
          roleName: role.name,
          organizationId: role.organizationId,
          APIs: rolePermissions,
          created_on: new Date().toISOString(),
          updated_on: new Date().toISOString(),
        }
        await rolePermissionService.addRolePermission(rolePermissionRecord)
      }

      // Update the admin user with the Organization Super Admin role ID
      if (organizationSuperAdminRoleId && localUser) {
        localUser.roleId = organizationSuperAdminRoleId
        await userService.updateUser(localUser)
        logInfo(
          `Organization Super Admin role assigned to user: ${organizationData.contactEmail}`,
        )
      }
    } catch (error) {
      logError(
        `Organization admin creation failed for: ${organizationData.contactEmail}`,
        error,
      )

      // Preserve GraphError and Microsoft Graph API error properties for proper handling upstream
      if (error.name === 'GraphError' || error.code === 'Request_BadRequest') {
        throw error // Re-throw error as-is
      }

      // For other errors, wrap them but preserve important properties
      const wrappedError = new Error(
        `Failed to create organization admin: ${error.message}`,
      )
      wrappedError.code = error.code || 'ORGANIZATION_ADMIN_CREATION_FAILED'
      wrappedError.statusCode = error.statusCode || 500
      wrappedError.originalError = error
      throw wrappedError
    }

    return { message: 'Organization and Admin created successfully' }
  }

  async editOrganization(data) {
    try {
      const existingOrganization =
        await organizationRepository.getOrganizationById(data.id)
      if (!existingOrganization) {
        throw new Error('Organization not found')
      }

      const organizationData = new OrganizationModel(data)
      if (data.status) {
        organizationData.status = data.status
      }

      if (
        existingOrganization.contactPersonName !==
        organizationData.contactPersonName
      ) {
        logInfo(
          `Contact person name changed from "${existingOrganization.contactPersonName}" to "${organizationData.contactPersonName}" for organization ${data.id}`,
        )

        try {
          const users = await userService.getUserByEmail(
            existingOrganization.contactEmail,
          )
          if (users && users.length > 0) {
            const user = users[0]

            user.name = organizationData.contactPersonName
            user.updated_on = new Date().toISOString()

            const a = await userService.updateUser(user)

            logInfo(
              `Updated user name in database for email: ${existingOrganization.contactEmail}`,
            )
            if (user.b2cUserId) {
              try {
                const cache = new NodeCache({ checkperiod: 600 })

                var Token = cache.get(`graphToken`)
                if (
                  !Token ||
                  Token.expiresOn.getTime() < new Date().getTime()
                ) {
                  Token = await graphService.getToken()
                  cache.set('graphToken', {
                    accessToken: Token.accessToken,
                    expiresOn: Token.expiresOn,
                  })
                }

                await graphService.updateUser(
                  Token.accessToken,
                  user.b2cUserId,
                  {
                    name: organizationData.contactPersonName,
                  },
                )

                logInfo(`Updated B2C displayName for user: ${user.b2cUserId}`)
              } catch (b2cError) {
                logError(
                  `Failed to update B2C displayName for user ${user.b2cUserId}:`,
                  b2cError,
                )
                // Don't fail the organization update if B2C update fails
              }
            }
          } else {
            logInfo(
              `No user found with email: ${existingOrganization.contactEmail}`,
            )
          }
        } catch (userUpdateError) {
          logError(
            `Failed to update user for contact person name change:`,
            userUpdateError,
          )
          // Don't fail the organization update if user update fails
        }
      }

      // Handle contact email change
      if (existingOrganization.contactEmail !== organizationData.contactEmail) {
        logInfo(
          `Contact email changed from "${existingOrganization.contactEmail}" to "${organizationData.contactEmail}" for organization ${data.id}`,
        )

        try {
          const users = await userService.getUserByEmail(
            existingOrganization.contactEmail,
          )
          if (users && users.length > 0) {
            const user = users[0]

            // Check if new email already exists
            const existingEmailUsers = await userService.getUserByEmail(
              organizationData.contactEmail,
            )
            if (existingEmailUsers && existingEmailUsers.length > 0) {
              logError(
                `Cannot update organization email: Email ${organizationData.contactEmail} already exists`,
              )
              throw new Error(
                `Email address ${organizationData.contactEmail} already exists`,
              )
            }

            let b2cResult = null
            let temporaryPassword = null

            // Update B2C user if b2cUserId exists
            if (user.b2cUserId) {
              try {
                logInfo(
                  `Updating B2C user email for organization admin: ${user.b2cUserId}`,
                )
                b2cResult = await b2cService.updateUserEmail(
                  user.b2cUserId,
                  organizationData.contactEmail,
                  organizationData.contactPersonName,
                )
                temporaryPassword = b2cResult.temporaryPassword
                logInfo(
                  `B2C user email updated successfully for organization admin`,
                )
              } catch (b2cError) {
                logError(
                  `Failed to update B2C user email for organization admin:`,
                  b2cError,
                )
                throw new Error('Failed to update email in Azure B2C')
              }
            }

            // Update local user record
            user.email = organizationData.contactEmail
            user.name = organizationData.contactPersonName
            user.updated_on = new Date().toISOString()

            // Update b2cUserId if we got a new one from B2C
            if (b2cResult && b2cResult.newB2CUser) {
              user.b2cUserId = b2cResult.newB2CUser.id
            }

            const updatedUser = await userService.updateUser(user)
            if (!updatedUser) {
              logError(
                `Failed to update user in database for organization email change: ${user.id}`,
              )
              throw new Error('Failed to update user in database')
            }

            // Send welcome email to new email address
            if (temporaryPassword) {
              try {
                await b2cService.sendWelcomeEmailWithB2CSetup(
                  organizationData.contactEmail,
                  organizationData.contactPersonName,
                  temporaryPassword,
                  true,
                )
                logInfo(
                  `Welcome email sent to new organization email: ${organizationData.contactEmail}`,
                )
              } catch (emailError) {
                logError(
                  `Failed to send welcome email to: ${organizationData.contactEmail}`,
                  emailError,
                )
              }
            }

            logInfo(
              `Updated user email in database from ${existingOrganization.contactEmail} to ${organizationData.contactEmail}`,
            )
          } else {
            logInfo(
              `No user found with email: ${existingOrganization.contactEmail}`,
            )
          }
        } catch (emailUpdateError) {
          logError(
            `Failed to update user for contact email change:`,
            emailUpdateError,
          )
          throw emailUpdateError
        }
      }

      return organizationRepository.updateOrganization(organizationData)
    } catch (error) {
      logError('Error editing organization:', error)
      throw error
    }
  }

  async listOrganizations(nameFilter, pageSize, pageNumber) {
    const result = await organizationRepository.getAllOrganizations(
      nameFilter,
      pageSize,
      pageNumber,
    )

    return {
      organizations: result.items,
      totalCount: result.totalCount,
      totalPages: result.totalPages,
      currentPage: pageNumber,
    }
  }

  async selectOrganization(superAdminId, organizationId) {
    const organization = await organizationRepository.getOrganizationById(
      organizationId,
    )
    if (!organization || !organization.isActive) {
      throw new Error('Invalid or inactive organization')
    }
    await userRepository.updateUserContext(superAdminId, { organizationId })
    return organization
  }

  async getOrganizationById(id) {
    const organization = await organizationRepository.getOrganizationById(id)
    if (!organization) {
      return null
    }

    let subscriberId = null
    try {
      const subscription =
        await subscriptionRepository.getActiveSubscriptionByOrganization(id)
      if (subscription) {
        if (subscription.subscriptionType === 'clinic') {
          subscriberId = subscription.id
        } else {
          subscriberId = id
        }
      }
    } catch (error) {
      logError('Error fetching subscription for subscriberId:', error)
    }

    return {
      ...organization,
      subscriberId,
    }
  }

  async deactivateOrganizationUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    for (const user of users) {
      user.isActive = false
      await userRepository.updateUser(user.id, user)
    }
  }

  async checkOrganizationLinkedToUsers(organizationId) {
    const users = await userRepository.getUsersByOrganizationId(organizationId)
    return users.length > 0
  }

  async deleteOrganization(organizationId) {
    return await organizationRepository.deleteOrganization(organizationId)
  }
}

module.exports = new OrganizationService()
