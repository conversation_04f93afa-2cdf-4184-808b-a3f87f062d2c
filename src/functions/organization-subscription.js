const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

app.http('organization-subscription', {
  methods: ['GET', 'POST', 'PUT', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.get:
          const action = req.query.get('action')

          switch (action) {
            case 'history':
              return await subscriptionHandler.getOrganizationSubscriptionHistory(
                req,
                decode,
              )
            case 'check-feature':
              return await subscriptionHandler.checkFeatureAccess(req, decode)
            case 'upcoming-renewals':
              return await subscriptionHandler.getUpcomingRenewals(req, decode)
            case 'analytics':
              return await subscriptionHandler.getSubscriptionAnalytics(
                req,
                decode,
              )
            default:
              return await subscriptionHandler.getOrganizationSubscription(
                req,
                decode,
              )
          }

        case HttpMethod.post:
          if (!req.body) {
            return jsonResponse(
              'Missing subscription payload',
              HttpStatusCode.BadRequest,
            )
          }

          const postAction = req.query.get('action')
          if (postAction === 'renew') {
            return await subscriptionHandler.renewSubscription(req, decode)
          }
          if (postAction === 'validate-email') {
            return await subscriptionHandler.validateEmailForSubscription(req, decode)
          }
          if (postAction === 'create-payment-order') {
            return await subscriptionHandler.createSubscriptionPaymentOrder(req, decode)
          }
          if (postAction === 'verify-payment') {
            return await subscriptionHandler.verifySubscriptionPayment(req, decode)
          }

          return await subscriptionHandler.subscribeOrganization(req, decode)

        case HttpMethod.put:
        case HttpMethod.patch:
          if (!req.body) {
            return jsonResponse(
              'Missing subscription payload',
              HttpStatusCode.BadRequest,
            )
          }

          const updateAction = req.query.get('action')
          if (updateAction === 'cancel') {
            return await subscriptionHandler.cancelSubscription(req, decode)
          }

          return await subscriptionHandler.updateOrganizationSubscription(
            req,
            decode,
          )

        default:
          return jsonResponse(
            'Method not allowed',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in organization-subscription function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
