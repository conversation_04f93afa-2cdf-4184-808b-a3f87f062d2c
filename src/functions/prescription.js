const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const prescriptionHandler = require('../handlers/prescription-handler')
const { jsonResponse } = require('../common/helper')

app.http('get-prescriptions', {
  methods: ['GET'],
  route: 'prescriptions',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.getPrescriptions(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching patient prescriptions',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('get-prescription-details', {
  methods: ['GET'],
  route: 'prescriptions/details',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.getPrescriptionDetails(req)
    } catch (err) {
      return jsonResponse(
        'Error fetching prescription details',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('create-update-prescription', {
  methods: ['POST', 'PATCH'],
  route: 'prescriptions',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      return prescriptionHandler.createOrUpdatePrescription(req)
    } catch (err) {
      return jsonResponse(
        'Error creating or updating prescription',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('prescription-search', {
  methods: ['POST'],
  route: 'prescriptions/search',
  authLevel: 'function',
  handler: (req, context) => prescriptionHandler.searchPrescriptions(req),
})
