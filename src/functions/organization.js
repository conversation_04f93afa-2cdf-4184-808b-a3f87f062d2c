const { app } = require('@azure/functions')
const organizationHandler = require('../handlers/admin/organization-handler')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

app.http('list-organizations', {
  methods: ['GET'],
  route: 'list-organizations',
  authLevel: 'anonymous',
  handler: async (req, context) => {
    try {
      return organizationHandler.listOrganizations(req)
    } catch (err) {
      return jsonResponse('Error fetching organizations', 500)
    }
  },
})

app.http('organization', {
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    switch (req.method) {
      case HttpMethod.post:
        try {
          return organizationHandler.createOrganization(req)
        } catch (err) {
          return jsonResponse('Error creating organization', 500)
        }

      case HttpMethod.patch:
        try {
          return organizationHandler.editOrganization(req)
        } catch (err) {
          return jsonResponse('Error editing organization', 500)
        }

      case HttpMethod.get:
        try {
          return organizationHandler.getOrganizationById(req)
        } catch (err) {
          return jsonResponse('Error fetching organization details', 500)
        }

      case HttpMethod.delete:
        try {
          return organizationHandler.deleteOrganization(req)
        } catch (err) {
          return jsonResponse('Error deleting organization', 500)
        }

      default:
        return jsonResponse('Unsupported HTTP method', 405)
    }
  },
})
