const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Organization Plan Management (special hidden plan)
app.http('organization-plan', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.post:
          return await subscriptionHandler.createOrganizationPlan(req, decode)

        case HttpMethod.get:
          return await subscriptionHandler.getOrganizationPlan(req, decode)

        case HttpMethod.patch:
          if (!req.body) {
            return jsonResponse(
              'Missing organization plan payload',
              HttpStatusCode.BadRequest,
            )
          }
          return await subscriptionHandler.updateOrganizationPlan(req, decode)

        default:
          return jsonResponse(
            'Method not allowed. Only GET, POST, and PATCH are supported for organization plan.',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in organization-plan function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
