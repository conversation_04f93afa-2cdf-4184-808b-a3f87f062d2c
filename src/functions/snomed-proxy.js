const axios = require('axios')
const axiosRetry = require('axios-retry').default
const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')

axiosRetry(axios, {
  retries: 3,
  retryDelay: (retryCount, error) => {
    if (error?.response?.status === 429) {
      return 5000 // wait 5 seconds before retry on 429
    }
    return axiosRetry.exponentialDelay(retryCount)
  },
  retryCondition: (error) => {
    return (
      axiosRetry.isNetworkOrIdempotentRequestError(error) ||
      error.response?.status === 429
    )
  },
})

app.http('snomed-proxy', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'snomed-proxy',
  handler: async (request, context) => {
    const term = request.query.get('term')
    if (!term) {
      return jsonResponse({ error: 'Missing "term" parameter' }, 400)
    }

    try {
      const response = await axios.get(
        'https://browser.ihtsdotools.org/snowstorm/snomed-ct/browser/MAIN/descriptions',
        {
          params: { term },
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            'User-Agent':
              'Mozilla/5.0 (compatible; MyApp/1.0; +http://myappdomain.com)',
          },
          timeout: 10000,
        },
      )

      return jsonResponse(response.data)
    } catch (error) {
      console.error('Error in Axios request:', error.message)
      const statusCode = error.response?.status || 500
      const message =
        statusCode === 429
          ? 'Rate limit exceeded, please try again later.'
          : 'Error fetching data'

      return jsonResponse({ error: message }, statusCode)
    }
  },
})
