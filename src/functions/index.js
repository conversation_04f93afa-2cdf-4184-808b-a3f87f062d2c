// CRITICAL: Initialize OpenTelemetry FIRST (before any other imports)
const {
  initializeTracing,
  setupTracingPreInvocation,
  setupTracingPostInvocation,
  addAuthToSpan,
} = require('../telemetry')
initializeTracing()

// CRITICAL: Initialize Key Vault secrets before Azure Functions runtime starts
require('../config/early-init')

const { app } = require('@azure/functions')
const AuthMessage = require('../common/auth-message')
const { doValidate } = require('../common/user-validation')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const finalizePatientRecordsCron = require('../tasks/finalize-patient-history-cron')
const initializeSubscriptionCrons = require('../tasks/subscription-cron')
const secretManager = require('../services/secret-manager')
const { logInfo, logError } = require('../common/logging')

// Load timer functions without storing secrets in process.env
finalizePatientRecordsCron()
initializeSubscriptionCrons()
app.setup({
  enableHttpStream: false,
})

// Initialize timer functions

app.hook.appStart(async (context) => {
  console.log('EMR Function start...')

  try {
    // CRITICAL: Set AzureWebJobsStorage as environment variable for timer functions
    logInfo('Setting up AzureWebJobsStorage for timer functions...')
    const azureWebJobsStorage = await secretManager.getSecret(
      'AzureWebJobsStorage',
    )
    if (azureWebJobsStorage) {
      process.env.AzureWebJobsStorage = azureWebJobsStorage
      logInfo('AzureWebJobsStorage set successfully for timer functions')
    } else {
      logError(
        'CRITICAL: AzureWebJobsStorage not found - timer functions will fail!',
      )
    }

    // Preload critical secrets on application startup
    logInfo('Preloading critical secrets from Key Vault...')

    const criticalSecrets = [
      'COSMOS_DB_CONNECTIONSTRING',
      'CLIENT_ID',
      'CLIENT_SECRET',
      'TENANT_ID',
      'JWT_SECRET',
      'EMAIL_USER',
      'EMAIL_PASSWORD',
      'TENANT_NAME',
      'BASE_URL',
      'BASE_ADMIN_URL',
    ]

    await secretManager.preloadSecrets(criticalSecrets)
    logInfo('Critical secrets preloaded successfully')
  } catch (error) {
    logError('Failed to preload secrets during app startup:', error)
    // Don't fail the app startup, just log the error
  }
})

app.hook.appTerminate((context) => {
  console.log('EMR Function stop...')
})

app.hook.preInvocation(async (context) => {
  var req = context.inputs[0]
  // Skip auth for non-HTTP functions (like timers)
  if (!req || !req.url) {
    return
  }

  // Setup tracing for this request
  setupTracingPreInvocation(context, req)

  // Skip auth for public endpoints
  const url = req.url.toLowerCase()

  // Public endpoints that don't require authentication
  const publicEndpoints = ['/subscription/quote', '/clinic/subscription']

  // Check for public endpoints
  if (publicEndpoints.some((endpoint) => url.includes(endpoint))) {
    return
  }

  // Allow GET requests for subscription plans (public catalog)
  if (url.includes('/subscription-plan') && req.method === 'GET') {
    return
  }

  // Allow GET requests for organization plans (public catalog)
  if (url.includes('/organization-plan') && req.method === 'GET') {
    return
  }

  // Allow GET requests for organization subscriptions (public lookup)
  if (url.includes('/organization-subscription') && req.method === 'GET') {
    return
  }
  if (url.includes('/list-organizations') && req.method === 'GET') {
    return
  }

  // Allow GET requests for roles (public catalog)
  if (url.includes('/list-roles') && req.method === 'GET') {
    return
  }

  if (url.includes('/payments/create-order') && req.method === 'POST') {
    try {
      const body = await req.json()

      context.invocationContext.extraInputs.set('requestBody', body)

      if (body.paymentType === 'subscription' && body.subscriberEmail) {
        return // Allow public access for clinic subscription payments
      }
      // If not a public clinic payment, continue to auth validation
    } catch (error) {
      // If body parsing fails, continue to auth validation
    }
  }

  // Allow verification of clinic subscription payments (public payment flow)
  if (url.includes('/payments/verify') && req.method === 'POST') {
    // Payment verification should be public to complete the clinic payment flow
    return
  }

  const auth = await doValidate(req)
  if (auth.message != AuthMessage.SUCCESS) {
    addAuthToSpan(false, auth.message, null)
    context.functionHandler = (...args) => {
      return jsonResponse(auth.message, HttpStatusCode.Unauthorized)
    }
  } else {
    addAuthToSpan(true, null, auth.decode)
    context.invocationContext.extraInputs.set('decode', auth.decode)
  }
})

// Post-invocation hook to end the tracing span
app.hook.postInvocation(async (context) => {
  setupTracingPostInvocation(context)
})
