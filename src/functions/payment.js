const { app } = require('@azure/functions')
const paymentHandler = require('../handlers/payment-handler')

app.http('create-payment-order', {
  methods: ['POST'],
  route: 'payments/create-order',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Creating payment order')
    const decode = context.extraInputs.get('decode')
    const requestBody = context.extraInputs.get('requestBody')
    return await paymentHandler.createOrder(req, decode, requestBody)
  },
})

app.http('verify-payment', {
  methods: ['POST'],
  route: 'payments/verify',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Verifying payment')
    return await paymentHandler.verifyPayment(req)
  },
})

app.http('payment-webhook', {
  methods: ['POST'],
  route: 'payments/webhook',
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log('Processing payment webhook')
    return await paymentHandler.handleWebhook(req)
  },
})

app.http('payment-details', {
  methods: ['GET'],
  route: 'payments/details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment details')
    return await paymentHandler.getPaymentDetails(req)
  },
})

app.http('organization-payments', {
  methods: ['GET'],
  route: 'payments/organization',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching organization payments')
    return await paymentHandler.getOrganizationPayments(req)
  },
})

app.http('payment-statistics', {
  methods: ['GET'],
  route: 'payments/stats',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment statistics')
    return await paymentHandler.getPaymentStatistics(req)
  },
})

app.http('search-payments', {
  methods: ['GET'],
  route: 'payments/search',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Searching payments')
    return await paymentHandler.searchPayments(req)
  },
})

app.http('cleanup-expired-tokens', {
  methods: ['POST'],
  route: 'payments/tokens/cleanup',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Cleaning up expired payment tokens')
    return await paymentHandler.cleanupExpiredTokens(req)
  },
})

app.http('validate-payment-tokens', {
  methods: ['POST'],
  route: 'payments/tokens/validate',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Validating payment tokens')
    return await paymentHandler.validateTokens(req)
  },
})
