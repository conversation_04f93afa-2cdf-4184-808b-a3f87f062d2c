/**
 * Azure Functions for ABDM (ABHA) operations
 * Handles ABHA number generation, verification, and management
 */

const { app } = require('@azure/functions')
const abdmHandler = require('../handlers/abdm-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// ABHA Number Generation - Initiate by Aadhaar
app.http('abdm-initiate-aadhaar', {
  methods: ['POST'],
  route: 'abdm/initiate/aadhaar',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Initiating ABHA creation by <PERSON>ad<PERSON><PERSON>')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.initiateAbhaCreationByAadhaar(req)
  },
})

// ABHA Number Generation - Verify OTP
app.http('abdm-verify-otp', {
  methods: ['POST'],
  route: 'abdm/verify-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying OTP for ABHA creation')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.verifyOtpForAbhaCreation(req)
  },
})

// ABHA Details - Get by ABHA Number
app.http('abdm-details-by-number', {
  methods: ['POST'],
  route: 'abdm/details/by-number',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Fetching ABHA details by number')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.getAbhaDetailsByNumber(req)
  },
})

// ABHA Details - Get by Mobile Number
app.http('abdm-details-by-mobile', {
  methods: ['POST'],
  route: 'abdm/details/by-mobile',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Fetching ABHA details by mobile')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.getAbhaDetailsByMobile(req)
  },
})

// ABHA Number Verification
app.http('abdm-verify-number', {
  methods: ['POST'],
  route: 'abdm/verify-number',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying ABHA number')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.verifyAbhaNumber(req)
  },
})

// Resend OTP
app.http('abdm-resend-otp', {
  methods: ['POST'],
  route: 'abdm/resend-otp',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Resending OTP')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.resendOtp(req)
  },
})

// Verify OTP and fetch ABHA details by number
app.http('abdm-verify-otp-fetch-details', {
  methods: ['POST'],
  route: 'abdm/verify-otp-fetch-details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying OTP and fetching ABHA details by number')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.verifyOtpAndFetchAbhaDetailsByNumber(req)
  },
})

// Verify OTP and fetch ABHA details by mobile
app.http('abdm-verify-otp-fetch-details-by-mobile', {
  methods: ['POST'],
  route: 'abdm/verify-otp-fetch-details-by-mobile',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('ABDM: Verifying OTP and fetching ABHA details by mobile')

    if (!req.body) {
      return jsonResponse('Missing request payload', HttpStatusCode.BadRequest)
    }

    return await abdmHandler.verifyOtpAndFetchAbhaDetailsByMobile(req)
  },
})

app.http('abdm-webhook', {
  methods: ['GET', 'POST'],
  route: 'abdm/webhook',
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log('ABDM Webhook called:', req.method, req.url)
    return await abdmHandler.handleWebhook(req, context)
  },
})
