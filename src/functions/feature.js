const { app } = require('@azure/functions')
const featureHandler = require('../handlers/feature-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Feature Management
app.http('feature', {
  methods: ['GET', 'POST', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.get:
          const action = req.query.get('action')
          const id = req.query.get('id')

          if (action === 'search') {
            return await featureHandler.searchFeatures(req, decode)
          } else if (action === 'permission-keys') {
            return await featureHandler.getAllPermissionKeys(req, decode)
          } else if (action === 'by-permission') {
            return await featureHandler.getFeaturesByPermissionKey(req, decode)
          } else if (id) {
            return await featureHandler.getFeature(req, decode)
          } else {
            return await featureHandler.getAllFeatures(req, decode)
          }

        case HttpMethod.post:
          if (!req.body) {
            return jsonResponse('Missing feature payload', HttpStatusCode.BadRequest)
          }
          return await featureHandler.createFeature(req, decode)

        case HttpMethod.patch:
          if (!req.body) {
            return jsonResponse('Missing feature payload', HttpStatusCode.BadRequest)
          }

          const patchAction = req.query.get('action')
          if (patchAction === 'deactivate') {
            return await featureHandler.deactivateFeature(req, decode)
          }

          return await featureHandler.updateFeature(req, decode)

        case HttpMethod.delete:
          return await featureHandler.deleteFeature(req, decode)

        default:
          return jsonResponse('Method not allowed', HttpStatusCode.MethodNotAllowed)
      }
    } catch (error) {
      context.error('Error in feature function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
