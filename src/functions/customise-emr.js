const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const customiseEmrHandler = require('../handlers/customise-emr-handler');

app.http('customise-emr', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'anonymous',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');
        const sourceName = request.query.get("source_name");
        switch (request.method) {
            case HttpMethod.get:
                if (!sourceName) {
                    return jsonResponse(`Missing sourceName`, HttpStatusCode.BadRequest)
                }
                var res = await customiseEmrHandler.getCustomiseEmr(sourceName);
                return jsonResponse(res);

            case HttpMethod.post:
                const payload = await request.json()
                if (!payload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await customiseEmrHandler.createCustomiseEmr(payload, decode.oid)
                return jsonResponse(data)

            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await customiseEmrHandler.patchCustomiseEmr(id, patchPayload, decode.oid)
                return jsonResponse(data);

            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
