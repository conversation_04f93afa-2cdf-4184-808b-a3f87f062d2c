const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Subscriber Management (Combined Organization + Subscription)
app.http('subscriber', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.post:
          // Create new subscriber (organization + subscription)
          if (!req.body) {
            return jsonResponse(
              'Missing subscriber payload',
              HttpStatusCode.BadRequest,
            )
          }
          return await subscriptionHandler.createSubscriber(req, decode)

        case HttpMethod.get:
          const subscriberId = req.query.get('id')

          if (subscriberId) {
            // Get single subscriber by ID
            return await subscriptionHandler.getSubscriberById(req, decode)
          } else {
            // Get all subscribers with optional filters
            return await subscriptionHandler.getAllSubscribers(req, decode)
          }

        case HttpMethod.patch:
          // Update subscriber
          if (!req.body) {
            return jsonResponse(
              'Missing subscriber update payload',
              HttpStatusCode.BadRequest,
            )
          }
          return await subscriptionHandler.updateSubscriber(req, decode)

        default:
          return jsonResponse(
            'Method not allowed. Only GET, POST, and PATCH are supported for subscriber.',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in subscriber function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
