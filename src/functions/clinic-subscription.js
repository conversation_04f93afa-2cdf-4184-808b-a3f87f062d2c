const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Clinic Subscription Flow (for individual clinics, not organizations)
app.http('clinic-subscription', {
  methods: ['POST'],
  authLevel: 'anonymous', // Public endpoint for clinic signup
  route: 'clinic/subscription',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)

    try {
      if (req.method === HttpMethod.post) {
        if (!req.body) {
          return jsonResponse(
            'Missing subscription payload',
            HttpStatusCode.BadRequest,
          )
        }

        const action = req.query.get('action')

        switch (action) {
          case 'validate-email':
            return await subscriptionHandler.validateEmailForSubscription(req, null)
          case 'start-trial':
            return await subscriptionHandler.startClinicTrial(req)
          case 'subscribe':
            return await subscriptionHandler.subscribeClinic(req)
          case 'change-plan':
            return await subscriptionHandler.changeClinicPlan(req)
          default:
            return jsonResponse(
              'Invalid action. Use action=validate-email, action=start-trial, action=subscribe, or action=change-plan',
              HttpStatusCode.BadRequest,
            )
        }
      }

      return jsonResponse(
        'Method not allowed. Only POST is supported.',
        HttpStatusCode.MethodNotAllowed,
      )
    } catch (error) {
      context.error('Error in clinic-subscription function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
