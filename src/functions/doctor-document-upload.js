const { app } = require('@azure/functions')
const multipart = require('parse-multipart')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { uploadBlob } = require('../utils/blobUtils')

app.http('user-document-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'user/document/upload',
  handler: async (request, context) => {
    context.log(`Http function processed request for url "${request.url}"`)
    const decode = context.extraInputs.get('decode')
    const data = await request.formData()
    const file = data.get('file')
    const fileName = file.name
    const filesize = file.size
    if (filesize > 209715200) {
      return jsonResponse(
        `File size should be less than 200MB`,
        HttpStatusCode.BadRequest,
      )
    }
    const buffer = await file.arrayBuffer()
    const doc_type = data.get('doc_type')
    const userId = data.get('userId')
    if (!doc_type || !userId) {
      return jsonResponse(
        `Missing doc_type or userId`,
        HttpStatusCode.BadRequest,
      )
    }
    const containerName = 'file-uploads'
    const blobName = `user/${userId}/${doc_type}/${fileName}`
    const blobUrl = await uploadBlob(containerName, blobName, buffer)
    return jsonResponse({ blobUrl })
  },
})
