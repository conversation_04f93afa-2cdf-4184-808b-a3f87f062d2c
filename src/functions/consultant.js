const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const patientHandler = require('../handlers/patient-handler');

app.http('consultant', {
    methods: ['GET', 'POST', 'PUT'],
    authLevel: 'function',
    route: "patient/consulting",
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        const decode = context.extraInputs.get('decode');

        const patientId = req.query.get("patientId"),
            date = req.query.get("date");

        switch (req.method) {
            case HttpMethod.get:
                if (!patientId || !date) {
                    return jsonResponse(`Missing patientId or date`, HttpStatusCode.BadRequest)
                }
                var res = await patientHandler.GetPateintConsultant(patientId, date);
                return jsonResponse(res);
            case HttpMethod.post:
            case HttpMethod.put:
                const payload = await req.json()
                if (!patientId) {
                    return jsonResponse(`Missing patientId`, HttpStatusCode.BadRequest)
                }
                if (req.method == HttpMethod.post) {
                    var data = await patientHandler.CreatePatientConsultant(patientId, payload, decode.oid)
                    return jsonResponse(data)
                } else {
                    if (patientId != payload.patientId) {
                        return jsonResponse(`patientId is not valid`, HttpStatusCode.BadRequest)
                    }
                    var data = await patientHandler.UpdatePatientConsultant(payload, decode.oid)
                    return jsonResponse(data);
                }
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
