const { app } = require('@azure/functions')
const patientLifestylePhysicalActivityHandler = require('../handlers/patient-lifestyle-physical-activity-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('physical-activity-dashboard', {
  methods: ['GET'],
  route: 'physical-activity/dashboard',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(
      `Complete Physical Activity Dashboard processed request for url "${req.url}"`,
    )

    try {
      const data =
        await patientLifestylePhysicalActivityHandler.getCompleteDashboard(req)
      return data
    } catch (err) {
      context.log('Error fetching complete dashboard:', err)
      return jsonResponse(
        'Error fetching complete dashboard',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
