const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const summaryHandler = require('../handlers/summary-handler')
const helper = require('../common/helper')
const consultationRecordHistoryService = require('../services/consultation-record-history-service')

app.http('summary', {
  methods: ['POST'],
  authLevel: 'anonymous',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)

    try {
      const body = await req.json()
      const { userId, patientId, text } = body

      if (!text || text.trim() === '') {
        return jsonResponse(`Missing text`, HttpStatusCode.BadRequest)
      }

      if (!userId || !patientId) {
        return jsonResponse(
          `Missing userId or patientId`,
          HttpStatusCode.BadRequest,
        )
      }

      context.log(`Processing text of length: ${text.length}`)

      const conversation = await summaryHandler.identifySpeaker(text)
      if (!conversation) {
        context.log('Failed to identify speakers, using fallback')
        return jsonResponse({
          conversation: [{ speaker: 'patient', message: text }],
          summary: helper.getDefaultSummary(),
        })
      }

      context.log(`Raw conversation response length: ${conversation.length}`)

      // Parse conversation with enhanced error handling first
      let objConversation = helper.parseJSON(conversation)
      if (
        !objConversation ||
        objConversation === '' ||
        !Array.isArray(objConversation)
      ) {
        context.log('Failed to parse conversation JSON, using fallback')
        objConversation = [{ speaker: 'patient', message: text }]
      }

      context.log(`Parsed conversation with ${objConversation.length} messages`)

      // Generate summary using the parsed conversation array
      const summary = await summaryHandler.sumnmaryConversation(
        JSON.stringify(objConversation),
      )
      if (!summary) {
        context.log('Failed to generate summary, using default')
      }

      // Parse summary with fallback
      let objsummary = helper.parseJSON(summary)
      if (!objsummary || objsummary === '') {
        context.log('Failed to parse summary JSON, using default')
        objsummary = null
      }

      if (!objsummary || !helper.validateMedicalRecord(objsummary)) {
        context.log('Summary validation failed, using default structure')
        objsummary = helper.getDefaultSummary()
      }

      // Clean up empty HTML lists and convert them to empty strings
      objsummary = helper.cleanEmptyHtmlLists(objsummary)

      // Store the payload for retry purposes
      try {
        await consultationRecordHistoryService.createConsultationRecordHistory({
          transcript: text,
          userId,
          patientId,
          processedAt: new Date().toISOString(),
          status: 'completed',
        })
        context.log('Successfully stored consultation record history')
      } catch (historyError) {
        context.log(
          'Failed to store consultation record history:',
          historyError,
        )
      }

      return jsonResponse({
        conversation: objConversation,
        summary: objsummary,
      })
    } catch (error) {
      context.log('Error in summary function:', error)

      try {
        const body = await req.json()
        const { userId, patientId, text } = body

        if (userId && patientId && text) {
          await consultationRecordHistoryService.createConsultationRecordHistory(
            {
              transcript: text,
              userId,
              patientId,
              processedAt: new Date().toISOString(),
              status: 'failed',
              errorMessage: error.message,
            },
          )
          context.log('Stored failed consultation record for retry')
        }
      } catch (storeError) {
        context.log('Failed to store error record:', storeError)
      }

      return jsonResponse(
        {
          error: 'Internal server error processing summary',
          details: error.message,
        },
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('retry-summary', {
  methods: ['POST'],
  authLevel: 'anonymous',
  route: 'retry/summary',
  handler: async (req, context) => {
    context.log(
      `Http function processed retry summary request for url "${req.url}"`,
    )

    try {
      const body = await req.json()
      const { userId, patientId } = body

      if (!userId || !patientId) {
        return jsonResponse(
          `Missing userId or patientId`,
          HttpStatusCode.BadRequest,
        )
      }

      const latestRecord =
        await consultationRecordHistoryService.getLatestConsultationRecord(
          userId,
          patientId,
        )

      if (!latestRecord) {
        return jsonResponse(
          `No consultation record found for userId: ${userId} and patientId: ${patientId}`,
          HttpStatusCode.NotFound,
        )
      }

      context.log(`Retrying summary processing for record: ${latestRecord.id}`)

      const conversation = await summaryHandler.identifySpeaker(
        latestRecord.transcript,
      )
      if (!conversation) {
        context.log('Failed to identify speakers, using fallback')
        return jsonResponse({
          conversation: [
            { speaker: 'patient', message: latestRecord.transcript },
          ],
          summary: helper.getDefaultSummary(),
        })
      }

      context.log(`Raw conversation response length: ${conversation.length}`)

      let objConversation = helper.parseJSON(conversation)
      if (
        !objConversation ||
        objConversation === '' ||
        !Array.isArray(objConversation)
      ) {
        context.log('Failed to parse conversation JSON, using fallback')
        objConversation = [
          { speaker: 'patient', message: latestRecord.transcript },
        ]
      }

      context.log(`Parsed conversation with ${objConversation.length} messages`)

      const summary = await summaryHandler.sumnmaryConversation(
        JSON.stringify(objConversation),
      )
      if (!summary) {
        context.log('Failed to generate summary, using default')
      }

      let objsummary = helper.parseJSON(summary)
      if (!objsummary || objsummary === '') {
        context.log('Failed to parse summary JSON, using default')
        objsummary = null
      }

      if (!objsummary || !helper.validateMedicalRecord(objsummary)) {
        context.log('Summary validation failed, using default structure')
        objsummary = helper.getDefaultSummary()
      }

      // Clean up empty HTML lists and convert them to empty strings
      objsummary = helper.cleanEmptyHtmlLists(objsummary)

      try {
        await consultationRecordHistoryService.updateConsultationRecordHistory(
          latestRecord.id,
          {
            status: 'completed',
            processedAt: new Date().toISOString(),
          },
        )
        context.log('Successfully updated consultation record history status')
      } catch (updateError) {
        context.log(
          'Failed to update consultation record history:',
          updateError,
        )
      }

      return jsonResponse({
        conversation: objConversation,
        summary: objsummary,
        retryInfo: {
          originalRecordId: latestRecord.id,
          originalCreatedAt: latestRecord.created_on,
        },
      })
    } catch (error) {
      context.log('Error in retry summary function:', error)
      return jsonResponse(
        {
          error: 'Internal server error processing retry summary',
          details: error.message,
        },
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
