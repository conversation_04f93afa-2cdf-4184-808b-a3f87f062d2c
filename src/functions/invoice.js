const { app } = require('@azure/functions')
const invoiceHandler = require('../handlers/invoice-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Telemetry Config Debug Endpoint
app.http('invoice-telemetry-config', {
  methods: ['GET'],
  route: 'invoice/telemetry-config',
  authLevel: 'function',
  handler: async (req, context) => {
    const grafanaEndpoint = process.env.GRAFANA_OTLP_ENDPOINT
    const grafanaToken = process.env.GRAFANA_OTLP_TOKEN
    const serviceName = process.env.OTEL_SERVICE_NAME
    const environment = process.env.environment
    const otelDebug = process.env.OTEL_DEBUG

    // Mask token for security (show first 10 and last 5 chars)
    let maskedToken = 'NOT SET'
    if (grafanaToken) {
      if (grafanaToken.length > 20) {
        maskedToken = `${grafanaToken.substring(0, 10)}...${grafanaToken.substring(grafanaToken.length - 5)}`
      } else {
        maskedToken = '***SET BUT SHORT***'
      }
    }

    const config = {
      telemetryStatus: grafanaToken ? 'ENABLED' : 'DISABLED',
      serviceName: serviceName || 'emr-microservice-dev (default)',
      environment: environment || 'local (default)',
      grafanaEndpoint: grafanaEndpoint || 'https://otlp-gateway-prod-us-central-0.grafana.net/otlp (default)',
      grafanaTokenSet: !!grafanaToken,
      grafanaTokenPreview: maskedToken,
      otelDebug: otelDebug || 'false (default)',
      nodeVersion: process.version,
      websiteInstanceId: process.env.WEBSITE_INSTANCE_ID || 'local',
      regionName: process.env.REGION_NAME || 'unknown',
      timestamp: new Date().toISOString(),
    }

    return jsonResponse(config, 200)
  },
})

// Invoice Management (Payment Invoices)
app.http('invoice', {
  methods: ['GET'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.get:
          const invoiceId = req.query.get('id')

          if (invoiceId) {
            return await invoiceHandler.getInvoiceById(req, decode)
          } else {
            return await invoiceHandler.getAllInvoices(req, decode)
          }

        default:
          return jsonResponse(
            'Method not allowed. Only GET is supported for invoice.',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in invoice function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

// Patient Profile with Consultations (for Super Admin)
app.http('invoice-patient-profile', {
  methods: ['GET'],
  route: 'invoice/patient-profile',
  authLevel: 'function',
  handler: async (req, context) => {
    const decode = context.extraInputs.get('decode')
    const patientId = req.query.get('patientId')
    const organizationId = req.query.get('organizationId')

    if (!patientId) {
      return jsonResponse('Missing patientId', HttpStatusCode.BadRequest)
    }

    if (!organizationId) {
      return jsonResponse('Missing organizationId', HttpStatusCode.BadRequest)
    }

    if (!decode || !decode.oid) {
      return jsonResponse('Unauthorized - Invalid token', HttpStatusCode.Unauthorized)
    }

    try {
      const data = await invoiceHandler.getPatientProfileWithConsultations(
        patientId,
        organizationId,
        decode.oid,
      )
      return jsonResponse(data)
    } catch (err) {
      context.log.error('Error fetching patient profile with consultations:', err.message)
      return jsonResponse(
        { error: err.message || 'Error fetching patient profile with consultations' },
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
