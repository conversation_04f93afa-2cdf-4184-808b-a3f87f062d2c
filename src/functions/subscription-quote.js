const { app } = require('@azure/functions')
const subscriptionQuoteHandler = require('../handlers/subscription-quote-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')

app.http('subscription-quote', {
  methods: ['POST'],
  authLevel: 'anonymous', // Public endpoint - no authentication required
  route: 'subscription/quote',
  handler: async (req, context) => {
    try {
      context.log(`Subscription quote request received for url "${req.url}"`)

      if (!req.body) {
        return jsonResponse('Request body is required', HttpStatusCode.BadRequest)
      }

      const data = await req.json()

      // Validate required fields
      const requiredFields = ['name', 'email', 'phoneNumber', 'organizationName', 'designation']
      const missingFields = requiredFields.filter(field => !data[field])

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest
        )
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        return jsonResponse('Invalid email format', HttpStatusCode.BadRequest)
      }

      const result = await subscriptionQuoteHandler.submitQuoteRequest(data)

      if (!result.success) {
        return jsonResponse(
          result.message || 'Failed to submit quote request',
          HttpStatusCode.InternalServerError
        )
      }

      return jsonResponse(
        {
          message: 'Your quote request has been submitted successfully. We will get back to you soon.',
          success: true
        },
        HttpStatusCode.Ok
      )
    } catch (error) {
      context.log.error('Error processing subscription quote request:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError
      )
    }
  }
})
