const { app } = require('@azure/functions')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const lifestyleAmbientHandler = require('../handlers/lifestyle-ambient-handler')
const lifestyleRecordHistoryService = require('../services/lifestyle-record-history-service')

app.http('lifestyle-ambient-listening', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'lifestyle/ambient-listening',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)

    try {
      const body = await req.json()
      const { transcript, source, userId, patientId } = body

      if (!transcript || transcript.trim() === '') {
        return jsonResponse(
          'Missing or empty transcript',
          HttpStatusCode.BadRequest,
        )
      }

      if (!source || source.trim() === '') {
        return jsonResponse(
          'Missing or empty source',
          HttpStatusCode.BadRequest,
        )
      }

      if (!userId || !patientId) {
        return jsonResponse(
          `Missing userId or patientId`,
          HttpStatusCode.BadRequest,
        )
      }

      context.log(
        `Processing lifestyle ambient listening for source: ${source}`,
      )

      const result =
        await lifestyleAmbientHandler.processLifestyleAmbientListening(
          transcript,
          source,
        )

      if (!result) {
        return jsonResponse(
          'Failed to process lifestyle ambient listening',
          HttpStatusCode.InternalServerError,
        )
      }

      // Store the payload for retry purposes
      try {
        await lifestyleRecordHistoryService.createLifestyleRecordHistory({
          transcript,
          source,
          userId,
          patientId,
          processedAt: new Date().toISOString(),
          status: 'completed',
        })
        context.log('Successfully stored lifestyle record history')
      } catch (historyError) {
        context.log('Failed to store lifestyle record history:', historyError)
      }

      return jsonResponse(
        {
          conversation: result.conversation || [],
          summary: result.summary || {},
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      context.log('Error in lifestyle ambient listening handler:', error)

      // Try to store failed record for retry purposes
      try {
        const body = await req.json()
        const { transcript, source, userId, patientId } = body

        if (userId && patientId && transcript && source) {
          await lifestyleRecordHistoryService.createLifestyleRecordHistory({
            transcript,
            source,
            userId,
            patientId,
            processedAt: new Date().toISOString(),
            status: 'failed',
            errorMessage: error.message,
          })
          context.log('Stored failed lifestyle record for retry')
        }
      } catch (storeError) {
        context.log('Failed to store error record:', storeError)
      }

      if (error.name === 'SyntaxError') {
        return jsonResponse(
          'Invalid JSON in request body',
          HttpStatusCode.BadRequest,
        )
      }

      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})

app.http('retry-lifestyle-ambient-listening', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'retry/lifestyle/ambient-listening',
  handler: async (req, context) => {
    context.log(
      `Http function processed retry lifestyle ambient listening request for url "${req.url}"`,
    )

    try {
      const body = await req.json()
      const { userId, patientId, source } = body

      if (!userId || !patientId || !source) {
        return jsonResponse(
          `Missing userId, patientId, or source`,
          HttpStatusCode.BadRequest,
        )
      }

      const latestRecord =
        await lifestyleRecordHistoryService.getLatestLifestyleRecord(
          source,
          patientId,
          userId,
        )

      if (!latestRecord) {
        return jsonResponse(
          `No lifestyle record found for source: ${source}, userId: ${userId} and patientId: ${patientId}`,
          HttpStatusCode.NotFound,
        )
      }

      context.log(
        `Retrying lifestyle ambient listening processing for record: ${latestRecord.id}`,
      )

      const result =
        await lifestyleAmbientHandler.processLifestyleAmbientListening(
          latestRecord.transcript,
          latestRecord.source,
        )

      if (!result) {
        return jsonResponse(
          'Failed to process lifestyle ambient listening retry',
          HttpStatusCode.InternalServerError,
        )
      }

      // Update the record status to completed
      try {
        await lifestyleRecordHistoryService.updateLifestyleRecordHistory(
          latestRecord.id,
          {
            status: 'completed',
            processedAt: new Date().toISOString(),
          },
        )
        context.log('Successfully updated lifestyle record history status')
      } catch (updateError) {
        context.log('Failed to update lifestyle record history:', updateError)
      }

      return jsonResponse(
        {
          conversation: result.conversation || [],
          summary: result.summary || {},
          retryInfo: {
            originalRecordId: latestRecord.id,
            originalCreatedAt: latestRecord.created_on,
            source: latestRecord.source,
          },
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      context.log('Error in retry lifestyle ambient listening handler:', error)
      return jsonResponse(
        {
          error:
            'Internal server error processing retry lifestyle ambient listening',
          details: error.message,
        },
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
