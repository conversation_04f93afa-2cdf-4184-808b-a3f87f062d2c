const { app } = require('@azure/functions');
const demographicsHandler = require('../handlers/demographics-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('patient-demographics', {
    methods: ['GET', 'POST', 'PUT'],
    authLevel: 'function',
    route: 'patient/lifestyle/demographics',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');

        try {
            switch (request.method) {
                case HttpMethod.get:
                    const patientId = request.query.get("patientId");
                    if (!patientId) {
                        return jsonResponse({
                            error: {
                                code: "MISSING_PATIENT_ID",
                                message: "Missing required parameter: patientId"
                            }
                        }, HttpStatusCode.BadRequest);
                    }

                    const demographics = await demographicsHandler.getDemographics(patientId);
                    if (!demographics) {
                        return jsonResponse({}, HttpStatusCode.Ok);
                    }

                    return jsonResponse(demographics, HttpStatusCode.Ok);

                case HttpMethod.post:
                    const createPatientId = request.query.get("patientId");
                    if (!createPatientId) {
                        return jsonResponse({
                            error: {
                                code: "MISSING_PATIENT_ID",
                                message: "Missing required parameter: patientId"
                            }
                        }, HttpStatusCode.BadRequest);
                    }

                    const createPayload = await request.json();
                    if (!createPayload) {
                        return jsonResponse({
                            error: {
                                code: "MISSING_PAYLOAD",
                                message: "Missing request body"
                            }
                        }, HttpStatusCode.BadRequest);
                    }

                    const createdDemographics = await demographicsHandler.createDemographics(
                        createPatientId, 
                        createPayload, 
                        decode?.oid
                    );
                    return jsonResponse(createdDemographics, HttpStatusCode.Created);

                case HttpMethod.put:
                    const demographicsId = request.query.get("id");
                    if (!demographicsId) {
                        return jsonResponse({
                            error: {
                                code: "MISSING_ID",
                                message: "Missing required parameter: id"
                            }
                        }, HttpStatusCode.BadRequest);
                    }

                    const updatePayload = await request.json();
                    if (!updatePayload) {
                        return jsonResponse({
                            error: {
                                code: "MISSING_PAYLOAD",
                                message: "Missing request body"
                            }
                        }, HttpStatusCode.BadRequest);
                    }

                    const updatedDemographics = await demographicsHandler.updateDemographics(
                        demographicsId, 
                        updatePayload, 
                        decode?.oid
                    );
                    return jsonResponse(updatedDemographics, HttpStatusCode.Ok);

                default:
                    return jsonResponse({
                        error: {
                            code: "METHOD_NOT_ALLOWED",
                            message: "Unsupported HTTP method"
                        }
                    }, HttpStatusCode.MethodNotAllowed);
            }
        } catch (error) {
            context.log.error('Demographics API error:', error);
            
            // Handle specific error types
            if (error.message.includes('Validation failed')) {
                return jsonResponse({
                    error: {
                        code: "VALIDATION_ERROR",
                        message: error.message,
                        details: error.message
                    }
                }, HttpStatusCode.BadRequest);
            }

            if (error.message.includes('already exists')) {
                return jsonResponse({
                    error: {
                        code: "CONFLICT",
                        message: error.message
                    }
                }, HttpStatusCode.Conflict);
            }

            if (error.message.includes('not found')) {
                return jsonResponse({
                    error: {
                        code: "NOT_FOUND",
                        message: error.message
                    }
                }, HttpStatusCode.NotFound);
            }

            // Generic server error
            return jsonResponse({
                error: {
                    code: "INTERNAL_SERVER_ERROR",
                    message: "An internal server error occurred",
                    details: error.message
                }
            }, HttpStatusCode.InternalServerError);
        }
    }
});