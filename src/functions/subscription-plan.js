const { app } = require('@azure/functions')
const subscriptionHandler = require('../handlers/subscription-handler')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const { HttpMethod } = require('../common/constant')

// Subscription Plans Management
app.http('subscription-plan', {
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    const decode = context.extraInputs.get('decode')

    try {
      switch (req.method) {
        case HttpMethod.get:
          const action = req.query.get('action')
          const id = req.query.get('id')

          if (action === 'search') {
            return await subscriptionHandler.searchPlans(req, decode)
          } else if (id) {
            return await subscriptionHandler.getPlan(req, decode)
          } else {
            return await subscriptionHandler.getAllPlans(req, decode)
          }

        case HttpMethod.post:
          if (!req.body) {
            return jsonResponse(
              'Missing subscription plan payload',
              HttpStatusCode.BadRequest,
            )
          }
          return await subscriptionHandler.createPlan(req, decode)

        case HttpMethod.put:
          if (!req.body) {
            return jsonResponse(
              'Missing subscription plan payload',
              HttpStatusCode.BadRequest,
            )
          }

          const putAction = req.query.get('action')
          if (putAction === 'deactivate') {
            return await subscriptionHandler.deactivatePlan(req, decode)
          }

          return await subscriptionHandler.replacePlan(req, decode)

        case HttpMethod.patch:
          if (!req.body) {
            return jsonResponse(
              'Missing subscription plan payload',
              HttpStatusCode.BadRequest,
            )
          }

          const patchAction = req.query.get('action')
          if (patchAction === 'deactivate') {
            return await subscriptionHandler.deactivatePlan(req, decode)
          }

          return await subscriptionHandler.updatePlan(req, decode)

        case HttpMethod.delete:
          return await subscriptionHandler.deletePlan(req, decode)

        default:
          return jsonResponse(
            'Method not allowed',
            HttpStatusCode.MethodNotAllowed,
          )
      }
    } catch (error) {
      context.error('Error in subscription-plan function:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  },
})
