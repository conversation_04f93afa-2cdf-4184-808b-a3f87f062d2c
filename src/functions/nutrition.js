const { app } = require('@azure/functions');
const nutritionHandler = require('../handlers/nutrition-handler');
const { HttpStatusCode } = require('axios');
const { jsonResponse } = require('../common/helper');
const { HttpMethod, DashboardFilter, MacroMetric, MicroMetric, AdditionalMetric, MacroChartMetric } = require('../common/constant');

app.http('food-list', {
    methods: ['GET'],
    route: 'food/list',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const input = req.query.get('input') || req.query.get('q');
                    const data = await nutritionHandler.getFoodNames(input);
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching food list:', err);
                    return jsonResponse('Error fetching food list', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('food-serving-unit', {
    methods: ['GET'],
    route: 'food/serving-unit',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const foodName = req.query.get('foodName') || req.query.get('name');
                    
                    if (!foodName || foodName.trim().length === 0) {
                        return jsonResponse({});
                    }

                    const data = await nutritionHandler.getServingUnitByFoodName(foodName.trim());
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching serving unit:', err);
                    return jsonResponse('Error fetching serving unit', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('patient-nutrition-summary', {
    methods: ['GET'],
    route: 'patient/nutrition/summary',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const patientId = req.query.get('patientId');
                    const filter = req.query.get('filter') || 'last_7_days';
                    const metricsParam = req.query.get('metrics');
                    const sort = req.query.get('sort') || 'asc';
                    
                    if (!patientId) {
                        return jsonResponse('Missing required parameter: patientId', HttpStatusCode.BadRequest);
                    }

                    const validFilters = Object.values(DashboardFilter);
                    if (!validFilters.includes(filter.toLowerCase())) {
                        return jsonResponse(`Invalid filter. Valid options: ${validFilters.join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    const validSortOptions = ['asc', 'desc'];
                    if (!validSortOptions.includes(sort.toLowerCase())) {
                        return jsonResponse(`Invalid sort option. Valid options: ${validSortOptions.join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    let metrics = null;
                    if (metricsParam && metricsParam.trim().length > 0) {
                        metrics = metricsParam.split(',').map(m => m.trim().toLowerCase()).filter(m => m.length > 0);
                    }

                    const data = await nutritionHandler.getNutritionSummary(patientId, filter.toLowerCase(), metrics, sort.toLowerCase());
                    
                    if (data === null) {
                        return jsonResponse('Error calculating nutrition summary', HttpStatusCode.InternalServerError);
                    }
                    
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching nutrition summary:', err);
                    return jsonResponse('Error fetching nutrition summary', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('patient-nutrition-average', {
    methods: ['GET'],
    route: 'patient/nutrition/average',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const patientId = req.query.get('patientId');
                    const filter = req.query.get('filter') || 'last_7_days';
                    const metricsParam = req.query.get('metrics');
                    
                    if (!patientId) {
                        return jsonResponse('Missing required parameter: patientId', HttpStatusCode.BadRequest);
                    }

                    const validFilters = Object.values(DashboardFilter);
                    if (!validFilters.includes(filter.toLowerCase())) {
                        return jsonResponse(`Invalid filter. Valid options: ${validFilters.join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    let metrics = null; 
                    if (metricsParam && metricsParam.trim().length > 0) {
                        metrics = metricsParam.split(',').map(m => m.trim().toLowerCase()).filter(m => m.length > 0);
                    }

                    const data = await nutritionHandler.getNutritionAverage(patientId, filter.toLowerCase(), metrics);
                    
                    if (data === null) {
                        return jsonResponse('Error calculating nutrition average', HttpStatusCode.InternalServerError);
                    }
                    
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching nutrition average:', err);
                    return jsonResponse('Error fetching nutrition average', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('patient-nutrition-chart', {
    methods: ['GET'],
    route: 'patient/nutrition/chart',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const patientId = req.query.get('patientId');
                    const filter = req.query.get('filter') || 'last_7_days';
                    const metric = req.query.get('metric') || 'carbs'; // carbs, protein, fat, fiber, calories
                    
                    if (!patientId) {
                        return jsonResponse('Missing required parameter: patientId', HttpStatusCode.BadRequest);
                    }

                    const validFilters = Object.values(DashboardFilter);
                    if (!validFilters.includes(filter.toLowerCase())) {
                        return jsonResponse(`Invalid filter. Valid options: ${validFilters.join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    const validMacroMetrics = Object.values(MacroChartMetric);
                    const validMicroMetrics = Object.values(MicroMetric);
                    const validAdditionalMetrics = Object.values(AdditionalMetric);
                    const allValidMetrics = [...validMacroMetrics, ...validMicroMetrics, ...validAdditionalMetrics, 'micro', 'additional'];
                    
                    if (!allValidMetrics.includes(metric.toLowerCase())) {
                        return jsonResponse(`Invalid metric. Valid options: ${[...validMacroMetrics, 'micro', 'additional'].join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    const data = await nutritionHandler.getNutritionChart(patientId, filter.toLowerCase(), metric.toLowerCase());
                    
                    if (data === null) {
                        return jsonResponse('Error generating nutrition chart data', HttpStatusCode.InternalServerError);
                    }
                    
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching nutrition chart data:', err);
                    return jsonResponse('Error fetching nutrition chart data', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});

app.http('patient-nutrition-percentage-chart', {
    methods: ['GET'],
    route: 'patient/nutrition/percentage-chart',
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);
        
        switch (req.method) {
            case HttpMethod.get:
                try {
                    const patientId = req.query.get('patientId');
                    const filter = req.query.get('filter') || 'last_7_days';
                    
                    if (!patientId) {
                        return jsonResponse('Missing required parameter: patientId', HttpStatusCode.BadRequest);
                    }

                    const validFilters = Object.values(DashboardFilter);
                    if (!validFilters.includes(filter.toLowerCase())) {
                        return jsonResponse(`Invalid filter. Valid options: ${validFilters.join(', ')}`, HttpStatusCode.BadRequest);
                    }

                    const data = await nutritionHandler.getNutritionPercentageChart(patientId, filter.toLowerCase());
                    
                    if (data === null) {
                        return jsonResponse('Error generating nutrition percentage chart data', HttpStatusCode.InternalServerError);
                    }
                    
                    return jsonResponse(data);
                } catch (err) {
                    context.log('Error fetching nutrition percentage chart data:', err);
                    return jsonResponse('Error fetching nutrition percentage chart data', HttpStatusCode.InternalServerError);
                }
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
