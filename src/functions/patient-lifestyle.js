const { app } = require('@azure/functions');
const patientLifeStyleHandler = require('../handlers/patient-lifestyle-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('patient-lifestyle', {
    methods: ['GET', 'POST', 'PATCH'],
    authLevel: 'function',
    route: 'patient/lifestyle',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');
        const source = request.query.get("source");
        const patientId = request.query.get("patientId");
        const fromDate = request.query.get("fromDate");
        const toDate = request.query.get("toDate");
        switch (request.method) {
            case HttpMethod.get:
                if (!source || !patientId) {
                    return jsonResponse(`Missing source or patientId`, HttpStatusCode.BadRequest)
                }
                if (fromDate && toDate) {
                    var res = await patientLifeStyleHandler.getPatientLifeStyleBySourceNameAndSession(patientId, source, fromDate, toDate);
                    return jsonResponse(res);
                }
                var res = await patientLifeStyleHandler.getPatientLifeStyleBySourceName(patientId, source);
                return jsonResponse(res);
            case HttpMethod.post:
                const payload = await request.json()
                if (!payload || !patientId) {
                    return jsonResponse(`Missing payload or patientId`, HttpStatusCode.BadRequest)
                }
                var data = await patientLifeStyleHandler.createPatientLifeStyle(patientId, payload, decode.oid);
                return jsonResponse(data)
            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await patientLifeStyleHandler.patchPatientLifeStyle(id, patchPayload, decode.oid);
                return jsonResponse(data);

            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
