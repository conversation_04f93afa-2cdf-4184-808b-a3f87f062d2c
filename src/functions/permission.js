const { app } = require('@azure/functions')
const permissionHandler = require('../handlers/role-permission-handler')
const { jsonResponse } = require('../common/helper')

app.http('assign-permissions', {
  methods: ['POST'],
  authLevel: 'function',
  handler: permissionHandler.assignPermissions,
})

app.http('get-permissions-by-role-id', {
  methods: ['GET'],
  route: 'permissions/api-list',
  authLevel: 'function',
  handler: async (req) => {
    const result = await permissionHandler.getAPIListByRoleId(req)

    return jsonResponse(result.body, result.status)
  },
})
