// CRITICAL: Initialize Key Vault secrets before Azure Functions runtime starts
require('../config/early-init')

const { app } = require('@azure/functions')
const consultationRecordHistoryService = require('../services/consultation-record-history-service')
const lifestyleRecordHistoryService = require('../services/lifestyle-record-history-service')

app.timer('cleanup-record-history', {
  schedule: '0 0 0 * * 0', // Every Sunday at 12:00 AM
  handler: async (myTimer, context) => {
    context.log('[Timer] Record history cleanup job running...')

    try {
      // Calculate cutoff date (24 hours ago)
      const cutoffDate = new Date(Date.now() - 24 * 60 * 60 * 1000)
      context.log(`Cleaning up records older than: ${cutoffDate.toISOString()}`)

      // Clean up consultation record history
      let consultationDeletedCount = 0
      try {
        consultationDeletedCount =
          await consultationRecordHistoryService.deleteOldConsultationRecords(
            cutoffDate,
          )
        context.log(
          `Deleted ${consultationDeletedCount} old consultation records`,
        )
      } catch (consultationError) {
        context.log(
          'Error cleaning up consultation records:',
          consultationError,
        )
      }

      // Clean up lifestyle record history
      let lifestyleDeletedCount = 0
      try {
        lifestyleDeletedCount =
          await lifestyleRecordHistoryService.deleteOldLifestyleRecords(
            cutoffDate,
          )
        context.log(`Deleted ${lifestyleDeletedCount} old lifestyle records`)
      } catch (lifestyleError) {
        context.log('Error cleaning up lifestyle records:', lifestyleError)
      }

      const totalDeleted = consultationDeletedCount + lifestyleDeletedCount
      context.log(
        `[Timer] Record history cleanup job completed. Total records deleted: ${totalDeleted}`,
      )

      // Log summary
      context.log('Cleanup Summary:', {
        cutoffDate: cutoffDate.toISOString(),
        consultationRecordsDeleted: consultationDeletedCount,
        lifestyleRecordsDeleted: lifestyleDeletedCount,
        totalRecordsDeleted: totalDeleted,
        completedAt: new Date().toISOString(),
      })
    } catch (error) {
      context.log('[Timer] Record history cleanup job failed:', error)
      throw error
    }
  },
})
