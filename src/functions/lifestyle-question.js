const { app } = require('@azure/functions');
const logging = require('../common/logging');
const lifeStyleHandler = require('../handlers/lifestyle-handler');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');

app.http('lifestyle-question', {
    methods: ['GET', 'POST', 'PATCH', 'DELETE'],
    authLevel: 'function',
    route: 'lifestyle/question',
    handler: async (request, context) => {
        context.log(`Http function processed request for url "${request.url}"`);
        const decode = context.extraInputs.get('decode');
        const source = request.query.get("source");
        switch (request.method) {
            case HttpMethod.get:
                if (!source) {
                    return jsonResponse(`Missing source`, HttpStatusCode.BadRequest)
                }
                const section = request.query.get("section");
                if (section) {
                    var res = await lifeStyleHandler.getLifeStyeBySourceNameAndSession(source, section);
                    return jsonResponse(res);
                }
                var res = await lifeStyleHandler.getLifeStyeBySourceName(source);
                return jsonResponse(res);
            case HttpMethod.post:
                if (!source) {
                    return jsonResponse(`Missing source`, HttpStatusCode.BadRequest)
                }
                const payload = await request.json()
                if (!payload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                payload.source = source;
                var data = await lifeStyleHandler.createLifeStyle(payload, decode.oid)
                return jsonResponse(data)
            case HttpMethod.patch:
                const patchPayload = await request.json()
                const id = request.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                if (!patchPayload) {
                    return jsonResponse(`Missing payload`, HttpStatusCode.BadRequest)
                }
                var data = await lifeStyleHandler.patchLifeStyle(id, patchPayload, decode.oid)
                return jsonResponse(data);
            case HttpMethod.delete:
                const deleteid = request.query.get("id");
                if (!deleteid) {
                    return jsonResponse(`Missing id`, HttpStatusCode.BadRequest)
                }
                var data = await lifeStyleHandler.deleteLifeStyle(deleteid)
                return jsonResponse(data);

            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
