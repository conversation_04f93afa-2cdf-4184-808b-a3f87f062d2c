const { app } = require('@azure/functions')
const dashboardHandler = require('../handlers/admin/dashboard-handler')
const userDashboardHandler = require('../handlers/user-dashboard-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { HttpMethod } = require('../common/constant')
const jwt = require('jsonwebtoken')

app.http('dashboard-summary', {
  methods: ['GET'],
  route: 'dashboard/summary',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const data = await dashboardHandler.getDashboardData(req)
      return jsonResponse(data, 200)
    } catch (err) {
      return jsonResponse('Error fetching dashboard summary', 500)
    }
  },
})

app.http('upcoming-appointments', {
  methods: ['GET'],
  route: 'dashboard/upcoming',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)

    const decode = context.extraInputs.get('decode')
    if (!decode || !decode.oid) {
      return jsonResponse('User authentication required', HttpStatusCode.Unauthorized)
    }

    try {
      if (req.method !== HttpMethod.get) {
        return jsonResponse('Unsupported HTTP method', HttpStatusCode.MethodNotAllowed)
      }

      const date = req.query.get('date')
      const doctorId = req.query.get('doctorId')
      const patientName = req.query.get('patientName')
      const patientId = req.query.get('patientId')

      const upcomingAppointments = await userDashboardHandler.getUpcomingAppointments(
        decode.oid,
        date,
        doctorId,
        patientName,
        patientId,
      )
      return jsonResponse({ upcomingAppointments })
    } catch (error) {
      context.log('Error in upcoming appointments:', error)
      return jsonResponse('Error fetching upcoming appointments', HttpStatusCode.InternalServerError)
    }
  },
})

app.http('user-dashboard', {
  methods: ['GET'],
  route: 'dashboard/user',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed request for url "${req.url}"`)
    
    const decode = context.extraInputs.get('decode')
    if (!decode || !decode.oid) {
      return jsonResponse('User authentication required', HttpStatusCode.Unauthorized)
    }

    try {
      switch (req.method) {
        case HttpMethod.get: {
          const endpoint = req.query.get('endpoint')
          
          if (endpoint === 'total-patients') {
            const totalPatients = await userDashboardHandler.getTotalPatients(decode.oid)
            return jsonResponse({ totalPatients })
          }
          
          if (endpoint === 'todays-appointments') {
            const todaysAppointments = await userDashboardHandler.getTodaysAppointments(decode.oid)
            return jsonResponse({ todaysAppointments })
          }
          
          if (endpoint === 'patient-queue') {
            const patientQueue = await userDashboardHandler.getPatientQueue(decode.oid)
            return jsonResponse({ patientQueue })
          }
          
          if (endpoint === 'average-waiting-time') {
            const averageWaitingTime = await userDashboardHandler.getAverageWaitingTime(decode.oid)
            return jsonResponse(averageWaitingTime)
          }
          
          // Default: return all dashboard data
          const data = await userDashboardHandler.getDashboardData(decode.oid)
          return jsonResponse(data)
        }

        default:
          return jsonResponse('Unsupported HTTP method', HttpStatusCode.MethodNotAllowed)
      }
    } catch (error) {
      context.log('Error in user dashboard:', error)
      return jsonResponse('Error fetching user dashboard data', HttpStatusCode.InternalServerError)
    }
  },
})

/*app.http('generate-physician-dashboard-url', {
  methods: ['POST'],
  route: 'generate-physician-dashboard-url',
  authLevel: 'function',
  handler: async (req, context) => {
    try {
      const decode = context.extraInputs.get('decode')
      if (!decode || !decode.oid) {
        return jsonResponse('User authentication required', HttpStatusCode.Unauthorized)
      }

      const dashboardId = req.query.get('dashboardId')
      const organizationId = req.query.get('organizationId')
      const expiresIn = req.query.get('expiresIn') || '24h'

      if (!dashboardId) {
        return jsonResponse({
          error: 'Validation failed',
          details: [{ msg: 'Dashboard ID is required', param: 'dashboardId' }]
        }, 400)
      }

      const userId = decode.oid

      // Create a secure token that includes the user's doctor ID
      const embedToken = jwt.sign(
        {
          userId,
          doctorId: userId, // User ID is the doctor ID
          organizationId: organizationId || decode.organizationId,
          dashboardId,
          embedType: 'physician-dashboard',
          iat: Math.floor(Date.now() / 1000)
        },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn }
      )

      // Generate the Redash public dashboard URL with token
      const redashBaseUrl = process.env.REDASH_URL || 'http://localhost:5000'
      const apiBaseUrl = process.env.API_BASE_URL || 'http://api:3000'

      // The dashboard URL with token that Redash will use to authenticate API calls
      const dashboardUrl = `${redashBaseUrl}/public/dashboards/${dashboardId}?org_slug=default&token=${embedToken}`

      return jsonResponse({
        success: true,
        dashboardUrl,
        embedToken,
        userId,
        doctorId: userId,
        expiresAt: new Date(Date.now() + (expiresIn.includes('h') ? parseInt(expiresIn) * 3600000 : 86400000)),
        instructions: {
          usage: 'Use this URL to access the personalized physician dashboard',
          apiEndpoints: `API calls in the dashboard will use ${apiBaseUrl}/api/redash-proxy/physician-dashboard/* with token=${embedToken}`,
        }
      })

    } catch (error) {
      context.log('Generate physician dashboard URL error:', error)
      return jsonResponse({
        error: 'Error generating physician dashboard URL',
        message: error.message
      }, 500)
    }
  },
})
*/