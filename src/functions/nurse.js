const { app } = require('@azure/functions');
const { HttpMethod } = require('../common/constant');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const nurseHandler = require('../handlers/nurse-handler');

app.http('nurse', {
    methods: ['GET', 'POST', 'PUT'],
    authLevel: 'function',
    handler: async (req, context) => {
        context.log(`Http function processed req for url "${req.url}"`);

        const decode = context.extraInputs.get("decode");

        switch (req.method) {
            case HttpMethod.get:
                const id = req.query.get("id");
                if (!id) {
                    return jsonResponse(`Missing patient id: ?id=`, HttpStatusCode.BadRequest);
                }
                var data = await nurseHandler.getNurseById(id);
                return jsonResponse(data);

            case HttpMethod.post:
            case HttpMethod.put:
                if (!req.body) {
                    return jsonResponse(`Missing request payload`, HttpStatusCode.BadRequest);
                }
                const payload = await req.json();
                var data = null;
                if (req.method == HttpMethod.post) {
                    data = await nurseHandler.createNurse(payload, decode.oid);
                } else {
                    data = await nurseHandler.updateNurse(payload, decode.oid);
                }
                return jsonResponse(data);
                
            default:
                return jsonResponse(`Unsupported HTTP method`, HttpStatusCode.MethodNotAllowed);
        }
    }
});
