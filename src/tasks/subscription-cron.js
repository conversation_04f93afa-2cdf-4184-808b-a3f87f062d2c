const cron = require('node-cron')
const subscriptionService = require('../services/subscription-service')
const emailService = require('../services/email-service')
const logging = require('../common/logging')

// Timer to process expired subscriptions
// Runs every day at 00:00 (midnight)
const processExpiredSubscriptions = () => {
  cron.schedule('0 0 0 * * *', async () => {
    logging.logInfo('[Cron] Subscription expired job running...')

    try {
      const result = await subscriptionService.processExpiredSubscriptions()

      if (result.success) {
        logging.logInfo(
          `[Cron] Subscription expired job completed: ${JSON.stringify(result.data)}`,
        )

        // Send expiration notification emails
        if (result.data && result.data.expiredSubscriptions) {
          for (const subscription of result.data.expiredSubscriptions) {
            try {
              if (
                subscription.contactEmail ||
                subscription.billingDetails?.email
              ) {
                await emailService.sendSubscriptionExpiredNotification(
                  subscription,
                )
                logging.logInfo(
                  `[Cron] Sent expiration notification to ${subscription.contactEmail || subscription.billingDetails?.email}`,
                )
              }
            } catch (emailError) {
              logging.logError(
                `[<PERSON>ron] Failed to send expiration email for subscription ${subscription.id}:`,
                emailError,
              )
            }
          }
        }
      } else {
        logging.logError(
          `[Cron] Subscription expired job failed: ${result.message}`,
        )
      }
    } catch (error) {
      logging.logError('[Cron] Subscription expired job error:', error)
    }
  })
}

// Timer to notify about upcoming renewals
// Runs every day at 09:00 AM
const sendRenewalReminders = () => {
  cron.schedule('0 0 9 * * *', async () => {
    logging.logInfo('[Cron] Subscription renewal reminder job running...')

    try {
      // Get subscriptions expiring in the next 7 days
      const result = await subscriptionService.getUpcomingRenewals(7)

      if (result.success && result.data.length > 0) {
        logging.logInfo(
          `[Cron] Found ${result.count} subscriptions expiring in the next 7 days`,
        )

        // Send renewal reminder emails
        for (const subscription of result.data) {
          try {
            if (
              subscription.contactEmail ||
              subscription.billingDetails?.email
            ) {
              await emailService.sendSubscriptionRenewalReminder(subscription)
              logging.logInfo(
                `[Cron] Sent renewal reminder to ${subscription.contactEmail || subscription.billingDetails?.email}`,
              )
            }
          } catch (emailError) {
            logging.logError(
              `[Cron] Failed to send renewal reminder for subscription ${subscription.id}:`,
              emailError,
            )
          }
        }

        logging.logInfo('[Cron] Renewal reminders processed successfully')
      } else {
        logging.logInfo('[Cron] No upcoming renewals found')
      }
    } catch (error) {
      logging.logError('[Cron] Renewal reminder job error:', error)
    }
  })
}

// Initialize all subscription cron jobs
const initializeSubscriptionCrons = () => {
  processExpiredSubscriptions()
  sendRenewalReminders()
  logging.logInfo('[Cron] Subscription cron jobs initialized')
}

module.exports = initializeSubscriptionCrons
