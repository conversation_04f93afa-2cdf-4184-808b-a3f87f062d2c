const organizationService = require('../../services/admin/organization-service')
const userRepository = require('../../repositories/admin/user-repository')

class DashboardHandler {
  async getDashboardData(req) {
    try {
      const organizations = await organizationService.listOrganizations(
        '',
        1000,
        1,
      )
      const allUsers = await userRepository.getAllUsers()
      const totalOrganizations = organizations.organizations.length
      const totalUsers = allUsers.length
      return { totalOrganizations, totalUsers }
    } catch (err) {
      throw new Error('Error fetching dashboard data: ' + err.message)
    }
  }
}

module.exports = new DashboardHandler()
