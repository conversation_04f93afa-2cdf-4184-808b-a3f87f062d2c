const { logError } = require("../common/logging");
const appointmentService = require("../services/appointment-service");

class AppointmentHandler {
    async addAppoinment(appointment, doctorId) {
        try {
            var date = appointment.date
            var existAppointment = await appointmentService.getAppointmentByQuery(`SELECT * FROM c WHERE c.doctorId = '${doctorId}' AND c.date = '${date}'`);
            if (existAppointment.length > 0) {
                var appm = existAppointment[0]
                var appointments = appm.appointments
                var latestQueue = appointments.length
                var newAppm = {
                    queueId: `QUE${Date.now()}`,
                    patientId: appointment.patientId,
                    time: appointment.time,
                    status: appointment.status,
                    queuePosition: latestQueue + 1
                }
                appm.appointments.push(newAppm);
                var result = await appointmentService.updateAppointment(appm)
                return result;
            } else {
                
                var appm = {
                    "id": `APP${new Date().getFullYear()}${new Date().getMonth()}${new Date().getDate()}${new Date().getMilliseconds()}`,
                    "doctorId": doctorId,
                    "date": `${date}`,
                    "appointments": [
                        {
                            "appointmentId": `QUE${Date.now()}`,
                            "patientId": appointment.patientId,
                            "time": appointment.time,
                            "queuePosition": 1,
                            "status": appointment.status
                        }
                    ]
                }

                var result = await appointmentService.createAppointment(appm);
                return result

            }
        } catch (error) {
            logError(``, error);
            return null
        }

    }

    async getAppionmentByDoctor(doctorId, date) {
        try {
            var query = `SELECT * FROM c WHERE c.doctorId = '${doctorId}' AND c.date = '${date}'`
            var data = await appointmentService.getAppointmentByQuery(query);
            return data;
        } catch (error) {
            logError(``, error);
            return null
        }
    }

    async getAppoimentbyPatientId(patientId, date) {
        try {
            try {
                var query = `SELECT * FROM c JOIN a IN c.appointments  WHERE a.patientId = '${patientId}' and c.date = '${date}'`
                var data = await appointmentService.getAppointmentByQuery(query);
                return data;
            } catch (error) {
                logError(``, error);
                return null
            }
        } catch (error) {
            logError(``, error);
            return null
        }
    }

    async updateAppointment(appointment) {
        try {
            var data = await appointmentService.updateAppointment(appointment);
            return data;
        } catch (error) {
            logError(``, error);
            return null
        }
    }
}

module.exports = new AppointmentHandler();