const logging = require("../common/logging");
const { v4: uuidv4 } = require('uuid');
const vitalsService = require("../services/vitals-service");

class VitalsHanlder {

    async CreateVitals(patientId ,Vitals, createdBy) {
        logging.logInfo(`Create Vitals, PatientId: ${patientId} by ${createdBy}`);
        if (!Vitals.id) {
            Vitals.id = uuidv4()
        }
        Vitals.patientId = patientId;
        Vitals.created_by = createdBy;
        Vitals.update_by = createdBy;
        var res = await vitalsService.addVitals(Vitals);
        return res;
    }

    async updateVitals(patientId, vitals, updateBy) {
        logging.logInfo(`Update Vitals for patient : ${patientId} :: ${vitals}`);
        if (vitals.patientId !== patientId) {
            return null;
        }
        vitals.updated_by = updateBy;
        var res = await vitalsService.updateVitals(vitals);
        return res
    }

    async getVitalsByPatient(patientId) {
        logging.logInfo(`Get Vitals by patient : ${patientId}`);
        var data = await vitalsService.getVitalsByPatient(patientId);
        return data
    }

    async getVitalsByQuery(queryString) {
        logging.logInfo(`Get Vitals by query String : ${queryString}`);
        var data = await vitalsService.getVitalsByQuery(queryString);
        return data;
    }

    async getVitalsbyDate(patientId, startDate, endDate) {
        logging.logInfo(`Get Vitals by patienId from ${startDate} to ${endDate}`);
        var query = `SELECT * FROM c WHERE c.updated_on >= '${startDate}' and c.updated_on <= '${endDate}' and c.patientId = '${patientId}'`;
        var data = await vitalsService.getVitalsByQuery(query);
        return data
    }

}

module.exports = new VitalsHanlder();