
const customiseEmrService = require("../services/customise-emr-service");
const { logError, logInfo } = require("../common/logging");

class CustomiseEmrsHalder {

    async getCustomiseEmr(sourceName) {
        try {
            logInfo(`Get customise emr by sourceName: ${sourceName}`);
            var data = await customiseEmrService.getCustomiseEmrBySourceName(sourceName);
            return data;
        } catch (error) {
            logError(`Unable to get customise emr with sourceName ${sourceName}`, error);
            return null
        }
    }

    async createCustomiseEmr(customiseEmr, created_by) {
        try {
            logInfo(`Create customise emr : `, JSON.stringify(customiseEmr));
            customiseEmr.created_by = created_by;
            customiseEmr.updated_by = created_by;
            var result = await customiseEmrService.createCustomiseEmr(customiseEmr);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async patchCustomiseEmr(customiseEmrId, customiseEmrData, updated_by) {
        try {
            logInfo(`Patch customise emr : ${customiseEmrId} :: ${JSON.stringify(customiseEmrData)}`);
            customiseEmrData.updated_by = updated_by;
            var result = await customiseEmrService.patchCustomiseEmr(customiseEmrId, customiseEmrData);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

}

module.exports = new CustomiseEmrsHalder();