const paymentService = require('../services/payment-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')
const { PaymentType } = require('../common/constant')

class PaymentHandler {
  async createOrder(req, decode, preParsedBody) {
    try {
      // Use pre-parsed body from context if available (from auth hook), otherwise parse from request
      const paymentData = preParsedBody || (await req.json())

      // For subscription payments with subscriberEmail, patientId is not required
      let requiredFields
      if (
        paymentData.paymentType === 'subscription' &&
        paymentData.subscriberEmail
      ) {
        requiredFields = [
          'amount',
          'paymentType',
          'subscriberEmail',
          'organizationId',
          'description',
        ]
      } else {
        // Base required fields - patientId is required for patient payments
        requiredFields = [
          'amount',
          'paymentType',
          'patientId',
          'organizationId',
          'description',
        ]
      }

      const missingFields = requiredFields.filter(
        (field) => !paymentData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      if (!Object.values(PaymentType).includes(paymentData.paymentType)) {
        return jsonResponse(
          `Invalid payment type. Allowed values: ${Object.values(
            PaymentType,
          ).join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      // Extract user information from JWT token
      if (decode) {
        paymentData.created_by = decode.oid || decode.sub
        paymentData.created_by_email = decode.emails ? decode.emails[0] : null
        paymentData.created_by_name = decode.name || null
      }

      paymentData.amount = Math.round(paymentData.amount * 100)
      const result = await paymentService.createOrder(paymentData)

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in createOrder handler', error)
      return jsonResponse(
        error.message || 'Failed to create payment order',
        HttpStatusCode.InternalServerError,
      )
    }
  }
  async verifyPayment(req) {
    try {
      const verificationData = await req.json()

      const requiredFields = [
        'razorpay_order_id',
        'razorpay_payment_id',
        'razorpay_signature',
      ]
      const missingFields = requiredFields.filter(
        (field) => !verificationData[field],
      )

      if (missingFields.length > 0) {
        return jsonResponse(
          `Missing required fields: ${missingFields.join(', ')}`,
          HttpStatusCode.BadRequest,
        )
      }

      const result = await paymentService.verifyPayment(verificationData)

      if (result.verified) {
        return jsonResponse(result, HttpStatusCode.Ok)
      } else {
        return jsonResponse(result, HttpStatusCode.BadRequest)
      }
    } catch (error) {
      logging.logError('Error in verifyPayment handler', error)
      return jsonResponse(
        'Payment verification failed',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async handleWebhook(req) {
    try {
      const signature = req.headers.get('x-razorpay-signature')

      if (!signature) {
        return jsonResponse(
          'Missing webhook signature',
          HttpStatusCode.BadRequest,
        )
      }

      const webhookData = await req.json()

      const result = await paymentService.handleWebhook(webhookData, signature)

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in handleWebhook handler', error)
      return jsonResponse(
        'Webhook processing failed',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPaymentDetails(req) {
    try {
      const id = req.query.get('id')
      const paymentReferenceToken = req.query.get('paymentReferenceToken')

      if (!id) {
        return jsonResponse(
          'Missing paymentId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      // Use secure method if token provided, otherwise fallback to regular method
      const payment = paymentReferenceToken
        ? await paymentService.getSecurePaymentById(id, paymentReferenceToken)
        : await paymentService.getPaymentById(id)

      return jsonResponse(payment, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentDetails handler', error)

      if (error.message.includes('not found')) {
        return jsonResponse('Payment not found', HttpStatusCode.NotFound)
      }

      if (error.message.includes('Invalid or expired payment token')) {
        return jsonResponse(
          'Invalid or expired payment token',
          HttpStatusCode.Unauthorized,
        )
      }

      return jsonResponse(
        'Failed to fetch payment details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getOrganizationPayments(req) {
    try {
      const organizationId = req.query.get('organizationId')
      const pageSize = parseInt(req.query.get('pageSize')) || 20
      const continuationToken = req.query.get('continuationToken')

      const filters = {
        patientId: req.query.get('patientId'),
        status: req.query.get('status'),
        paymentType: req.query.get('paymentType'),
      }

      const result = await paymentService.getPaymentsByOrganization(
        organizationId,
        pageSize,
        continuationToken,
        filters,
      )

      return jsonResponse(result, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getOrganizationPayments handler', error)
      return jsonResponse(
        'Failed to fetch organization payments',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPaymentStatistics(req) {
    try {
      const organizationId = req.query.get('organizationId')

      if (!organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      const stats = await paymentService.getPaymentStatistics(organizationId)

      return jsonResponse(stats, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getPaymentStatistics handler', error)
      return jsonResponse(
        'Failed to fetch payment statistics',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPayments(req) {
    try {
      const filters = {
        organizationId: req.query.get('organizationId'),
        patientId: req.query.get('patientId'),
        status: req.query.get('status'),
        paymentType: req.query.get('paymentType'),
        startDate: req.query.get('startDate'),
        endDate: req.query.get('endDate'),
        minAmount: req.query.get('minAmount')
          ? parseInt(req.query.get('minAmount')) * 100
          : null,
        maxAmount: req.query.get('maxAmount')
          ? parseInt(req.query.get('maxAmount')) * 100
          : null,
      }

      Object.keys(filters).forEach((key) => {
        if (
          filters[key] === null ||
          filters[key] === undefined ||
          filters[key] === ''
        ) {
          delete filters[key]
        }
      })

      if (!filters.organizationId) {
        return jsonResponse(
          'Missing organizationId parameter',
          HttpStatusCode.BadRequest,
        )
      }

      const payments = await paymentService.getPaymentsByOrganization(
        filters.organizationId,
        20,
        null,
        filters,
      )

      return jsonResponse(payments, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in searchPayments handler', error)
      return jsonResponse(
        'Failed to search payments',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Clean up expired payment tokens
   */
  async cleanupExpiredTokens(req) {
    try {
      const cleanedCount = await paymentService.cleanupExpiredTokens()

      return jsonResponse(
        {
          success: true,
          message: `Cleaned up ${cleanedCount} expired tokens`,
          cleanedCount: cleanedCount,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in cleanupExpiredTokens handler', error)
      return jsonResponse(
        'Failed to cleanup expired tokens',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Validate payment tokens
   */
  async validateTokens(req) {
    try {
      const tokenData = await req.json()
      const { paymentReferenceToken, sessionToken, razorpayOrderId } = tokenData

      if (!paymentReferenceToken && !sessionToken) {
        return jsonResponse(
          'At least one token (paymentReferenceToken or sessionToken) is required',
          HttpStatusCode.BadRequest,
        )
      }

      const isValid = await paymentService.validatePaymentTokens(
        paymentReferenceToken,
        sessionToken,
        razorpayOrderId,
      )

      return jsonResponse(
        {
          valid: isValid,
          message: isValid ? 'Tokens are valid' : 'Invalid or expired tokens',
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in validateTokens handler', error)
      return jsonResponse(
        'Failed to validate tokens',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PaymentHandler()
