const invoiceService = require('../services/invoice-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')

class InvoiceHandler {
  async getAllInvoices(req, decode) {
    try {
      // Support both JWT organizationId and query parameter
      // Query parameter takes precedence for super admin users
      const organizationId = req.query.get('organizationId') || (decode && decode.organizationId)
      const page = parseInt(req.query.get('page')) || 1
      const limit = parseInt(req.query.get('limit')) || 20
      const searchTerm = req.query.get('search')
      const paymentType = req.query.get('type') // consultation, pharmacy, lab_test, etc.
      const startDate = req.query.get('startDate')
      const endDate = req.query.get('endDate')
      const gender = req.query.get('gender')

      logging.logInfo(`Invoice request - organizationId: ${organizationId || 'NOT PROVIDED'}`)

      if (!organizationId) {
        return jsonResponse(
          'Organization ID is required. Please provide organizationId query parameter.',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await invoiceService.getInvoicesByOrganization({
        organizationId,
        page,
        limit,
        searchTerm,
        paymentType,
        startDate,
        endDate,
        gender,
      })

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getAllInvoices handler:', error)
      return jsonResponse(
        error.message || 'Failed to fetch invoices',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getInvoiceById(req, decode) {
    try {
      const invoiceId = req.query.get('id')

      if (!invoiceId) {
        return jsonResponse('Invoice ID is required', HttpStatusCode.BadRequest)
      }

      const result = await invoiceService.getInvoiceById(invoiceId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getInvoiceById handler:', error)
      return jsonResponse(
        error.message || 'Failed to fetch invoice',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPatientProfileWithConsultations(patientId, organizationId, userId) {
    const data = await invoiceService.getPatientProfileWithConsultations(
      patientId,
      organizationId,
    )
    return data
  }
}

module.exports = new InvoiceHandler()
