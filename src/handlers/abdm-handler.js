/**
 * ABDM Handler for ABHA number generation, verification, and management
 * Handles all ABDM-related operations and API interactions
 */

const abdmService = require('../services/abdm-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { logError, logInfo } = require('../common/logging')
const {
  ABDMInitiateAadhaarRequest,
  ABDMValidator,
} = require('../models/abdm-model')

class ABDMHandler {
  /**
   * Initiate ABHA number creation using Aadhaar
   * @param {Object} req - Request object
   * @returns {Object} - Response with transaction details
   */
  async initiateAbhaCreationByAadhaar(req) {
    try {
      const requestData = await req.json()

      // Validate request using model
      const validation = ABDMValidator.validateRequest(
        ABDMInitiateAadhaarRequest,
        requestData,
      )
      if (!validation.isValid) {
        return jsonResponse(
          ABDMValidator.formatValidationErrors(validation.errors),
          HttpStatusCode.BadRequest,
        )
      }

      const { aadhaar, mobile } = requestData

      logInfo(`Initiating ABHA creation by Aadhaar for mobile: ${mobile}`)

      const result = await abdmService.initiateAbhaCreationByAadhaar(
        aadhaar,
        mobile,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in initiateAbhaCreationByAadhaar handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP for ABHA creation
   * @param {Object} req - Request object
   * @returns {Object} - Response with verification status
   */
  async verifyOtpForAbhaCreation(req) {
    try {
      const { txnId, otp, type, mobile } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate type (mobile or aadhaar)
      if (type && !['mobile', 'aadhaar'].includes(type)) {
        return jsonResponse(
          'Invalid type. Must be "mobile" or "aadhaar".',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(
        `Verifying ${
          type || 'aadhaar'
        } OTP for ABHA creation with txnId: ${txnId}`,
      )

      // Use appropriate service method based on type
      const result =
        type === 'mobile'
          ? await abdmService.verifyMobileOtpForAbhaCreation(txnId, otp)
          : await abdmService.verifyOtpForAbhaCreation(txnId, otp, mobile)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
            data: result.data,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpForAbhaCreation handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for number: ${abhaNumber}`)

      const result = await abdmService.getAbhaDetailsByNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - ABHA details
   */
  async getAbhaDetailsByMobile(req) {
    try {
      const { mobile } = await req.json()

      // Validate required fields
      if (!mobile) {
        return jsonResponse(
          'Mobile number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate mobile format (10 digits)
      if (!/^\d{10}$/.test(mobile)) {
        return jsonResponse(
          'Invalid mobile number format. Must be 10 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Fetching ABHA details for mobile: ${mobile}`)

      const result = await abdmService.getAbhaDetailsByMobile(mobile)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in getAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify ABHA number
   * @param {Object} req - Request object
   * @returns {Object} - Verification result
   */
  async verifyAbhaNumber(req) {
    try {
      const { abhaNumber } = await req.json()

      // Validate required fields
      if (!abhaNumber) {
        return jsonResponse(
          'ABHA number is required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate ABHA number format (14 digits with hyphens or 14 digits)
      if (
        !/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(abhaNumber) &&
        !/^\d{14}$/.test(abhaNumber)
      ) {
        return jsonResponse(
          'Invalid ABHA number format',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying ABHA number: ${abhaNumber}`)

      const result = await abdmService.verifyAbhaNumber(abhaNumber)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            isValid: result.isValid,
            status: result.status,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            isValid: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyAbhaNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Resend OTP for ABHA operations
   * @param {Object} req - Request object
   * @returns {Object} - Response
   */
  async resendOtp(req) {
    try {
      const { txnId } = await req.json()

      // Validate required fields
      if (!txnId) {
        return jsonResponse(
          'Transaction ID is required',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Resending OTP for txnId: ${txnId}`)

      const result = await abdmService.resendOtp(txnId)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            txnId: result.txnId,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in resendOtp handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByNumber(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyOtpAndFetchAbhaDetailsByNumber(
        txnId,
        otp,
      )

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByNumber handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Verify OTP and fetch ABHA details by mobile number
   * @param {Object} req - Request object
   * @returns {Object} - Response with ABHA details
   */
  async verifyOtpAndFetchAbhaDetailsByMobile(req) {
    try {
      const { txnId, otp } = await req.json()

      // Validate required fields
      if (!txnId || !otp) {
        return jsonResponse(
          'Transaction ID and OTP are required',
          HttpStatusCode.BadRequest,
        )
      }

      // Validate OTP format (6 digits)
      if (!/^\d{6}$/.test(otp)) {
        return jsonResponse(
          'Invalid OTP format. Must be 6 digits.',
          HttpStatusCode.BadRequest,
        )
      }

      logInfo(`Verifying OTP and fetching ABHA details for txnId: ${txnId}`)

      const result = await abdmService.verifyAbhaDetailsByMobile(txnId, otp)

      if (result.success) {
        return jsonResponse(
          {
            success: true,
            data: result.data,
            message: result.message,
          },
          HttpStatusCode.Ok,
        )
      } else {
        return jsonResponse(
          {
            success: false,
            error: result.error,
            details: result.details,
          },
          HttpStatusCode.BadRequest,
        )
      }
    } catch (error) {
      logError('Error in verifyOtpAndFetchAbhaDetailsByMobile handler:', error)
      return jsonResponse(
        'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Handle ABDM webhook for Milestone 2 (HIP/HIU integration)
   * This webhook will receive consent artifacts, data requests, and transfer notifications
   * @param {Object} req - Request object
   * @param {Object} context - Azure Function context
   * @returns {Object} - Response acknowledging webhook receipt
   */
  async handleWebhook(req, context) {
    try {
      context.log('🔔 ABDM Webhook received')

      const method = req.method
      const url = req.url
      const headers = {}

      if (req.headers && typeof req.headers.forEach === 'function') {
        req.headers.forEach((value, key) => {
          headers[key] = key.toLowerCase().includes('auth')
            ? '***masked***'
            : value
        })
      }

      context.log('📋 Request details:', {
        method,
        url,
        headers: Object.keys(headers),
        timestamp: new Date().toISOString(),
      })

      if (method === 'GET') {
        context.log('✅ Webhook verification request')

        const challenge =
          req.query.get('challenge') || req.query.get('hub.challenge')

        if (challenge) {
          context.log('🔑 Returning challenge for verification')
          return new Response(challenge, {
            status: 200,
            headers: { 'Content-Type': 'text/plain' },
          })
        }

        return jsonResponse(
          {
            success: true,
            message:
              'ABDM Webhook endpoint is active and ready for Milestone 2',
            service: 'ABDM HIP/HIU Webhook',
            timestamp: new Date().toISOString(),
            supportedEvents: [
              'CONSENT_GRANTED',
              'CONSENT_REVOKED',
              'DATA_REQUEST',
              'DATA_TRANSFER_COMPLETE',
              'HEALTH_RECORD_SHARED',
            ],
          },
          HttpStatusCode.Ok,
        )
      }

      if (method === 'POST') {
        context.log('📨 Processing webhook event')

        let webhookData = {}
        try {
          webhookData = await req.json()
        } catch (error) {
          context.log('❌ Failed to parse webhook JSON:', error.message)
          return jsonResponse(
            {
              success: false,
              error: 'Invalid JSON payload',
            },
            HttpStatusCode.BadRequest,
          )
        }

        context.log('📄 Webhook payload:', JSON.stringify(webhookData, null, 2))

        const eventType =
          webhookData.eventType ||
          webhookData.event ||
          webhookData.type ||
          'UNKNOWN'
        const requestId =
          webhookData.requestId || webhookData.id || `webhook_${Date.now()}`
        const timestamp = webhookData.timestamp || new Date().toISOString()

        context.log(`🎯 Event Type: ${eventType}`)
        context.log(`🆔 Request ID: ${requestId}`)

        let processingResult = { success: true, message: 'Event received' }

        switch (eventType) {
          case 'CONSENT_GRANTED':
          case 'consent.granted':
            processingResult = this.handleConsentGranted(webhookData, context)
            break

          case 'CONSENT_REVOKED':
          case 'consent.revoked':
            processingResult = this.handleConsentRevoked(webhookData, context)
            break

          case 'DATA_REQUEST':
          case 'data.request':
            processingResult = this.handleDataRequest(webhookData, context)
            break

          case 'DATA_TRANSFER_COMPLETE':
          case 'data.transfer.complete':
            processingResult = this.handleDataTransferComplete(
              webhookData,
              context,
            )
            break

          case 'HEALTH_RECORD_SHARED':
          case 'health.record.shared':
            processingResult = this.handleHealthRecordShared(
              webhookData,
              context,
            )
            break

          default:
            context.log(`⚠️ Unknown event type: ${eventType}`)
            processingResult = {
              success: true,
              message: `Event type '${eventType}' received but not yet implemented`,
              note: 'This will be implemented in Milestone 2',
            }
        }

        return jsonResponse(
          {
            success: true,
            message: 'Webhook processed successfully',
            eventType,
            requestId,
            timestamp: new Date().toISOString(),
            processing: processingResult,
          },
          HttpStatusCode.Ok,
        )
      }

      return jsonResponse(
        {
          success: false,
          error: 'Method not allowed',
          supportedMethods: ['GET', 'POST'],
        },
        HttpStatusCode.MethodNotAllowed,
      )
    } catch (error) {
      context.log('❌ Webhook processing error:', error)
      return jsonResponse(
        {
          success: false,
          error: 'Internal server error',
          message: error.message,
          timestamp: new Date().toISOString(),
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  /**
   * Handle consent granted event (Milestone 2)
   */
  handleConsentGranted(webhookData, context) {
    context.log('✅ Processing CONSENT_GRANTED event')

    const { consentId, abhaNumber, purpose, dataTypes } =
      webhookData.data || webhookData

    context.log('📋 Consent details:', {
      consentId,
      abhaNumber: abhaNumber ? '***provided***' : 'missing',
      purpose,
      dataTypes,
    })

    // TODO: Implement consent processing logic in Milestone 2
    // - Store consent artifact
    // - Update patient consent status
    // - Prepare for data sharing

    return {
      success: true,
      message: 'Consent granted event processed',
      action: 'Ready for data sharing',
    }
  }

  /**
   * Handle consent revoked event (Milestone 2)
   */
  handleConsentRevoked(webhookData, context) {
    context.log('🚫 Processing CONSENT_REVOKED event')

    const { consentId, abhaNumber, reason } = webhookData.data || webhookData

    context.log('📋 Revocation details:', {
      consentId,
      abhaNumber: abhaNumber ? '***provided***' : 'missing',
      reason,
    })

    // TODO: Implement consent revocation logic in Milestone 2
    // - Update consent status
    // - Stop any ongoing data sharing
    // - Notify relevant systems

    return {
      success: true,
      message: 'Consent revoked event processed',
      action: 'Data sharing stopped',
    }
  }

  /**
   * Handle data request event (Milestone 2)
   */
  handleDataRequest(webhookData, context) {
    context.log('📥 Processing DATA_REQUEST event')

    const { requestId, abhaNumber, dataTypes, purpose } =
      webhookData.data || webhookData

    context.log('📋 Data request details:', {
      requestId,
      abhaNumber: abhaNumber ? '***provided***' : 'missing',
      dataTypes,
      purpose,
    })

    // TODO: Implement data request processing in Milestone 2
    // - Validate consent
    // - Prepare requested data
    // - Initiate data transfer

    return {
      success: true,
      message: 'Data request event processed',
      action: 'Preparing data for transfer',
    }
  }

  /**
   * Handle data transfer complete event (Milestone 2)
   */
  handleDataTransferComplete(webhookData, context) {
    context.log('✅ Processing DATA_TRANSFER_COMPLETE event')

    const { transferId, status, recordCount } = webhookData.data || webhookData

    context.log('📋 Transfer details:', {
      transferId,
      status,
      recordCount,
    })

    // TODO: Implement transfer completion logic in Milestone 2
    // - Update transfer status
    // - Log transfer completion
    // - Notify relevant parties

    return {
      success: true,
      message: 'Data transfer completion processed',
      action: 'Transfer logged and completed',
    }
  }

  /**
   * Handle health record shared event (Milestone 2)
   */
  handleHealthRecordShared(webhookData, context) {
    context.log('📤 Processing HEALTH_RECORD_SHARED event')

    const { recordId, abhaNumber, sharedWith, recordType } =
      webhookData.data || webhookData

    context.log('📋 Sharing details:', {
      recordId,
      abhaNumber: abhaNumber ? '***provided***' : 'missing',
      sharedWith,
      recordType,
    })

    // TODO: Implement record sharing logic in Milestone 2
    // - Log record sharing
    // - Update sharing history
    // - Notify patient if required

    return {
      success: true,
      message: 'Health record sharing processed',
      action: 'Sharing logged and tracked',
    }
  }
}

module.exports = new ABDMHandler()
