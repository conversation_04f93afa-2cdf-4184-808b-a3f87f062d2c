const featureService = require('../services/feature-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const logging = require('../common/logging')

class FeatureHandler {
  async createFeature(req, decode) {
    try {
      const featureData = await req.json()

      if (!featureData.featureName) {
        return jsonResponse('Feature name is required', HttpStatusCode.BadRequest)
      }

      const result = await featureService.createFeature(featureData, decode.oid)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Created)
    } catch (error) {
      logging.logError('Error in createFeature handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateFeature(req, decode) {
    try {
      const featureId = req.query.get('id')

      if (!featureId) {
        return jsonResponse('Feature ID is required', HttpStatusCode.BadRequest)
      }

      const featureData = await req.json()

      const result = await featureService.updateFeature(featureId, featureData, decode.oid)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in updateFeature handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deleteFeature(req, decode) {
    try {
      const featureId = req.query.get('id')

      if (!featureId) {
        return jsonResponse('Feature ID is required', HttpStatusCode.BadRequest)
      }

      const result = await featureService.deleteFeature(featureId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.message, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in deleteFeature handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deactivateFeature(req, decode) {
    try {
      const featureId = req.query.get('id')

      if (!featureId) {
        return jsonResponse('Feature ID is required', HttpStatusCode.BadRequest)
      }

      const result = await featureService.deactivateFeature(featureId, decode.oid)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in deactivateFeature handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getFeature(req, decode) {
    try {
      const featureId = req.query.get('id')

      if (!featureId) {
        return jsonResponse('Feature ID is required', HttpStatusCode.BadRequest)
      }

      const result = await featureService.getFeature(featureId)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.NotFound)
      }

      return jsonResponse(result.data, HttpStatusCode.Ok)
    } catch (error) {
      logging.logError('Error in getFeature handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getAllFeatures(req, decode) {
    try {
      const action = req.query.get('action')

      // Handle different GET actions
      if (action === 'search') {
        return await this.searchFeatures(req, decode)
      } else if (action === 'permission-keys') {
        return await this.getAllPermissionKeys(req, decode)
      } else if (action === 'by-permission') {
        return await this.getFeaturesByPermissionKey(req, decode)
      }

      // Default: get all features with optional includeInactive filter
      const includeInactive = req.query.get('includeInactive') === 'true'
      const result = await featureService.getAllFeatures(includeInactive)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          features: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getAllFeatures handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchFeatures(req, decode) {
    try {
      const featureName = req.query.get('featureName')
      const permissionKey = req.query.get('permissionKey')

      const searchParams = {}
      if (featureName) searchParams.featureName = featureName
      if (permissionKey) searchParams.permissionKey = permissionKey

      const result = await featureService.searchFeatures(searchParams)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          features: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in searchFeatures handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getFeaturesByPermissionKey(req, decode) {
    try {
      const permissionKey = req.query.get('permissionKey')

      if (!permissionKey) {
        return jsonResponse('Permission key is required', HttpStatusCode.BadRequest)
      }

      const result = await featureService.getFeaturesByPermissionKey(permissionKey)

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          features: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getFeaturesByPermissionKey handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getAllPermissionKeys(req, decode) {
    try {
      const result = await featureService.getAllPermissionKeys()

      if (!result.success) {
        return jsonResponse(result.message, HttpStatusCode.BadRequest)
      }

      return jsonResponse(
        {
          permissionKeys: result.data,
          count: result.count,
        },
        HttpStatusCode.Ok,
      )
    } catch (error) {
      logging.logError('Error in getAllPermissionKeys handler:', error)
      return jsonResponse(
        error.message || 'Internal server error',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new FeatureHandler()
