const dashboardService = require('../services/dashboard-service')
const { logError, logInfo } = require('../common/logging')

class UserDashboardHandler {

  async getDashboardData(userId) {
    try {
      logInfo(`Get dashboard data for user: ${userId}`)
      const result = await dashboardService.getDashboardData(userId)
      return result
    } catch (error) {
      logError('Error in getDashboardData handler:', error)
      throw new Error('Error fetching dashboard data', { cause: error })
    }
  }

  async getTotalPatients(userId) {
    try {
      logInfo(`Get total patients for user: ${userId}`)
      const result = await dashboardService.getTotalPatients(userId)
      return result
    } catch (error) {
      logError('Error in getTotalPatients handler:', error)
      throw new Error('Error fetching total patients', { cause: error })
    }
  }

  async getTodaysAppointments(userId) {
    try {
      logInfo(`Get today's appointments for user: ${userId}`)
      const result = await dashboardService.getTodaysAppointments(userId)
      return result
    } catch (error) {
      logError('Error in getTodaysAppointments handler:', error)
      throw new Error('Error fetching today\'s appointments', { cause: error })
    }
  }

  async getPatientQueue(userId) {
    try {
      logInfo(`Get patient queue for user: ${userId}`)
      const result = await dashboardService.getPatientQueue(userId)
      return result
    } catch (error) {
      logError('Error in getPatientQueue handler:', error)
      throw new Error('Error fetching patient queue', { cause: error })
    }
  }

  async getAverageWaitingTime(userId) {
    try {
      logInfo(`Get average waiting time for user: ${userId}`)
      const result = await dashboardService.getAverageWaitingTime(userId)
      return result
    } catch (error) {
      logError('Error in getAverageWaitingTime handler:', error)
      throw new Error('Error fetching average waiting time', { cause: error })
    }
  }

  async getUpcomingAppointments(userId, date = null, doctorId = null, patientName = null, patientId = null) {
    try {
      logInfo(`Get upcoming appointments for user: ${userId}`)
      const result = await dashboardService.getUpcomingAppointments(userId, { date, doctorId, patientName, patientId })
      return result
    } catch (error) {
      logError('Error in getUpcomingAppointments handler:', error)
      throw new Error('Error fetching upcoming appointments', { cause: error })
    }
  }
}

module.exports = new UserDashboardHandler()
