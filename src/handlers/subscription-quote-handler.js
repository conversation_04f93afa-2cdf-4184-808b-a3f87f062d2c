const emailService = require('../services/email-service')
const logging = require('../common/logging')

class SubscriptionQuoteHandler {
  async submitQuoteRequest(data) {
    try {
      const { name, email, phoneNumber, organizationName, designation, requirements } = data

      // Send email notification to admin/sales team
      await emailService.sendQuoteRequestNotification({
        name,
        email,
        phoneNumber,
        organizationName,
        designation,
        requirements
      })

      // Optionally send confirmation email to the requester
      await emailService.sendQuoteRequestConfirmation(email, name)

      logging.logInfo(`Subscription quote request submitted successfully - Email: ${email}, Organization: ${organizationName}`)

      return {
        success: true,
        message: 'Quote request submitted successfully'
      }
    } catch (error) {
      logging.logError('Error submitting quote request:', error)
      return {
        success: false,
        message: error.message || 'Failed to submit quote request'
      }
    }
  }
}

module.exports = new SubscriptionQuoteHandler()
