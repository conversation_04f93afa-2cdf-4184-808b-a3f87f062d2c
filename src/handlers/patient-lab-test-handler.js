const labTestService = require('../services/patient-lab-test-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
const rolePermissionHandler = require('./role-permission-handler')
const userService = require('../services/user-service')
const userSysRoleRepository = require('../repositories/user-sys-role-repository')

class LabTestHandler {
  async checkOcrPermission(decode) {
    try {
      if (!decode || !decode.oid) {
        return false
      }

      const user = await userService.getUserById(decode.oid)
      if (!user) {
        return false
      }

      const userSysRole = await userSysRoleRepository.getUserSysRoleByUserId(user.id)
      if (userSysRole && userSysRole.isActive && userSysRole.permissionKeys) {
        return userSysRole.permissionKeys.includes('emr.lab-master.ocr')
      }

      if (!user.roleId) {
        return false
      }

      const rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(user.roleId)
      const permissionKeys = rolePermissions?.APIs?.map(api => api.permissionKey) || []
      return permissionKeys.includes('emr.lab-master.ocr')
    } catch (error) {
      console.error('Error checking OCR permission:', error)
      return false
    }
  }

  maskFileMetadata(fileMetadata) {
    if (!fileMetadata || !Array.isArray(fileMetadata)) {
      return fileMetadata
    }

    return fileMetadata.map(file => ({
      ...file,
      ocrData: null,
      ocrResult: null,
    }))
  }

  maskLabTestResults(data, hasOcrPermission) {
    if (hasOcrPermission) {
      return data
    }

    return data.map(labTestRecord => {
      if (!labTestRecord.labTests) {
        return labTestRecord
      }

      if (Array.isArray(labTestRecord.labTests)) {
        return {
          ...labTestRecord,
          labTests: labTestRecord.labTests.map(test => ({
            ...test,
            results: null,
            reference: null,
            fileMetadata: this.maskFileMetadata(test.fileMetadata),
          })),
        }
      } else {
        const maskedLabTests = {}
        for (const [department, tests] of Object.entries(labTestRecord.labTests)) {
          maskedLabTests[department] = tests.map(test => ({
            ...test,
            results: null,
            reference: null,
            fileMetadata: this.maskFileMetadata(test.fileMetadata),
          }))
        }
        return {
          ...labTestRecord,
          labTests: maskedLabTests,
        }
      }
    })
  }

  async getLabTests(req, decode) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || null
      const sortField = req.query.get('sortField') || null
      const sortOrder = req.query.get('sortOrder') || 'asc'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null
      const searchText = req.query.get('searchText') || null
      const department = req.query.get('department') || 'ALL'

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const data = await labTestService.getLabTestsByPatient(
        patientId,
        dateFilter,
        sortField,
        sortOrder,
        customDateRange,
        searchText,
        department,
      )

      const hasOcrPermission = await this.checkOcrPermission(decode)

      const maskedData = this.maskLabTestResults(data, hasOcrPermission)

      return jsonResponse(maskedData)
    } catch (err) {
      console.error('Error fetching lab tests:', err)
      return jsonResponse(
        'Failed to fetch lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createLabTest(req) {
    try {
      const body = await req.json()
      const { patientId, labTests } = body
      if (!patientId || !Array.isArray(labTests) || labTests.length === 0) {
        return jsonResponse(
          'Invalid data for lab tests',
          HttpStatusCode.BadRequest,
        )
      }

      const result = await labTestService.createLabTestsForPatient(
        patientId,
        labTests,
      )
      return jsonResponse(result, HttpStatusCode.Created)
    } catch (err) {
      console.error('Error creating lab tests:', err)
      return jsonResponse(
        'Failed to create lab tests',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updateLabTest(req) {
    try {
      const data = await req.json()
      const result = await labTestService.updateLabTest(data)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to update lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deleteLabTest(req) {
    try {
      const id = req.query.get('id')
      const result = await labTestService.deleteLabTest(id)
      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        'Failed to delete lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getLabTestDetails(req, decode) {
    try {
      const labTestId = req.query.get('labTestId')
      if (!labTestId) {
        return jsonResponse(
          'Missing patient ID or labtest ID',
          HttpStatusCode.BadRequest,
        )
      }

      const labTest = await labTestService.getLabTestById(labTestId)

      const hasOcrPermission = await this.checkOcrPermission(decode)

      const maskedData = this.maskLabTestResults(labTest, hasOcrPermission)

      return jsonResponse(maskedData)
    } catch (err) {
      return jsonResponse(
        'Error fetching labtest details',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async searchPatientLabTest(req) {
    try {
      const body = await req.json()
      const {
        searchText = '',
        pageSize = 10,
        continuationToken = '',
        patientId = null,
      } = body

      if (!searchText.trim()) {
        return jsonResponse('Missing search text', HttpStatusCode.BadRequest)
      }

      const result = await labTestService.searchPatientLabTest(
        searchText,
        pageSize,
        continuationToken,
        patientId,
      )

      return jsonResponse(result)
    } catch (err) {
      console.error('Search lab test error:', err)
      return jsonResponse(
        'Error searching lab test',
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new LabTestHandler()
