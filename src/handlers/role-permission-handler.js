const { logInfo } = require('../common/logging')
const rolePermissionService = require('../services/role-permission-service')
const { APIPermissions } = require('../common/permissions')
const logging = require('../common/logging')

class RolePermissionHandler {
  async getAPIListbyRole(roleName) {
    logInfo(`get permission for role :: ${roleName}`)
    const data = await rolePermissionService.getAPIsListbyRole(roleName)

    if (data && data.length > 0) {
      const rolePermissions = data[0]

      if (!APIPermissions || APIPermissions.length === 0) {
        logInfo('APIPermissions is undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      if (!rolePermissions.APIs || rolePermissions.APIs.length === 0) {
        logInfo('Role APIs are undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      const permissionKeys = rolePermissions.APIs.flatMap((apiPermission) => {
        const matchingPermission = APIPermissions.find(
          (permission) =>
            permission.apis.includes(apiPermission.api) &&
            permission.methods.some((method) =>
              apiPermission.methods.includes(method),
            ),
        )
        return matchingPermission ? [matchingPermission.key] : []
      })

      return {
        ...rolePermissions,
        permissionKeys,
      }
    }
    return data
  }

  async getAPIListbyRoleAndOrganization(roleName, organizationId) {
    logInfo(
      `get permission for role :: ${roleName} and organization :: ${organizationId}`,
    )
    const data = await rolePermissionService.getAPIsListbyRoleAndOrganization(
      roleName,
      organizationId,
    )

    if (data && data.length > 0) {
      const rolePermissions = data[0]

      if (!APIPermissions || APIPermissions.length === 0) {
        logInfo('APIPermissions is undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      if (!rolePermissions.APIs || rolePermissions.APIs.length === 0) {
        logInfo('Role APIs are undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      const permissionKeys = rolePermissions.APIs.flatMap((apiPermission) => {
        const matchingPermission = APIPermissions.find(
          (permission) =>
            permission.apis.includes(apiPermission.api) &&
            permission.methods.some((method) =>
              apiPermission.methods.includes(method),
            ),
        )
        return matchingPermission ? [matchingPermission.key] : []
      })

      return {
        ...rolePermissions,
        permissionKeys,
      }
    }
    return data
  }

  async getAPIListbyRoleId(roleId) {
    logInfo(`Fetching permissions for role ID :: ${roleId}`)
    const data = await rolePermissionService.getRoleById(roleId)
    if (data) {
      const rolePermissions = data

      if (!APIPermissions || APIPermissions.length === 0) {
        logInfo('APIPermissions is undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      if (!rolePermissions.APIs || rolePermissions.APIs.length === 0) {
        logInfo('Role APIs are undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      const permissionKeys = rolePermissions.APIs.flatMap((apiPermission) => {
        const matchingPermission = APIPermissions.find(
          (permission) =>
            permission.apis.includes(apiPermission.api) &&
            permission.methods.some((method) =>
              apiPermission.methods.includes(method),
            ),
        )
        return matchingPermission ? [matchingPermission.key] : []
      })

      return {
        ...rolePermissions,
        permissionKeys,
      }
    }
    return null
  }

  async getAPIListbyRoleIdAndOrganization(roleId, organizationId) {
    logInfo(
      `Fetching permissions for role ID :: ${roleId} and organization :: ${organizationId}`,
    )
    const data = await rolePermissionService.getRoleByIdAndOrganization(
      roleId,
      organizationId,
    )

    if (data) {
      const rolePermissions = data

      if (!APIPermissions || APIPermissions.length === 0) {
        logInfo('APIPermissions is undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      if (!rolePermissions.APIs || rolePermissions.APIs.length === 0) {
        logInfo('Role APIs are undefined or empty')
        return { ...rolePermissions, permissionKeys: [] }
      }

      const permissionKeys = rolePermissions.APIs.flatMap((apiPermission) => {
        const matchingPermission = APIPermissions.find(
          (permission) =>
            permission.apis.includes(apiPermission.api) &&
            permission.methods.some((method) =>
              apiPermission.methods.includes(method),
            ),
        )
        return matchingPermission ? [matchingPermission.key] : []
      })

      return {
        ...rolePermissions,
        permissionKeys,
      }
    }
    return null
  }

  async assignPermissions(req) {
    try {
      const body = await req.json()
      const { roleId, permissions } = body

      if (!roleId || !permissions || !Array.isArray(permissions)) {
        throw new Error('Role ID and permissions array are required')
      }

      // Validate permissions against predefined permissions
      const validPermissions = permissions.filter((permission) =>
        APIPermissions.some((p) => p.key === permission),
      )

      if (validPermissions.length !== permissions.length) {
        const invalidPermissions = permissions.filter(
          (permission) => !APIPermissions.some((p) => p.key === permission),
        )
        logInfo(
          `Invalid permissions provided: ${invalidPermissions.join(', ')}`,
        )
        throw new Error('Invalid permissions provided')
      }

      const apiPermissions = validPermissions.flatMap((permissionKey) => {
        const permission = APIPermissions.find((p) => p.key === permissionKey)

        if (permission.apis.length === 0 && permission.methods.length === 0) {
          return [{ permissionKey }]
        }

        return permission.apis.map((api) => ({
          api,
          methods: permission.methods,
          permissionKey,
        }))
      })

      const existingRolePermissions = await rolePermissionService.getRoleById(
        roleId,
      )

      if (existingRolePermissions) {
        existingRolePermissions.APIs = apiPermissions
        const result = await rolePermissionService.updateRolePermission(
          existingRolePermissions,
        )
        return result
      } else {
        const newRolePermission = {
          id: roleId,
          APIs: apiPermissions,
        }
        const result = await rolePermissionService.addRolePermission(
          newRolePermission,
        )
        return result
      }
    } catch (error) {
      logInfo(`Error assigning permissions: ${error.message}`)
      throw error
    }
  }

  async validateAPIAccess(roleName, apiName, method) {
    logInfo(
      `Validating API access for role :: ${roleName}, API :: ${apiName}, Method :: ${method}`,
    )
    const rolePermissions = await this.getAPIListbyRole(roleName)

    if (!rolePermissions || !rolePermissions.APIs) {
      return false
    }

    const apiPermission = rolePermissions.APIs.find(
      (api) => api.api === apiName,
    )
    if (!apiPermission) {
      return false
    }

    return apiPermission.methods.includes(method.toUpperCase())
  }

  async getAPIListByRoleId(req) {
    try {
      const roleId = req.query.get('roleId')
      if (!roleId) {
        return { status: 400, body: { message: 'Role ID is required' } }
      }

      const rolePermissions = await rolePermissionService.getRoleById(roleId)
      if (!rolePermissions) {
        return { status: 404, body: { message: 'Role not found' } }
      }

      // Map APIs and methods back to permission keys
      const formattedAPIs = rolePermissions.APIs.map((apiPermission) => {
        // First, try to find exact permissionKey match
        let matchingPermission = APIPermissions.find(
          (permission) => permission.key === apiPermission.permissionKey,
        )

        // If no exact match, find the best matching permission
        if (!matchingPermission && apiPermission.api) {
          // Find all permissions that match the API
          const candidatePermissions = APIPermissions.filter(
            (permission) =>
              permission.apis.includes(apiPermission.api) &&
              permission.methods.some((method) =>
                apiPermission.methods?.includes(method),
              ),
          )

          // Prioritize exact method match over partial match
          matchingPermission = candidatePermissions.find((permission) => {
            const permMethodsSorted = JSON.stringify(
              [...permission.methods].sort(),
            )
            const apiMethodsSorted = JSON.stringify(
              [...(apiPermission.methods || [])].sort(),
            )
            return permMethodsSorted === apiMethodsSorted
          })

          // If no exact match, use the first candidate (fallback)
          if (!matchingPermission && candidatePermissions.length > 0) {
            matchingPermission = candidatePermissions[0]
          }
        }

        return {
          api: apiPermission.api || null,
          methods: apiPermission.methods || [],
          key: matchingPermission
            ? matchingPermission.key
            : apiPermission.permissionKey || null,
        }
      })

      // Include permissions without APIs
      const permissionsWithoutAPIs = APIPermissions.filter(
        (permission) =>
          !permission.apis.length &&
          rolePermissions.APIs.some(
            (apiPermission) => apiPermission.permissionKey === permission.key,
          ),
      ).map((permission) => ({
        api: null,
        methods: [],
        key: permission.key,
      }))

      // Combine and filter unique permissions
      const combinedPermissions = [...formattedAPIs, ...permissionsWithoutAPIs]
      const uniquePermissions = combinedPermissions.filter(
        (permission, index, self) =>
          index ===
          self.findIndex(
            (p) =>
              p.key === permission.key &&
              p.api === permission.api &&
              JSON.stringify(p.methods) === JSON.stringify(permission.methods),
          ),
      )

      return {
        status: 200,
        body: { data: uniquePermissions },
      }
    } catch (error) {
      logInfo(`Error fetching API list by role ID: ${error.message}`)
      return { status: 500, body: { message: 'Error fetching API list' } }
    }
  }
}

module.exports = new RolePermissionHandler()
