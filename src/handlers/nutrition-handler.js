const nutritionService = require('../services/nutrition-service');
const { logError, logInfo } = require('../common/logging');
const { NutritionMetric, MacroMetric, MicroMetric, AdditionalMetric, MacroChartMetric } = require('../common/constant');

class NutritionHandler {

    async getFoodNames(searchTerm = '') {
        try {
            if (searchTerm && searchTerm.trim().length > 0) {
                logInfo(`Get food list starting with: ${searchTerm}`);
            } else {
                logInfo(`Get all food list`);
            }
            var result = await nutritionService.getFoodNames(searchTerm);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getServingUnitByFoodName(foodName) {
        try {
            logInfo(`Get serving unit for food: ${foodName}`);
            var result = await nutritionService.getServingUnitByFoodName(foodName);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getNutritionSummary(patientId, filter, metrics, sort) {
        try {
            logInfo(`Get nutrition summary for patient: ${patientId}, filter: ${filter}, metrics: ${metrics?.join(', ') || 'specified metrics only'}, sort: ${sort}`);
            
            let targetMetrics = metrics;
            if (metrics && metrics.length === 1) {
                if (metrics[0] === 'micro') {
                    targetMetrics = Object.values(MicroMetric);
                } else if (metrics[0] === 'macro') {
                    targetMetrics = Object.values(MacroMetric);
                }
            }
            
            var result = await nutritionService.getNutritionSummary(patientId, filter, targetMetrics, sort);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getNutritionAverage(patientId, filter, metrics) {
        try {
            logInfo(`Get nutrition average for patient: ${patientId}, filter: ${filter}, metrics: ${metrics?.join(', ') || 'all available metrics'}`);
            
            const defaultMetrics = [
                NutritionMetric.CALORIES, NutritionMetric.CARBS, NutritionMetric.PROTEIN, 
                NutritionMetric.FAT, NutritionMetric.FIBER, NutritionMetric.SUGAR, NutritionMetric.SALT, NutritionMetric.OIL
            ];
            const targetMetrics = metrics && metrics.length > 0 ? metrics : defaultMetrics;
            
            var result = await nutritionService.getNutritionAverage(patientId, filter, targetMetrics);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getNutritionChart(patientId, filter, metric) {
        try {
            logInfo(`Get nutrition chart for patient: ${patientId}, filter: ${filter}, metric: ${metric}`);
            
            
            let targetMetrics = [metric];
            if (metric === 'micro') {
                targetMetrics = Object.values(MicroMetric);
            } else if (metric === 'macro') {
                targetMetrics = Object.values(MacroChartMetric);
            } else if (metric === 'additional') {
                targetMetrics = Object.values(AdditionalMetric);
            }
            
            var result = await nutritionService.getNutritionChart(patientId, filter, targetMetrics);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

    async getNutritionPercentageChart(patientId, filter) {
        try {
            logInfo(`Get nutrition percentage chart for patient: ${patientId}, filter: ${filter}`);
            var result = await nutritionService.getNutritionPercentageChart(patientId, filter);
            return result;
        } catch (error) {
            logError(error);
            return null;
        }
    }

}

module.exports = new NutritionHandler();
