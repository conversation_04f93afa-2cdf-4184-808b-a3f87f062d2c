const { parseJ<PERSON><PERSON> } = require("../common/helper");
const { logInfo } = require("../common/logging");
const nurseService = require("../services/nurse-service");

class NurseHandler {

    async createNurse(nurse, created_by) {
        if (!nurse.id) {
            nurse.id = generateNurseId();
        }
        logInfo(`Create Nurse profile: ${JSON.parse(nurse)}`);        
        nurse.created_by = created_by;
        nurse.updated_by = created_by;
        var data = await nurseService.createNurse(nurse);
        return data;
    }

    async updateNurse(nurse, updated_by) {
        logInfo(`Update Nurse : ${JSON.parse(nurse)}`);
        doctorpatients.updated_by = updated_by;
        var result = await nurseService.updateNurse(nurse);
        return result;
    }

    async getNurseById(id) {
        logInfo(`Get Nurse by id: ${id}`);
        var data = await nurseService.getNurse(id);
        return data;
    }

    async getNurseByEmail(email) {
        logInfo(`Get Nurse by email : ${email}`);
        var query = `SELECT * FROM c WHERE c.email = '${email}'`;
        var result = await nurseService.queryNurses(query, 5, null);
        return result;
    }

    async getAllNurse(pageSize, continueToken) {
        logInfo(`Get all Nurse`);
        var data = await nurseService.getAllNurses(pageSize, continueToken);
        return data
    }
}

function generateNurseId() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let nurseId = '';
    for (let i = 0; i < 8; i++) {
        nurseId += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return nurseId;
}

module.exports = new NurseHandler();