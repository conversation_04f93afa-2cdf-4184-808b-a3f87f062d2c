const openAIService = require('../services/openai-service')
const logging = require('../common/logging')
const secretManager = require('../services/secret-manager')

class SummaryHandler {
  async identifySpeaker(transctipt) {
    try {
      var classification =
        'You are an AI assistant that helps doctors separate chat transcripts to identify which parts are spoken by the doctor and which by the patient.\n\n' +
        'CRITICAL REQUIREMENTS:\n' +
        '- Return ONLY valid, complete JSON - no markdown, explanations, or other text\n' +
        '- Process EVERY SINGLE message in the conversation\n' +
        '- NEVER truncate or stop mid-conversation\n' +
        '- Complete the ENTIRE JSON array no matter how long\n' +
        '- Handle repetitive, fragmented, or corrupted text gracefully\n' +
        '- Continue processing until the very end of the input\n\n' +
        'Format: [{"speaker": "doctor", "message": "exact message text"},{"speaker": "patient", "message": "exact message text"}]\n\n' +
        'Rules:\n' +
        '1. Each message must be a complete sentence or thought\n' +
        '2. Escape all quotes inside message content using \\" \n' +
        '3. Do not break messages in the middle of sentences\n' +
        '4. Return complete JSON array with ALL conversation messages\n' +
        '5. If no clear speaker identification is possible, use "patient" as default\n' +
        '6. Ensure all JSON brackets and braces are properly closed\n' +
        '7. Each object must have both "speaker" and "message" properties\n' +
        '8. Process the ENTIRE input text - do not skip any part\n' +
        '9. For repetitive content, include each instance as separate messages\n' +
        '10. For fragmented text, combine fragments into coherent messages when possible\n' +
        '11. Skip completely unintelligible fragments but process everything else\n\n' +
        'SPECIAL HANDLING FOR COMPLEX INPUTS:\n' +
        '- If the input contains repetitive conversations, process each repetition\n' +
        '- If text appears fragmented or corrupted, do your best to extract meaningful messages\n' +
        '- Continue processing until you reach the absolute end of the input text\n' +
        '- Your JSON array must end with a complete object, never an incomplete one\n\n' +
        'MANDATORY: Your response must include EVERY processable message from the conversation. Count the messages in the input and ensure your JSON array has the same number of objects. Do not stop until you have processed every single message. The last object in your array must be complete with both "speaker" and "message" properties.\n\n' +
        'Return only the complete JSON array with ALL messages, nothing else.'
      var result = await openAIService.chatCompletion(
        classification,
        transctipt,
      )
      return result
    } catch (error) {
      logging.logError('Unable to identify Speaker', error)
      return null
    }
  }

  async sumnmaryConversation(conversation) {
    try {
      const summaryInfo = await secretManager.getSecret('SummaryInfo')
      var classification =
        "In a clinic at a hospital in the afternoon, a doctor is conducting a routine examination for a patient. The doctor inquires about the patient's personal medical history, family medical history, daily lifestyle habits, physical examination, and clinical assessments.\n\nCRITICAL: You must return ONLY valid JSON. Do not wrap the response in markdown code blocks (```json). Do not include any text before or after the JSON. Start your response directly with { and end with }.\n\nOutput Request: From the conversation below, please analyze and return detailed information in JSON format for the following items: " +
        summaryInfo +
        ', vitals, anthropometry, generalphysicalexamination, heent, systemicexamination. The output must strictly follow the items mentioned above, and must include all the items listed.\n\nFor the following fields, use HTML unordered list format with <ul><li> tags: ' +
        summaryInfo +
        '\n\nIMPORTANT: For temperature in vitals, extract both the numeric value and the unit (Celsius, Fahrenheit, C, F) if mentioned. Store temperature as an object with "value" and "unit" properties. If no unit is mentioned, assume Celsius. Example: {"temperature": {"value": 98.6, "unit": "Fahrenheit"}} or {"temperature": {"value": 37, "unit": "Celsius"}}.' +
        ", heent.\n\nFor systemicexamination, return as an object with these sub-fields in HTML unordered list format: neurologicalExamination, cardiovascularExamination, respiratoryExamination, abdomenExamination, rheumatologicalExamination.\n\nFor vitals, return as an object with numeric values: heartRate, systolicPressure, diastolicPressure, respiratoryRate, spO2, temperature.\n\nFor anthropometry, return as an object with: height (string), weight (string), bmi (numeric), waistCircumference (string).\n\nFor generalphysicalexamination, return as an object with boolean values and notes: pallor, icterus, cyanosis, clubbing, pedalEnema, pedalEnemaNotes, lymphadenopathy, lymphadenopathyNotes.\n\nIf there is no information available for any field, return appropriate default values (empty strings for text, 0 for numbers, false for booleans, empty objects for objects).\n\nIMPORTANT: Ensure all string values are properly escaped. Use double quotes for all JSON keys and string values. Do not truncate the response - complete the entire JSON structure.\n\nAdditionally:\nAny medical conditions, diagnoses, or terms must be identified and classified using their respective codes from the International Classification of Diseases (ICD). You do not need to show the note that where you got the information from ICD data, for example: 'Hypertension (ICD-10: I10)' then you do not show '(ICD-10: I10)'.\nThe result response must be translated to English only."
      var result = await openAIService.chatCompletion(
        classification,
        conversation,
      )
      return result
    } catch (error) {
      logging.logError(error)
      return null
    }
  }
}

module.exports = new SummaryHandler()
