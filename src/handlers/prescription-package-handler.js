const packageService = require('../services/prescription-package-service')
const { HttpStatusCode } = require('axios')
const { jsonResponse } = require('../common/helper')
class PackageHandler {
  async getPackagesByType(req) {
    try {
      const url = new URL(req.url)
      const type = url.searchParams.get('type')
      const userId = url.searchParams.get('userId')

      if (!type) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Package type is required',
              details: { field: 'type', value: null },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const result = await packageService.getPrescriptionPackages(type, userId)

      if (!result) {
        return jsonResponse(result, HttpStatusCode.InternalServerError)
      }

      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Error fetching packages',
          },
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async getPackageById(req) {
    try {
      const url = new URL(req.url)
      const id = url.searchParams.get('id')
      const organizationId = url.searchParams.get('organizationId')

      if (!id) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'ID is required',
              details: { field: 'id', value: null },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const result = await packageService.getPrescriptionPackageById(
        id,
        organizationId,
      )

      if (!result.success) {
        const statusCode =
          result.error?.code === 'PRESCRIPTION_PACKAGE_NOT_FOUND'
            ? HttpStatusCode.NotFound
            : HttpStatusCode.InternalServerError
        return jsonResponse(result, statusCode)
      }

      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Error fetching package',
          },
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async createPackage(req) {
    try {
      const body = await req.json()
      const { name, type, description, medicineIds, userId } = body

      if (!name || !type) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Package name and type are required',
              details: {
                field: !name ? 'name' : 'type',
                value: !name ? name : type,
              },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const result = await packageService.createPrescriptionPackage(
        { name, type, description, medicineIds },
        userId,
      )

      if (!result.success) {
        let statusCode = HttpStatusCode.InternalServerError
        if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = HttpStatusCode.BadRequest
        } else if (result.error?.code === 'DUPLICATE_PACKAGE_NAME') {
          statusCode = HttpStatusCode.Conflict
        } else if (result.error?.code === 'DEPARTMENT_NOT_FOUND') {
          statusCode = HttpStatusCode.NotFound
        }
        return jsonResponse(result, statusCode)
      }

      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Error creating package',
          },
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async updatePackage(req) {
    try {
      const url = new URL(req.url)
      const id = url.searchParams.get('id')
      if (!id) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'ID is required',
              details: { field: 'id', value: null },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const body = await req.json()
      const { name, medicineIds, userId } = body

      // At least one field must be provided for update
      if (!name && !medicineIds) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message:
                'At least one field (name or medicineIds) must be provided for update',
              details: { field: 'name,medicineIds', value: null },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      // Validate medicineIds if provided
      if (medicineIds && !Array.isArray(medicineIds)) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'Medicine IDs must be an array',
              details: { field: 'medicineIds', value: medicineIds },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const result = await packageService.updatePrescriptionPackage(
        id,
        { name, medicineIds },
        userId,
      )

      if (!result.success) {
        let statusCode = HttpStatusCode.InternalServerError
        if (result.error?.code === 'PRESCRIPTION_PACKAGE_NOT_FOUND') {
          statusCode = HttpStatusCode.NotFound
        } else if (result.error?.code === 'VALIDATION_ERROR') {
          statusCode = HttpStatusCode.BadRequest
        } else if (result.error?.code === 'DUPLICATE_PACKAGE_NAME') {
          statusCode = HttpStatusCode.Conflict
        }
        return jsonResponse(result, statusCode)
      }

      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Error updating package',
          },
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }

  async deletePackage(req) {
    try {
      const url = new URL(req.url)
      const id = url.searchParams.get('id')
      if (!id) {
        return jsonResponse(
          {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: 'ID is required',
              details: { field: 'id', value: null },
            },
          },
          HttpStatusCode.BadRequest,
        )
      }

      const decode = req.extraInputs?.get('decode') || {}
      const userId = decode.sub || decode.userId

      const result = await packageService.deletePrescriptionPackage(id, userId)

      if (!result.success) {
        let statusCode = HttpStatusCode.InternalServerError
        if (result.error?.code === 'PRESCRIPTION_PACKAGE_NOT_FOUND') {
          statusCode = HttpStatusCode.NotFound
        } else if (result.error?.code === 'PACKAGE_IN_USE') {
          statusCode = HttpStatusCode.Conflict
        }
        return jsonResponse(result, statusCode)
      }

      return jsonResponse(result)
    } catch (err) {
      return jsonResponse(
        {
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Error deleting package',
          },
        },
        HttpStatusCode.InternalServerError,
      )
    }
  }
}

module.exports = new PackageHandler()
