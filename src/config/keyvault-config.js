/**
 * Azure Key Vault Configuration Service
 * Handles Key Vault connections and secret retrieval using Managed Identity
 */

const { SecretClient } = require('@azure/keyvault-secrets')
const { DefaultAzureCredential } = require('@azure/identity')

// Use console.log to avoid circular dependency with logging module
const logInfo = (message, ...args) => console.log(`[INFO] ${message}`, ...args)
const logError = (message, ...args) =>
  console.error(`[ERROR] ${message}`, ...args)

class KeyVaultConfig {
  constructor() {
    this.client = null
    this.keyVaultUrl = null
    this.isInitialized = false
    this.initializationPromise = null
  }

  /**
   * Initialize Key Vault client with Managed Identity
   */
  async initialize() {
    if (this.initializationPromise) {
      return this.initializationPromise
    }

    this.initializationPromise = this._doInitialize()
    return this.initializationPromise
  }

  async _doInitialize() {
    try {
      // Get Key Vault URL from environment variable
      this.keyVaultUrl = process.env.KEYVAULT_URL

      if (!this.keyVaultUrl) {
        throw new Error('KEYVAULT_URL environment variable is not set')
      }

      logInfo(`Initializing Key Vault client for: ${this.keyVaultUrl}`)

      // Create credential using DefaultAzureCredential (supports Managed Identity)
      const credential = new DefaultAzureCredential()

      // Create Key Vault client
      this.client = new SecretClient(this.keyVaultUrl, credential)

      // Test the connection by attempting to get a dummy secret (this will fail but validates auth)
      try {
        await this.client.getSecret('connection-test')
      } catch (error) {
        // Expected to fail for non-existent secret, but should not fail due to auth issues
        if (error.code === 'SecretNotFound') {
          logInfo(
            'Key Vault connection test successful (secret not found is expected)',
          )
        } else if (
          error.code === 'Forbidden' ||
          error.code === 'Unauthorized'
        ) {
          throw new Error(
            `Key Vault authentication failed: ${error.message}. Please ensure Managed Identity has proper permissions.`,
          )
        } else {
          logInfo(
            `Key Vault connection established (test error: ${error.code})`,
          )
        }
      }

      this.isInitialized = true
      logInfo('Key Vault client initialized successfully')
    } catch (error) {
      logError('Failed to initialize Key Vault client:', error)
      this.isInitialized = false
      throw error
    }
  }

  /**
   * Get a secret from Key Vault
   * @param {string} secretName - Name of the secret in Key Vault
   * @returns {Promise<string>} - Secret value
   */
  async getSecret(secretName) {
    try {
      if (!this.isInitialized) {
        await this.initialize()
      }

      if (!this.client) {
        throw new Error('Key Vault client is not initialized')
      }

      logInfo(`Retrieving secret: ${secretName}`)
      const secret = await this.client.getSecret(secretName)

      if (!secret || !secret.value) {
        throw new Error(`Secret '${secretName}' not found or has no value`)
      }

      return secret.value
    } catch (error) {
      logError(`Failed to retrieve secret '${secretName}':`, error)
      throw error
    }
  }

  /**
   * Get multiple secrets at once
   * @param {string[]} secretNames - Array of secret names
   * @returns {Promise<Object>} - Object with secret names as keys and values
   */
  async getSecrets(secretNames) {
    try {
      if (!this.isInitialized) {
        await this.initialize()
      }

      const secrets = {}
      const promises = secretNames.map(async (secretName) => {
        try {
          const value = await this.getSecret(secretName)
          secrets[secretName] = value
        } catch (error) {
          logError(`Failed to retrieve secret '${secretName}':`, error)
          secrets[secretName] = null
        }
      })

      await Promise.all(promises)
      return secrets
    } catch (error) {
      logError('Failed to retrieve multiple secrets:', error)
      throw error
    }
  }

  /**
   * Check if Key Vault is available and accessible
   * @returns {Promise<boolean>}
   */
  async isAvailable() {
    try {
      if (!this.isInitialized) {
        await this.initialize()
      }
      return true
    } catch (error) {
      logError('Key Vault is not available:', error)
      return false
    }
  }

  /**
   * Get Key Vault URL
   * @returns {string}
   */
  getKeyVaultUrl() {
    return this.keyVaultUrl
  }
}

// Export singleton instance
module.exports = new KeyVaultConfig()
