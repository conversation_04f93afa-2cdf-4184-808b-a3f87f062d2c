const { DefaultAzureCredential } = require('@azure/identity')
const { SecretClient } = require('@azure/keyvault-secrets')

async function initializeCriticalSecrets() {
  const keyVaultUrl = process.env.KEYVAULT_URL
  if (!keyVaultUrl) {
    console.log('[EARLY-INIT] KEYVAULT_URL not set — skipping Key Vault init')
    return
  }

  console.log(`[EARLY-INIT] Connecting to Key Vault: ${keyVaultUrl}`)
  const client = new SecretClient(keyVaultUrl, new DefaultAzureCredential())

  try {
    console.log('[EARLY-INIT] Retrieving "AzureWebJobsStorage" secret...')
    const secret = await client.getSecret('AzureWebJobsStorage')

    if (secret?.value) {
      process.env.AzureWebJobsStorage = secret.value
      console.log('[EARLY-INIT] ✅ AzureWebJobsStorage loaded successfully')
    } else {
      console.warn('[EARLY-INIT] ⚠️ Secret value empty — check Key Vault setup')
    }
  } catch (err) {
    console.error(
      '[EARLY-INIT] ❌ Failed to get AzureWebJobsStorage:',
      err.message,
    )

    if (process.env.AzureWebJobsStorage) {
      console.log(
        '[EARLY-INIT] Using existing environment variable as fallback',
      )
    } else {
      console.warn(
        '[EARLY-INIT] Missing AzureWebJobsStorage — timer functions may fail',
      )
    }
  }

  console.log('[EARLY-INIT] Initialization complete')
}

// Run automatically if KEYVAULT_URL is present
if (process.env.KEYVAULT_URL) {
  initializeCriticalSecrets().catch((err) =>
    console.error('[EARLY-INIT] Unhandled init error:', err.message),
  )
}

module.exports = { initializeCriticalSecrets }
