const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class OrganizationModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.name = data.name || ''
    this.contactEmail = data.contactEmail || ''
    this.contactPersonName = data.contactPersonName || ''
    this.contactPhone = data.contactPhone || ''
    this.phoneNumber = data.phoneNumber || data.contactPhone || '' // Support both fields

    // Enhanced address structure with pincode, city, state, country
    this.address = data.address || {
      street: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
    }

    // New fields for subscriber management
    this.pan = data.pan || '' // Permanent Account Number (Indian tax ID)
    this.gstin = data.gstin || '' // GST Identification Number

    this.description = data.description || ''
    this.isActive = data.isActive !== undefined ? data.isActive : true
    this.registrationFee =
      data.registrationFee !== undefined ? data.registrationFee : 0
    this.createdAt = data.createdAt || new Date().toISOString()
    this.updatedAt = data.updatedAt || new Date().toISOString()

    // Validate required fields
    if (!this.name) {
      throw new Error('Organization name is required.')
    }
    if (!this.contactEmail) {
      throw new Error('Contact email is required.')
    }
    if (!this.contactPersonName) {
      throw new Error('Contact person name is required.')
    }
  }

  // Validation method for PAN and GSTIN
  validate() {
    const errors = []

    // PAN validation (optional field, but if provided must be valid)
    if (this.pan && this.pan.trim() !== '') {
      const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/
      if (!panRegex.test(this.pan.toUpperCase())) {
        errors.push('Invalid PAN format (should be like **********)')
      }
    }

    // GSTIN validation (optional field, but if provided must be valid)
    if (this.gstin && this.gstin.trim() !== '') {
      const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
      if (!gstinRegex.test(this.gstin.toUpperCase())) {
        errors.push('Invalid GSTIN format')
      }
    }

    // Pincode validation (optional)
    if (this.address && this.address.pincode && this.address.pincode.trim() !== '') {
      const pincodeRegex = /^[0-9]{6}$/
      if (!pincodeRegex.test(this.address.pincode)) {
        errors.push('Invalid pincode format (should be 6 digits)')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      contactEmail: this.contactEmail,
      contactPersonName: this.contactPersonName,
      contactPhone: this.contactPhone,
      phoneNumber: this.phoneNumber, 
      address: this.address,
      pan: this.pan ? this.pan.toUpperCase() : '',
      gstin: this.gstin ? this.gstin.toUpperCase() : '',
      description: this.description,
      isActive: this.isActive,
      registrationFee: this.registrationFee,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      created_by: this.created_by,
      created_on: this.created_on,
      updated_by: this.updated_by,
      updated_on: this.updated_on,
    }
  }
}

module.exports = OrganizationModel
