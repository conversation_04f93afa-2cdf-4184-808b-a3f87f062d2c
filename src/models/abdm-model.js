/**
 * ABDM Models for ABHA number operations
 * Contains request/response models and validation schemas
 */

class ABDMInitiateAadhaarRequest {
  constructor(data) {
    this.aadhaar = data.aadhaar || ''
    this.mobile = data.mobile || null
  }

  validate() {
    const errors = []

    if (!this.aadhaar) {
      errors.push('Aadhaar number is required')
    } else if (!/^\d{12}$/.test(this.aadhaar)) {
      errors.push('Invalid Aadhaar number format. Must be 12 digits.')
    }

    if (this.mobile && !/^\d{10}$/.test(this.mobile)) {
      errors.push('Invalid mobile number format. Must be 10 digits.')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMInitiateMobileRequest {
  constructor(data) {
    this.mobile = data.mobile || ''
  }

  validate() {
    const errors = []

    if (!this.mobile) {
      errors.push('Mobile number is required')
    } else if (!/^\d{10}$/.test(this.mobile)) {
      errors.push('Invalid mobile number format. Must be 10 digits.')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMVerifyOtpRequest {
  constructor(data) {
    this.txnId = data.txnId || ''
    this.otp = data.otp || ''
  }

  validate() {
    const errors = []

    if (!this.txnId) {
      errors.push('Transaction ID is required')
    }

    if (!this.otp) {
      errors.push('OTP is required')
    } else if (!/^\d{6}$/.test(this.otp)) {
      errors.push('Invalid OTP format. Must be 6 digits.')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMCompleteCreationRequest {
  constructor(data) {
    this.txnId = data.txnId || ''
    this.profileData = data.profileData || {}
  }

  validate() {
    const errors = []

    if (!this.txnId) {
      errors.push('Transaction ID is required')
    }

    if (!this.profileData || typeof this.profileData !== 'object') {
      errors.push('Profile data is required')
    } else {
      const requiredFields = ['firstName', 'lastName', 'gender', 'yearOfBirth']
      for (const field of requiredFields) {
        if (!this.profileData[field]) {
          errors.push(`${field} is required in profile data`)
        }
      }

      // Validate gender
      if (this.profileData.gender && !['M', 'F', 'O'].includes(this.profileData.gender)) {
        errors.push('Gender must be M (Male), F (Female), or O (Other)')
      }

      // Validate year of birth
      if (this.profileData.yearOfBirth) {
        const currentYear = new Date().getFullYear()
        const year = parseInt(this.profileData.yearOfBirth)
        if (isNaN(year) || year < 1900 || year > currentYear) {
          errors.push('Invalid year of birth')
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMGetDetailsByNumberRequest {
  constructor(data) {
    this.abhaNumber = data.abhaNumber || ''
  }

  validate() {
    const errors = []

    if (!this.abhaNumber) {
      errors.push('ABHA number is required')
    } else if (!/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(this.abhaNumber) && !/^\d{14}$/.test(this.abhaNumber)) {
      errors.push('Invalid ABHA number format')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMGetDetailsByMobileRequest {
  constructor(data) {
    this.mobile = data.mobile || ''
  }

  validate() {
    const errors = []

    if (!this.mobile) {
      errors.push('Mobile number is required')
    } else if (!/^\d{10}$/.test(this.mobile)) {
      errors.push('Invalid mobile number format. Must be 10 digits.')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMVerifyNumberRequest {
  constructor(data) {
    this.abhaNumber = data.abhaNumber || ''
  }

  validate() {
    const errors = []

    if (!this.abhaNumber) {
      errors.push('ABHA number is required')
    } else if (!/^\d{2}-\d{4}-\d{4}-\d{4}$/.test(this.abhaNumber) && !/^\d{14}$/.test(this.abhaNumber)) {
      errors.push('Invalid ABHA number format')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

class ABDMResendOtpRequest {
  constructor(data) {
    this.txnId = data.txnId || ''
  }

  validate() {
    const errors = []

    if (!this.txnId) {
      errors.push('Transaction ID is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// Response Models
class ABDMResponse {
  constructor(success, data = null, message = '', error = null, details = null) {
    this.success = success
    this.data = data
    this.message = message
    this.timestamp = new Date().toISOString()
    
    if (error) {
      this.error = error
    }
    
    if (details) {
      this.details = details
    }
  }
}

class ABDMInitiateResponse extends ABDMResponse {
  constructor(success, txnId = null, message = '', error = null, details = null) {
    super(success, null, message, error, details)
    if (txnId) {
      this.txnId = txnId
    }
  }
}

class ABDMCompleteCreationResponse extends ABDMResponse {
  constructor(success, abhaNumber = null, abhaAddress = null, message = '', error = null, details = null) {
    super(success, null, message, error, details)
    if (abhaNumber) {
      this.abhaNumber = abhaNumber
    }
    if (abhaAddress) {
      this.abhaAddress = abhaAddress
    }
  }
}

class ABDMVerificationResponse extends ABDMResponse {
  constructor(success, isValid = false, status = null, message = '', error = null, details = null) {
    super(success, null, message, error, details)
    this.isValid = isValid
    if (status) {
      this.status = status
    }
  }
}

// Validation utility
class ABDMValidator {
  static validateRequest(requestClass, data) {
    const request = new requestClass(data)
    return request.validate()
  }

  static formatValidationErrors(errors) {
    return {
      success: false,
      error: 'Validation failed',
      details: {
        errors: errors
      }
    }
  }
}

module.exports = {
  ABDMInitiateAadhaarRequest,
  ABDMInitiateMobileRequest,
  ABDMVerifyOtpRequest,
  ABDMCompleteCreationRequest,
  ABDMGetDetailsByNumberRequest,
  ABDMGetDetailsByMobileRequest,
  ABDMVerifyNumberRequest,
  ABDMResendOtpRequest,
  ABDMResponse,
  ABDMInitiateResponse,
  ABDMCompleteCreationResponse,
  ABDMVerificationResponse,
  ABDMValidator
}
