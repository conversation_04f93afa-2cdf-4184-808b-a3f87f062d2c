const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

class LifestyleRecordHistoryModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.transcript = data.transcript || ''
    this.source = data.source || ''
    this.userId = data.userId || ''
    this.patientId = data.patientId || ''
    this.processedAt = data.processedAt || new Date().toISOString()
    this.status = data.status || 'pending' // 'pending', 'completed', 'failed'
    this.type = data.type || 'lifestyle-ambient-listening'
    this.retryCount = data.retryCount || 0
    this.lastRetryAt = data.lastRetryAt || null
    this.errorMessage = data.errorMessage || null

    // Validate required fields
    if (!this.transcript) {
      throw new Error('Transcript is required for lifestyle record history.')
    }
    if (!this.source) {
      throw new Error('Source is required for lifestyle record history.')
    }
    if (!this.userId) {
      throw new Error('User ID is required for lifestyle record history.')
    }
    if (!this.patientId) {
      throw new Error('Patient ID is required for lifestyle record history.')
    }
  }

  /**
   * Convert to plain object for storage
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      transcript: this.transcript,
      source: this.source,
      userId: this.userId,
      patientId: this.patientId,
      processedAt: this.processedAt,
      status: this.status,
      type: this.type,
      retryCount: this.retryCount,
      lastRetryAt: this.lastRetryAt,
      errorMessage: this.errorMessage,
      created_by: this.created_by,
      updated_by: this.updated_by,
      created_on: this.created_on,
      updated_on: this.updated_on
    }
  }
}

module.exports = LifestyleRecordHistoryModel
