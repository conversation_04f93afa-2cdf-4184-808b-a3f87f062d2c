const CosmosDbMetadata = require('./CosmosDb-Metadata-model');

class MedicalHistoryAddictionModel extends CosmosDbMetadata {
  constructor(data) {
    super(data);
    this.patientId = data.patientId || '';
    this.diagnosis = data.diagnosis || [];
    this.smoking = data.smoking || { history: 'no' };
    this.alcohol = data.alcohol || { history: 'no' };
    this.tobacco = data.tobacco || { history: 'no' };
    this.drugs = data.drugs || { history: 'no' };
    this.nicotineDependenceTest = data.nicotineDependenceTest || null;
  }
}

module.exports = MedicalHistoryAddictionModel;