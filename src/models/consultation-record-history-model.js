const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

class ConsultationRecordHistoryModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.transcript = data.transcript || ''
    this.userId = data.userId || ''
    this.patientId = data.patientId || ''
    this.processedAt = data.processedAt || new Date().toISOString()
    this.status = data.status || 'pending' // 'pending', 'completed', 'failed'
    this.type = data.type || 'consultation-summary'
    this.retryCount = data.retryCount || 0
    this.lastRetryAt = data.lastRetryAt || null
    this.errorMessage = data.errorMessage || null

    // Validate required fields
    if (!this.transcript) {
      throw new Error('Transcript is required for consultation record history.')
    }
    if (!this.userId) {
      throw new Error('User ID is required for consultation record history.')
    }
    if (!this.patientId) {
      throw new Error('Patient ID is required for consultation record history.')
    }
  }

  /**
   * Convert to plain object for storage
   * @returns {Object} Plain object representation
   */
  toObject() {
    return {
      id: this.id,
      transcript: this.transcript,
      userId: this.userId,
      patientId: this.patientId,
      processedAt: this.processedAt,
      status: this.status,
      type: this.type,
      retryCount: this.retryCount,
      lastRetryAt: this.lastRetryAt,
      errorMessage: this.errorMessage,
      created_by: this.created_by,
      updated_by: this.updated_by,
      created_on: this.created_on,
      updated_on: this.updated_on
    }
  }

  /**
   * Mark record as completed
   */
  markCompleted() {
    this.status = 'completed'
    this.processedAt = new Date().toISOString()
    this.updated_on = new Date().toISOString()
  }

  /**
   * Mark record as failed
   * @param {string} errorMessage - Error message
   */
  markFailed(errorMessage) {
    this.status = 'failed'
    this.errorMessage = errorMessage
    this.updated_on = new Date().toISOString()
  }

  /**
   * Increment retry count
   */
  incrementRetry() {
    this.retryCount += 1
    this.lastRetryAt = new Date().toISOString()
    this.updated_on = new Date().toISOString()
  }
}

module.exports = ConsultationRecordHistoryModel
