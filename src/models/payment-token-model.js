const CosmosDbMetadata = require('./CosmosDb-Metadata-model')
const { v4: uuidv4 } = require('uuid')

/**
 * Payment Token Model
 * Stores tokenized payment references without sensitive data
 */
class PaymentTokenModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    
    this.id = data.id || uuidv4()
    this.paymentId = data.paymentId || ''
    this.organizationId = data.organizationId || ''
    
    // Tokenized payment reference - no sensitive data
    this.paymentReferenceToken = data.paymentReferenceToken || ''
    this.sessionToken = data.sessionToken || ''
    
    // Payment metadata (non-sensitive)
    this.amount = data.amount || 0
    this.currency = data.currency || 'INR'
    this.paymentType = data.paymentType || ''
    this.status = data.status || 'created'
    
    // Token metadata
    this.tokenCreatedAt = data.tokenCreatedAt || new Date().toISOString()
    this.tokenExpiresAt = data.tokenExpiresAt || null
    this.tokenUsageCount = data.tokenUsageCount || 0
    this.maxTokenUsage = data.maxTokenUsage || 1
    
    // Security flags
    this.isTokenActive = data.isTokenActive !== undefined ? data.isTokenActive : true
    this.tokenRevoked = data.tokenRevoked || false
    this.revokedAt = data.revokedAt || null
    this.revokedReason = data.revokedReason || null
    
    // Audit trail
    this.createdBy = data.createdBy || ''
    this.lastUsedAt = data.lastUsedAt || null
    this.lastUsedBy = data.lastUsedBy || null
    
    this.validate()
  }
  
  validate() {
    const errors = []

    if (!this.paymentId) {
      errors.push('Payment ID is required')
    }

    if (!this.organizationId) {
      errors.push('Organization ID is required')
    }

    if (!this.paymentReferenceToken) {
      errors.push('Payment reference token is required')
    }

    if (!this.amount || this.amount <= 0) {
      errors.push('Amount must be greater than 0')
    }

    if (errors.length > 0) {
      throw new Error(`Payment token validation failed: ${errors.join(', ')}`)
    }
  }
  
  /**
   * Check if token is expired
   * @returns {boolean}
   */
  isExpired() {
    if (!this.tokenExpiresAt) {
      return false
    }
    return new Date() > new Date(this.tokenExpiresAt)
  }
  
  /**
   * Check if token is valid for use
   * @returns {boolean}
   */
  isValidForUse() {
    return this.isTokenActive && 
           !this.tokenRevoked && 
           !this.isExpired() && 
           this.tokenUsageCount < this.maxTokenUsage
  }
  
  /**
   * Mark token as used
   */
  markAsUsed(userId = null) {
    this.tokenUsageCount++
    this.lastUsedAt = new Date().toISOString()
    if (userId) {
      this.lastUsedBy = userId
    }
    
    // Auto-revoke if max usage reached
    if (this.tokenUsageCount >= this.maxTokenUsage) {
      this.revokeToken('Max usage reached')
    }
  }
  
  /**
   * Revoke the token
   * @param {string} reason - Reason for revocation
   */
  revokeToken(reason = 'Manual revocation') {
    this.tokenRevoked = true
    this.isTokenActive = false
    this.revokedAt = new Date().toISOString()
    this.revokedReason = reason
  }
  
  /**
   * Extend token expiry
   * @param {number} additionalMinutes - Additional minutes to extend
   */
  extendExpiry(additionalMinutes = 30) {
    const currentExpiry = this.tokenExpiresAt ? new Date(this.tokenExpiresAt) : new Date()
    const newExpiry = new Date(currentExpiry.getTime() + (additionalMinutes * 60 * 1000))
    this.tokenExpiresAt = newExpiry.toISOString()
  }
  
  /**
   * Get token summary for logging (without sensitive data)
   * @returns {Object}
   */
  getTokenSummary() {
    return {
      id: this.id,
      paymentId: this.paymentId,
      organizationId: this.organizationId,
      amount: this.amount,
      currency: this.currency,
      paymentType: this.paymentType,
      status: this.status,
      tokenCreatedAt: this.tokenCreatedAt,
      tokenExpiresAt: this.tokenExpiresAt,
      tokenUsageCount: this.tokenUsageCount,
      maxTokenUsage: this.maxTokenUsage,
      isTokenActive: this.isTokenActive,
      tokenRevoked: this.tokenRevoked,
      isExpired: this.isExpired(),
      isValidForUse: this.isValidForUse()
    }
  }
}

/**
 * Payment Session Token Model
 * For temporary payment sessions
 */
class PaymentSessionTokenModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    
    this.id = data.id || uuidv4()
    this.sessionId = data.sessionId || uuidv4()
    this.paymentId = data.paymentId || ''
    this.userId = data.userId || ''
    this.organizationId = data.organizationId || ''
    
    this.sessionToken = data.sessionToken || ''
    this.sessionCreatedAt = data.sessionCreatedAt || new Date().toISOString()
    this.sessionExpiresAt = data.sessionExpiresAt || new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 min default
    
    this.isActive = data.isActive !== undefined ? data.isActive : true
    this.completedAt = data.completedAt || null
    this.failedAt = data.failedAt || null
    this.failureReason = data.failureReason || null
  }
  
  /**
   * Check if session is expired
   * @returns {boolean}
   */
  isExpired() {
    return new Date() > new Date(this.sessionExpiresAt)
  }
  
  /**
   * Mark session as completed
   */
  markCompleted() {
    this.isActive = false
    this.completedAt = new Date().toISOString()
  }
  
  /**
   * Mark session as failed
   * @param {string} reason - Failure reason
   */
  markFailed(reason) {
    this.isActive = false
    this.failedAt = new Date().toISOString()
    this.failureReason = reason
  }
}

module.exports = {
  PaymentTokenModel,
  PaymentSessionTokenModel
}
