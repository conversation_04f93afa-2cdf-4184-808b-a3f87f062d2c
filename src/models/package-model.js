const { PackageType } = require('../common/constant')
const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model')

class PackageModel extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || ''
    this.name = data.name || ''

    if (!Object.values(PackageType).includes(data.type)) {
      throw new Error(
        `Invalid package type. Allowed types are ${Object.values(
          PackageType,
        ).join(', ')}`,
      )
    }
    this.type = data.type || PackageType.DEPARTMENT
    this.medicines = data.medicines || []
    this.createdBy = data.createdBy || ''
    this.departmentName = data.departmentName || ''
    this.description = data.description || ''
    this.isActive = typeof data.isActive === 'boolean' ? data.isActive : true
  }

  // Calculate medicine count
  get medicineCount() {
    return this.medicines ? this.medicines.length : 0
  }

  addMedicineWithProtocol(medicine) {
    const medicineData = {
      id: medicine.id,
      productId: medicine.productId,
      medicineId: medicine.productId || medicine.id,
      medicineName: medicine.productName,
      brandName: medicine.productName,
      strength: medicine.qty,
      drugForm: medicine.productForm,
      genericName: medicine.saltComposition,
      manufacturer: medicine.marketerOrManufacturer,
      cost: medicine.mrp,
      addedAt: new Date().toISOString(),
      addedBy: this.updated_by || this.created_by,
      isActive: true
    }

    return medicineData
  }

}

module.exports = PackageModel
