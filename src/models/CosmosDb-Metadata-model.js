class CosmosDbMetadata {
    constructor(data) {
        this._rid = data._rid || "";
        this._self = data._self || "";
        this._etag = data._etag || "";
        this._attachments = data._attachments || "";
        this._ts = data._ts || 0;
        this.created_by = data.created_by || "";
        this.updated_by = data.updated_by || "";
        this.created_on = data.created_on || "";
        this.updated_on = data.updated_on || "";
    }
}

module.exports = CosmosDbMetadata