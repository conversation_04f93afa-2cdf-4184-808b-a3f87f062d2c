const { v4: uuidv4 } = require('uuid')
const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

// Valid feature types (modules)
const FEATURE_TYPES = {
  MRD: 'MRD',
  EMR: 'EMR',
  BILLING: 'Billing',
  AI: 'AI',
}

class Feature extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || uuidv4()
    this.featureName = data.featureName || ''
    this.description = data.description || ''
    this.type = data.type || '' // MRD, EMR, Billing, AI
    this.subType = data.subType || '' // Optional subtype for categorization (e.g., "Consultation", "Billing", "Ambient Listening")
    this.permissionKeys = data.permissionKeys || []
    this.isActive = data.isActive !== undefined ? data.isActive : true
  }

  validate() {
    const errors = []

    if (!this.featureName || this.featureName.trim() === '') {
      errors.push('Feature name is required')
    }

    // Validate type field
    if (!this.type || this.type.trim() === '') {
      errors.push('Feature type is required')
    } else {
      const validTypes = Object.values(FEATURE_TYPES)
      if (!validTypes.includes(this.type)) {
        errors.push(
          `Invalid feature type: "${
            this.type
          }". Must be one of: ${validTypes.join(', ')}`,
        )
      }
    }

    if (!Array.isArray(this.permissionKeys)) {
      errors.push('Permission keys must be an array')
    }

    // Validate permission key format (e.g., emr.payment.appointment_booking or emr.lab-test.view)
    if (this.permissionKeys.length > 0) {
      const permissionKeyPattern = /^[a-z]+\.[a-z_-]+(\.[a-z_-]+)*$/
      this.permissionKeys.forEach((key) => {
        if (!permissionKeyPattern.test(key)) {
          errors.push(
            `Invalid permission key format: "${key}". Expected format: module.category.action (e.g., emr.payment.appointment_booking or emr.lab-test.view)`,
          )
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  toJSON() {
    return {
      id: this.id,
      featureName: this.featureName,
      description: this.description,
      type: this.type,
      subType: this.subType,
      permissionKeys: this.permissionKeys,
      isActive: this.isActive,
      created_by: this.created_by,
      created_on: this.created_on,
      updated_on: this.updated_on,
      updated_by: this.updated_by,
    }
  }
}

module.exports = Feature
module.exports.FEATURE_TYPES = FEATURE_TYPES
