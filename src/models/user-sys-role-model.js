const { v4: uuidv4 } = require('uuid')
const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

class UserSysRole extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || uuidv4()
    this.userId = data.userId || ''
    this.subscriberId = data.subscriberId || ''
    this.organizationId = data.organizationId || ''
    this.subscriptionId = data.subscriptionId || ''
    this.planId = data.planId || ''
    this.planName = data.planName || ''
    
    // Features with their permission keys
    this.features = data.features || {
      MRD: [], // [{ featureId: "uuid", featureName: "Dashboard", permissionKeys: ["mrd.dashboard.view"] }]
      EMR: [],
      Billing: []
    }
    
    // Flattened permission keys for quick access
    this.permissionKeys = data.permissionKeys || []
    
    this.isActive = data.isActive !== undefined ? data.isActive : true
    this.subscriptionStatus = data.subscriptionStatus || 'active' // active, expired, cancelled
  }

  validate() {
    const errors = []

    if (!this.userId || this.userId.trim() === '') {
      errors.push('User ID is required')
    }

    if (!this.subscriberId || this.subscriberId.trim() === '') {
      errors.push('Subscriber ID is required')
    }

    if (!this.organizationId || this.organizationId.trim() === '') {
      errors.push('Organization ID is required')
    }

    if (!this.subscriptionId || this.subscriptionId.trim() === '') {
      errors.push('Subscription ID is required')
    }

    if (!this.planId || this.planId.trim() === '') {
      errors.push('Plan ID is required')
    }

    if (!Array.isArray(this.permissionKeys)) {
      errors.push('Permission keys must be an array')
    }

    // Validate features structure
    if (typeof this.features !== 'object' || this.features === null) {
      errors.push('Features must be an object')
    } else {
      const validModules = ['MRD', 'EMR', 'Billing']
      for (const module of validModules) {
        if (!Array.isArray(this.features[module])) {
          errors.push(`Features.${module} must be an array`)
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  // Generate flattened permission keys from features
  generatePermissionKeys() {
    const allPermissionKeys = []
    
    Object.values(this.features).forEach(moduleFeatures => {
      if (Array.isArray(moduleFeatures)) {
        moduleFeatures.forEach(feature => {
          if (feature.permissionKeys && Array.isArray(feature.permissionKeys)) {
            allPermissionKeys.push(...feature.permissionKeys)
          }
        })
      }
    })
    
    // Remove duplicates and sort
    this.permissionKeys = [...new Set(allPermissionKeys)].sort()
    return this.permissionKeys
  }

  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      subscriberId: this.subscriberId,
      organizationId: this.organizationId,
      subscriptionId: this.subscriptionId,
      planId: this.planId,
      planName: this.planName,
      features: this.features,
      permissionKeys: this.permissionKeys,
      isActive: this.isActive,
      subscriptionStatus: this.subscriptionStatus,
      created_by: this.created_by,
      created_on: this.created_on,
      updated_on: this.updated_on,
      updated_by: this.updated_by,
    }
  }
}

module.exports = UserSysRole
