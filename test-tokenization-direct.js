#!/usr/bin/env node

/**
 * Direct Tokenization Testing
 * Tests the payment tokenization service directly without API calls
 */

const paymentTokenizationService = require('./src/services/payment-tokenization-service')
const { PaymentTokenModel, PaymentSessionTokenModel } = require('./src/models/payment-token-model')

async function testTokenization() {
  console.log('🔐 Testing Payment Tokenization Service Directly')
  console.log('================================================')
  console.log('')

  try {
    // Initialize the service
    console.log('📋 Initializing tokenization service...')
    await paymentTokenizationService.initialize()
    console.log('✅ Service initialized successfully')
    console.log('')

    // Test 1: Basic Token Generation and Validation
    console.log('🔑 Test 1: Basic Token Generation and Validation')
    console.log('------------------------------------------------')
    
    const sensitiveData = 'pay_test_razorpay_payment_id_123456789'
    console.log(`Original data: ${sensitiveData}`)
    
    const token = await paymentTokenizationService.generateToken(sensitiveData)
    console.log(`Generated token: ${token}`)
    
    const isValid = await paymentTokenizationService.validateToken(token, sensitiveData)
    console.log(`Token validation result: ${isValid ? '✅ Valid' : '❌ Invalid'}`)
    console.log('')

    // Test 2: Payment Data Tokenization
    console.log('💳 Test 2: Payment Data Tokenization')
    console.log('------------------------------------')
    
    const paymentData = {
      id: 'payment_test_123',
      amount: 50000,
      currency: 'INR',
      razorpayPaymentId: 'pay_sensitive_razorpay_id_123',
      razorpaySignature: 'signature_sensitive_hash_456',
      cardNumber: '****************',
      cardHolderName: 'John Doe',
      cvv: '123',
      organizationId: 'org_test_789',
      status: 'created'
    }
    
    console.log('Original payment data:')
    console.log(JSON.stringify(paymentData, null, 2))
    console.log('')
    
    const tokenizedData = await paymentTokenizationService.tokenizePaymentData(paymentData)
    console.log('Tokenized payment data:')
    console.log(JSON.stringify(tokenizedData, null, 2))
    console.log('')

    // Test 3: Payment Reference Token
    console.log('🎫 Test 3: Payment Reference Token')
    console.log('----------------------------------')
    
    const paymentReference = {
      paymentId: 'payment_test_123',
      organizationId: 'org_test_789',
      amount: 50000,
      timestamp: Date.now()
    }
    
    console.log('Payment reference:')
    console.log(JSON.stringify(paymentReference, null, 2))
    
    const referenceToken = await paymentTokenizationService.createPaymentReferenceToken(paymentReference)
    console.log(`Generated reference token: ${referenceToken}`)
    
    const isReferenceValid = await paymentTokenizationService.validatePaymentReferenceToken(referenceToken, paymentReference)
    console.log(`Reference token validation: ${isReferenceValid ? '✅ Valid' : '❌ Invalid'}`)
    console.log('')

    // Test 4: Session Token
    console.log('🎪 Test 4: Session Token Generation')
    console.log('-----------------------------------')
    
    const sessionData = {
      paymentId: 'payment_test_123',
      userId: 'user_test_456',
      organizationId: 'org_test_789',
      sessionType: 'payment_flow'
    }
    
    console.log('Session data:')
    console.log(JSON.stringify(sessionData, null, 2))
    
    const sessionToken = await paymentTokenizationService.generatePaymentSessionToken(sessionData)
    console.log(`Generated session token: ${sessionToken}`)
    console.log('')

    // Test 5: Payment Token Model
    console.log('📊 Test 5: Payment Token Model')
    console.log('------------------------------')
    
    const tokenModelData = {
      paymentId: 'payment_test_123',
      organizationId: 'org_test_789',
      paymentReferenceToken: referenceToken,
      amount: 50000,
      currency: 'INR',
      paymentType: 'consultation',
      status: 'created'
    }
    
    const paymentTokenModel = new PaymentTokenModel(tokenModelData)
    console.log('Payment Token Model created:')
    console.log(`- ID: ${paymentTokenModel.id}`)
    console.log(`- Payment ID: ${paymentTokenModel.paymentId}`)
    console.log(`- Organization ID: ${paymentTokenModel.organizationId}`)
    console.log(`- Amount: ${paymentTokenModel.amount}`)
    console.log(`- Is Valid for Use: ${paymentTokenModel.isValidForUse()}`)
    console.log(`- Is Expired: ${paymentTokenModel.isExpired()}`)
    console.log(`- Usage Count: ${paymentTokenModel.tokenUsageCount}`)
    console.log('')

    // Test 6: Session Token Model
    console.log('🎭 Test 6: Session Token Model')
    console.log('------------------------------')
    
    const sessionModelData = {
      paymentId: 'payment_test_123',
      userId: 'user_test_456',
      organizationId: 'org_test_789',
      sessionToken: sessionToken
    }
    
    const sessionTokenModel = new PaymentSessionTokenModel(sessionModelData)
    console.log('Session Token Model created:')
    console.log(`- ID: ${sessionTokenModel.id}`)
    console.log(`- Session ID: ${sessionTokenModel.sessionId}`)
    console.log(`- Payment ID: ${sessionTokenModel.paymentId}`)
    console.log(`- User ID: ${sessionTokenModel.userId}`)
    console.log(`- Is Active: ${sessionTokenModel.isActive}`)
    console.log(`- Is Expired: ${sessionTokenModel.isExpired()}`)
    console.log('')

    // Test 7: Token Usage and Expiration
    console.log('⏰ Test 7: Token Usage and Expiration')
    console.log('-------------------------------------')
    
    console.log('Testing token usage...')
    paymentTokenModel.markAsUsed('user_test_456')
    console.log(`After first use - Usage Count: ${paymentTokenModel.tokenUsageCount}`)
    console.log(`Still valid for use: ${paymentTokenModel.isValidForUse()}`)
    
    // Mark as used multiple times to test limits
    for (let i = 0; i < 5; i++) {
      paymentTokenModel.markAsUsed(`user_test_${i}`)
    }
    console.log(`After multiple uses - Usage Count: ${paymentTokenModel.tokenUsageCount}`)
    console.log(`Still valid for use: ${paymentTokenModel.isValidForUse()}`)
    console.log(`Token revoked: ${paymentTokenModel.tokenRevoked}`)
    console.log('')

    // Test 8: Session Completion
    console.log('🏁 Test 8: Session Completion')
    console.log('-----------------------------')
    
    console.log(`Session active before completion: ${sessionTokenModel.isActive}`)
    sessionTokenModel.markCompleted()
    console.log(`Session active after completion: ${sessionTokenModel.isActive}`)
    console.log(`Completion time: ${sessionTokenModel.completedAt}`)
    console.log('')

    console.log('🎉 All Tokenization Tests Completed Successfully!')
    console.log('================================================')
    console.log('')
    console.log('Summary:')
    console.log('✅ Basic token generation and validation')
    console.log('✅ Payment data tokenization')
    console.log('✅ Payment reference token creation and validation')
    console.log('✅ Session token generation')
    console.log('✅ Payment token model functionality')
    console.log('✅ Session token model functionality')
    console.log('✅ Token usage tracking and limits')
    console.log('✅ Session lifecycle management')
    console.log('')
    console.log('🔒 Security Features Verified:')
    console.log('- Sensitive data is properly tokenized')
    console.log('- Tokens have proper expiration')
    console.log('- Usage limits are enforced')
    console.log('- Session management works correctly')
    console.log('- Token validation is secure')

  } catch (error) {
    console.error('❌ Test failed:', error)
    console.error('Stack trace:', error.stack)
  }
}

// Run the tests
if (require.main === module) {
  testTokenization().catch(error => {
    console.error('Test execution failed:', error)
    process.exit(1)
  })
}

module.exports = testTokenization
